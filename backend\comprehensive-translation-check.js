/**
 * 全面检查翻译系统合并状态
 */

require('dotenv').config();

async function comprehensiveTranslationCheck() {
    console.log('🔍 全面检查翻译系统合并状态...\n');

    const results = {
        systemMerged: false,
        functionalityPreserved: false,
        issues: [],
        recommendations: []
    };

    try {
        // 1. 检查核心翻译引擎是否使用旧系统逻辑
        console.log('1. 检查核心翻译引擎合并状态...');
        const fs = require('fs');
        const path = require('path');
        
        const enginePath = path.join(__dirname, 'src', 'services', 'core', 'TranslationEngines.js');
        const engineContent = fs.readFileSync(enginePath, 'utf8');
        
        const usesOldSystem = engineContent.includes("require('../translationService')");
        const hasDirectApiCalls = engineContent.includes('translateWithBaidu') || engineContent.includes('translateWithDeepseek');
        
        if (usesOldSystem && !hasDirectApiCalls) {
            console.log('   ✅ 核心翻译引擎已合并，使用旧系统逻辑');
            results.systemMerged = true;
        } else if (hasDirectApiCalls) {
            console.log('   ❌ 核心翻译引擎仍有直接API调用');
            results.issues.push('核心翻译引擎未完全合并');
        } else {
            console.log('   ⚠️ 核心翻译引擎状态不明确');
            results.issues.push('核心翻译引擎状态需要确认');
        }

        // 2. 检查上架翻译是否统一
        console.log('\n2. 检查上架翻译统一性...');
        const carControllerPath = path.join(__dirname, 'src', 'api', 'v1', 'controllers', 'carController.js');
        const carControllerContent = fs.readFileSync(carControllerPath, 'utf8');
        
        const hasEnvSelection = carControllerContent.includes('USE_NEW_TRANSLATION_SYSTEM');
        const usesUnifiedCall = carControllerContent.includes('handleProductOnShelf(product.productId)');
        
        if (usesUnifiedCall && !hasEnvSelection) {
            console.log('   ✅ 上架翻译已统一，无环境变量选择');
        } else if (hasEnvSelection) {
            console.log('   ❌ 仍存在环境变量选择逻辑');
            results.issues.push('上架翻译仍有环境变量选择');
        } else {
            console.log('   ⚠️ 上架翻译状态需要确认');
        }

        // 3. 检查定时任务是否统一
        console.log('\n3. 检查定时任务统一性...');
        const scheduledTasksPath = path.join(__dirname, 'src', 'services', 'scheduledTasksService.js');
        const scheduledTasksContent = fs.readFileSync(scheduledTasksPath, 'utf8');
        
        const taskHasEnvSelection = scheduledTasksContent.includes('USE_NEW_TRANSLATION_SYSTEM');
        const taskUsesNewSystem = scheduledTasksContent.includes('newScheduledTasksService.autoTranslateProductsTask');
        
        if (taskUsesNewSystem && !taskHasEnvSelection) {
            console.log('   ✅ 定时任务已统一使用新系统');
        } else if (taskHasEnvSelection) {
            console.log('   ❌ 定时任务仍有环境变量选择');
            results.issues.push('定时任务仍有环境变量选择');
        } else {
            console.log('   ⚠️ 定时任务状态需要确认');
        }

        // 4. 测试翻译功能
        console.log('\n4. 测试翻译功能完整性...');
        
        // 测试旧翻译系统API
        const translationService = require('./src/services/translationService');
        const oldApiResult = await translationService.translate('测试', 'zh', 'en', { productId: 'test' });
        
        if (oldApiResult.success) {
            console.log(`   ✅ 旧翻译API正常: ${oldApiResult.engine}`);
            results.functionalityPreserved = true;
        } else {
            console.log(`   ❌ 旧翻译API失败: ${oldApiResult.error}`);
            results.issues.push('旧翻译API不可用');
        }

        // 测试核心翻译引擎
        const TranslationEngines = require('./src/services/core/TranslationEngines');
        const logger = { warn: console.warn, error: console.error, info: console.info, debug: () => {} };
        const engines = new TranslationEngines(logger);
        
        const coreResult = await engines.executeTranslation('baidu', '测试', 'zh', 'en');
        
        if (coreResult.success) {
            console.log(`   ✅ 核心翻译引擎正常: ${coreResult.engine || 'unknown'}`);
        } else {
            console.log(`   ❌ 核心翻译引擎失败: ${coreResult.error}`);
            results.issues.push('核心翻译引擎不可用');
        }

        // 5. 检查是否还有多套系统
        console.log('\n5. 检查系统架构...');
        
        const hasOldSystem = fs.existsSync(path.join(__dirname, 'src', 'services', 'translationService.js'));
        const hasNewSystem = fs.existsSync(path.join(__dirname, 'src', 'services', 'core', 'TranslationManager.js'));
        const hasAdapter = fs.existsSync(path.join(__dirname, 'src', 'services', 'translationMigrationAdapter.js'));
        
        console.log(`   旧翻译系统: ${hasOldSystem ? '存在' : '不存在'}`);
        console.log(`   新翻译系统: ${hasNewSystem ? '存在' : '不存在'}`);
        console.log(`   迁移适配器: ${hasAdapter ? '存在' : '不存在'}`);
        
        if (hasOldSystem && hasNewSystem) {
            if (usesOldSystem) {
                console.log('   ✅ 两套系统已合并：新系统内部调用旧系统');
            } else {
                console.log('   ⚠️ 两套系统并存但未完全合并');
                results.issues.push('系统未完全合并');
            }
        }

        // 6. 生成总结报告
        console.log('\n📊 合并状态总结:');
        console.log('==================');
        
        if (results.systemMerged && results.functionalityPreserved && results.issues.length === 0) {
            console.log('🎉 翻译系统已完全合并！');
            console.log('✅ 核心翻译引擎使用统一逻辑');
            console.log('✅ 上架翻译统一调用');
            console.log('✅ 翻译功能完全保持');
            console.log('✅ 无环境变量选择逻辑');
            
            console.log('\n🏗️ 当前架构:');
            console.log('- 统一翻译引擎: translationService.js');
            console.log('- 队列管理: core/TranslationManager.js');
            console.log('- 调度器: core/TranslationScheduler.js');
            console.log('- 监控: core/TranslationMonitor.js');
            
        } else {
            console.log('⚠️ 翻译系统合并不完整');
            
            if (results.issues.length > 0) {
                console.log('\n❌ 发现的问题:');
                results.issues.forEach(issue => console.log(`   - ${issue}`));
            }
            
            console.log('\n💡 建议:');
            if (!results.systemMerged) {
                console.log('   - 完成核心翻译引擎的合并');
            }
            if (!results.functionalityPreserved) {
                console.log('   - 修复翻译功能问题');
            }
            if (results.issues.includes('上架翻译仍有环境变量选择')) {
                console.log('   - 移除上架翻译的环境变量选择逻辑');
            }
            if (results.issues.includes('定时任务仍有环境变量选择')) {
                console.log('   - 移除定时任务的环境变量选择逻辑');
            }
        }

        return results;

    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        return { ...results, error: error.message };
    }
}

// 运行检查
comprehensiveTranslationCheck().catch(console.error);
