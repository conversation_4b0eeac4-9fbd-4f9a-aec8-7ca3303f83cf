/**
 * 新的定时任务服务
 * 使用优化后的翻译系统
 */

const cron = require('node-cron');
const translationManager = require('./core/TranslationManager');
const { SCHEDULE_CONFIG } = require('../config/translationConfig');
const { Product, ConfigCar } = require('../models');
const { Op } = require('sequelize');

/**
 * 日志记录函数
 */
const logTask = (taskName, message, isError = false) => {
  const timestamp = new Date().toISOString();
  const level = isError ? 'ERROR' : 'INFO';
  console.log(`[${timestamp}] [${level}] [定时任务:${taskName}] ${message}`);
};

/**
 * 自动翻译产品任务
 */
const autoTranslateProductsTask = async () => {
  const taskName = '自动翻译产品';
  
  try {
    logTask(taskName, '开始执行');
    
    if (!SCHEDULE_CONFIG.autoTranslation.enabled) {
      logTask(taskName, '自动翻译功能已禁用');
      return { success: true, message: '自动翻译功能已禁用' };
    }

    // 确保翻译管理器已启动
    if (!translationManager.isRunning) {
      await translationManager.start();
    }

    // 查找需要翻译的产品
    const products = await Product.findAll({
      where: {
        [Op.or]: [
          { test_en: false },
          { test_ru: false },
          { test_es: false },
          { test_fr: false },
          { test_all: false }
        ]
      },
      limit: SCHEDULE_CONFIG.autoTranslation.batchSize,
      order: [['updatedAt', 'ASC']]
    });

    if (products.length === 0) {
      logTask(taskName, '没有需要翻译的产品');
      return { success: true, processed: 0, message: '没有需要翻译的产品' };
    }

    logTask(taskName, `找到 ${products.length} 个需要翻译的产品`);

    // 使用和上架翻译完全一模一样的流程
    const { handleProductOnShelf } = require('./autoTranslationService');
    const productIds = products.map(p => p.productId);

    let successful = 0;
    let failed = 0;
    let scheduled = 0;

    console.log(`[自动翻译] 开始处理 ${productIds.length} 个产品，使用和上架翻译一模一样的流程`);

    // 逐个处理产品，使用和上架翻译完全相同的调用方式
    for (const productId of productIds) {
      try {
        // 完全按照上架翻译的调用方式：handleProductOnShelf(productId)
        // 不传递任何额外参数，使用默认的 priority='high', useQueue=true
        const result = await handleProductOnShelf(productId);

        if (result.success) {
          if (result.action === 'scheduled') {
            scheduled++;
            console.log(`[自动翻译] 产品 ${productId} 已加入翻译队列: ${result.taskId} (优先级: ${result.priority})`);
          } else {
            successful++;
            console.log(`[自动翻译] 产品 ${productId} 翻译完成`);
          }
        } else {
          failed++;
          console.error(`[自动翻译] 产品 ${productId} 处理失败:`, result.error);
        }
      } catch (error) {
        failed++;
        console.error(`[自动翻译] 产品 ${productId} 处理异常:`, error.message);
      }
    }

    const result = {
      total: productIds.length,
      successful,
      failed,
      scheduled
    };

    logTask(taskName, `处理完成: 总计 ${result.total}, 成功 ${result.successful}, 失败 ${result.failed}, 已调度 ${result.scheduled}`);

    return {
      success: true,
      processed: result.total,
      successful: result.successful,
      failed: result.failed,
      scheduled: result.scheduled,
      message: `自动翻译任务完成: 处理 ${result.total} 个产品，已调度 ${result.scheduled} 个到翻译队列`
    };

  } catch (error) {
    logTask(taskName, `执行失败: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 自动翻译配置车任务
 */
const autoTranslateConfigCarsTask = async () => {
  const taskName = '自动翻译配置车';
  
  try {
    logTask(taskName, '开始执行');
    
    if (!SCHEDULE_CONFIG.autoTranslation.enabled) {
      logTask(taskName, '自动翻译功能已禁用');
      return { success: true, message: '自动翻译功能已禁用' };
    }

    // 确保翻译管理器已启动
    if (!translationManager.isRunning) {
      await translationManager.start();
    }

    // 查找需要翻译的配置车
    const configCars = await ConfigCar.findAll({
      where: {
        [Op.or]: [
          { peizhi_en: false },
          { peizhi_ru: false },
          { peizhi_es: false },
          { peizhi_fr: false },
          { peizhi_all: false }
        ]
      },
      limit: SCHEDULE_CONFIG.autoTranslation.batchSize,
      order: [['updatedAt', 'ASC']]
    });

    if (configCars.length === 0) {
      logTask(taskName, '没有需要翻译的配置车');
      return { success: true, processed: 0, message: '没有需要翻译的配置车' };
    }

    logTask(taskName, `找到 ${configCars.length} 个需要翻译的配置车`);

    // 批量处理配置车翻译
    const results = [];
    for (const configCar of configCars) {
      try {
        const result = await translationManager.handleConfigCarTranslation(configCar.id, {
          useQueue: true,
          priority: 'normal'
        });
        
        results.push({
          id: configCar.id,
          success: result.success,
          result
        });
        
      } catch (error) {
        results.push({
          id: configCar.id,
          success: false,
          error: error.message
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;

    logTask(taskName, `处理完成: 总计 ${results.length}, 成功 ${successful}, 失败 ${failed}`);

    return {
      success: true,
      processed: results.length,
      successful,
      failed,
      results,
      message: `自动翻译配置车任务完成: 处理 ${results.length} 个配置车`
    };

  } catch (error) {
    logTask(taskName, `执行失败: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 清理过期翻译数据任务
 */
const cleanupExpiredTranslationDataTask = async () => {
  const taskName = '清理过期翻译数据';
  
  try {
    logTask(taskName, '开始执行');
    
    if (!SCHEDULE_CONFIG.cleanup.enabled) {
      logTask(taskName, '清理任务已禁用');
      return { success: true, message: '清理任务已禁用' };
    }

    const retentionDays = SCHEDULE_CONFIG.cleanup.retentionDays;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    // 清理翻译日志
    if (translationManager.logger) {
      await translationManager.logger.cleanupOldLogs();
    }

    // 清理监控数据
    if (translationManager.monitor) {
      translationManager.monitor.cleanupExpiredData();
    }

    logTask(taskName, `清理完成: 删除 ${retentionDays} 天前的数据`);

    return {
      success: true,
      retentionDays,
      cutoffDate,
      message: `清理任务完成: 删除 ${retentionDays} 天前的数据`
    };

  } catch (error) {
    logTask(taskName, `执行失败: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 翻译系统健康检查任务
 */
const translationHealthCheckTask = async () => {
  const taskName = '翻译系统健康检查';
  
  try {
    logTask(taskName, '开始执行');

    // 检查翻译管理器状态
    const health = translationManager.getHealthStatus();
    
    if (health.status !== 'running') {
      logTask(taskName, `翻译管理器状态异常: ${health.status}`, true);
      
      // 尝试重启翻译管理器
      try {
        await translationManager.start();
        logTask(taskName, '翻译管理器已重启');
      } catch (restartError) {
        logTask(taskName, `重启翻译管理器失败: ${restartError.message}`, true);
      }
    }

    // 检查队列状态
    if (health.queue && health.queue.processingTasks > health.queue.maxConcurrentTasks) {
      logTask(taskName, `队列处理任务数异常: ${health.queue.processingTasks}`, true);
    }

    // 检查引擎状态
    if (health.engines && health.engines.availableEngines < 1) {
      logTask(taskName, '没有可用的翻译引擎', true);
    }

    logTask(taskName, `健康检查完成: 状态 ${health.status}, 队列大小 ${health.queue?.currentQueueSize || 0}`);

    return {
      success: true,
      health,
      message: '健康检查完成'
    };

  } catch (error) {
    logTask(taskName, `执行失败: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 初始化定时任务
 */
const initScheduledTasks = () => {
  try {
    // 自动翻译产品任务
    if (SCHEDULE_CONFIG.autoTranslation.enabled) {
      cron.schedule(SCHEDULE_CONFIG.autoTranslation.cron, async () => {
        await autoTranslateProductsTask();
      });
      logTask('初始化', `自动翻译产品任务已设置: ${SCHEDULE_CONFIG.autoTranslation.cron}`);
    }

    // 清理任务
    if (SCHEDULE_CONFIG.cleanup.enabled) {
      cron.schedule(SCHEDULE_CONFIG.cleanup.cron, async () => {
        await cleanupExpiredTranslationDataTask();
      });
      logTask('初始化', `清理任务已设置: ${SCHEDULE_CONFIG.cleanup.cron}`);
    }

    // 健康检查任务已删除

    // 配置车翻译任务已删除

    logTask('初始化', '所有定时任务已初始化完成');

  } catch (error) {
    logTask('初始化', `初始化定时任务失败: ${error.message}`, true);
  }
};

/**
 * 获取任务统计信息
 */
const getTaskStats = () => {
  return {
    autoTranslation: {
      enabled: SCHEDULE_CONFIG.autoTranslation.enabled,
      cron: SCHEDULE_CONFIG.autoTranslation.cron,
      batchSize: SCHEDULE_CONFIG.autoTranslation.batchSize
    },
    cleanup: {
      enabled: SCHEDULE_CONFIG.cleanup.enabled,
      cron: SCHEDULE_CONFIG.cleanup.cron,
      retentionDays: SCHEDULE_CONFIG.cleanup.retentionDays
    },
    syncCheck: {
      enabled: SCHEDULE_CONFIG.syncCheck.enabled,
      cron: SCHEDULE_CONFIG.syncCheck.cron,
      batchSize: SCHEDULE_CONFIG.syncCheck.batchSize
    },
    translationManager: translationManager.getStats()
  };
};

module.exports = {
  autoTranslateProductsTask,
  cleanupExpiredTranslationDataTask,
  initScheduledTasks,
  getTaskStats
};
