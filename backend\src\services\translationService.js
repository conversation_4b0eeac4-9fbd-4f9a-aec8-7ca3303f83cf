const axios = require('axios');
// 引入所有需要的模型
const db = require('../models');
const { Product, ProductEn, ProductRu, ProductEs, ProductFr, ConfigCar, ConfigCarEn, ConfigCarRu, ConfigCarEs, ConfigCarFr, ProductBikeSnapshot } = db;
const { setCache, getCache, deleteCache } = require('./cacheService');
const { translateWithBaidu, translateToMultipleLanguagesWithBaidu } = require('./baiduTranslationService');
const { translateWithDeepseek, translateToMultipleLanguagesWithDeepseek } = require('./deepseekTranslationService');
const { carModelTranslationMap, getCarModelEnglishName } = require('./carModelTranslationMap'); // 引入车型翻译映射
const { Op, Sequelize, Transaction } = require('sequelize');
require('dotenv').config();

// 获取sequelize实例用于事务管理
const sequelize = db.sequelize;
const TranslationStatusService = require('./translationStatusService');
const translationMonitoringService = require('./translationMonitoringService');
const logger = require('../utils/logger');

// 引入新翻译系统
const translationManager = require('./core/TranslationManager');



// --- 新增：品牌中英文对照表 ---
const brandTranslationMap = new Map([
  ['比亚迪', 'BYD'], ['吉利', 'Geely'], ['长城', 'GWM'], ['奇瑞', 'Chery'],
  ['蔚来', 'NIO'], ['小鹏', 'XPeng'], ['理想', 'Li Auto'], ['哈弗', 'HAVAL'],
  ['领克', 'Lynk & Co'], ['极氪', 'Zeekr'], ['红旗', 'Hongqi'], ['五菱', 'Wuling'],
  ['荣威', 'Roewe'], ['名爵', 'MG'], ['长安', 'Changan'], ['东风', 'Dongfeng'],
  ['广汽传祺', 'GAC Trumpchi'], ['北汽新能源', 'BAIC BJEV'], ['腾势', 'DENZA'],
  ['极狐', 'Arcfox'], ['问界', 'AITO'], ['零跑', 'Leapmotor'], ['哪吒', 'NETA'],
  ['鸿蒙智行', 'Huawei'],
  ['欧拉', 'ORA'], ['岚图', 'VOYAH'], ['宝骏', 'Baojun'], ['启辰', 'Venucia'],
  ['思皓', 'SOL'], ['江淮', 'JAC'], ['东风风行', 'Dongfeng Fengxing'],
  ['奔腾', 'Bestune'], ['大通', 'Maxus'], ['凯翼', 'Cowin'], ['捷途', 'Jetour'],
  ['星途', 'Exeed'], ['欧尚', 'Oshan'], ['比速', 'Bisu'], ['华骐', 'Horki'],
  ['国机智骏', 'GAC NE'], ['云度', 'Yudo'], ['合创', 'Hycan'], ['枫叶', 'Maple'],
  ['凌宝', 'Lingbox'], ['开沃', 'Skywell'], ['知豆', 'Zhidou'], ['雷丁', 'Letin'],
  ['汉腾', 'Hanteng'], ['斯威汽车', 'SWM'], ['爱驰', 'Aiways'], ['恒驰', 'Hengchi'],
  ['威马', 'Weltmeister'], ['天际', 'ENOVATE'], ['华人运通', 'Human Horizons'],
  ['赛力斯', 'Seres'],['埃安', 'AION'],
  // 新增国际品牌
  ['大众', 'Volkswagen'], ['丰田', 'Toyota'], ['本田', 'Honda'], ['日产', 'Nissan'],
  ['马自达', 'Mazda'], ['三菱', 'Mitsubishi'], ['斯巴鲁', 'Subaru'], ['铃木', 'Suzuki'],
  ['雷克萨斯', 'Lexus'], ['英菲尼迪', 'Infiniti'], ['讴歌', 'Acura'], ['奔驰', 'Mercedes-Benz'],
  ['宝马', 'BMW'], ['奥迪', 'Audi'], ['保时捷', 'Porsche'], ['宾利', 'Bentley'],
  ['兰博基尼', 'Lamborghini'], ['法拉利', 'Ferrari'], ['玛莎拉蒂', 'Maserati'], ['阿斯顿马丁', 'Aston Martin'],
  ['捷豹', 'Jaguar'], ['路虎', 'Land Rover'], ['沃尔沃', 'Volvo'], ['凯迪拉克', 'Cadillac'],
  ['布加迪', 'Bugatti'], ['迈巴赫', 'Maybach'], ['雪佛兰', 'Chevrolet'], ['福特', 'Ford'],
  ['菲亚特', 'Fiat'], ['标致', 'Peugeot'], ['雪铁龙', 'Citroën'], ['雷诺', 'Renault'],
  ['特斯拉', 'Tesla'], ['现代', 'Hyundai'], ['起亚', 'Kia'], ['斯柯达', 'Škoda'],
  ['吉普', 'Jeep'], ['克莱斯勒', 'Chrysler'], ['道奇', 'Dodge'], ['林肯', 'Lincoln'],
  ['捷达', 'Jetta'], ['迈凯伦', 'McLaren'], ['mini', 'MINI'], ['GMC', 'GMC'],
  // 更多中国品牌
  ['上汽', 'SAIC'], ['一汽', 'FAW'], ['广汽', 'GAC'], ['北汽', 'BAIC'],
  ['东南', 'Soueast'], ['华晨', 'Brilliance'], ['陆风', 'Landwind'], ['猎豹', 'Leopaard'],
  ['力帆', 'Lifan'], ['众泰', 'Zotye'], ['宝沃', 'Borgward'], ['观致', 'Qoros'],
  ['长城坦克', 'Tank'], ['魏牌', 'WEY'], ['金杯', 'Jinbei'],
  ['五十铃', 'Isuzu'], ['吉利几何', 'Geometry'], ['长安欧尚', 'Oshan']
]);

// --- 新增：查找品牌英文名的辅助函数 ---
const getBrandEnglishName = (chineseBrand) => {
  if (!chineseBrand || typeof chineseBrand !== 'string') {
    return null;
  }
  let normalizedBrand = chineseBrand.trim();
  // 处理 "xx汽车" 的情况
  if (normalizedBrand.endsWith('汽车')) {
    normalizedBrand = normalizedBrand.substring(0, normalizedBrand.length - 2).trim();
  }
  return brandTranslationMap.get(normalizedBrand) || null;
};

/**
 * 支持的语言代码及其对应模型
 */
const LANGUAGES = {
  EN: 'en', RU: 'ru', ES: 'es', FR: 'fr'
};

// 使用db中的模型映射
const PRODUCT_MODELS = {
  en: ProductEn,
  ru: ProductRu,
  es: ProductEs,
  fr: ProductFr
};

const CONFIG_CAR_MODELS = {
  en: ConfigCarEn,
  ru: ConfigCarRu,
  es: ConfigCarEs,
  fr: ConfigCarFr
};

/**
 * 检查语言代码是否受支持 ? * @param {string} lang - 语言代码
 * @returns {boolean} - 是否支持
 */
const isSupportedLanguage = (lang) => {
  return Object.values(LANGUAGES).includes(lang);
};

/**
 * 配置的翻译服务提供商，默认为百度翻译
 */
const TRANSLATION_SERVICE = process.env.TRANSLATION_SERVICE || 'baidu';



/**
 * 通用翻译函数，根据配置调用对应的翻译API - 新的错误处理逻辑
 * @param {string} text - 待翻译文本
 * @param {string} from - 源语言，默认为中文
 * @param {string} to - 目标语言，默认为英语
 * @param {Object} options - 翻译选项
 * @returns {Promise<Object>} - 翻译结果对象
 */
const translate = async (text, from = 'zh', to = 'en', options = {}) => {
  const productId = options.productId || 'unknown';

  // 确保所有语言被平等对待，不存在特殊处理
  if (!isSupportedLanguage(to)) {
    return {
      success: false,
      error: `不支持的目标语言: ${to}`,
      translatedText: `[${to.toUpperCase()}] ${text}`
    };
  }

  // 获取引擎优先级列表
  const engines = ['baidu', 'deepseek'];
  const primaryEngine = TRANSLATION_SERVICE || 'baidu';

  // 简化引擎选择：优先使用配置的主引擎，然后是备用引擎
  const orderedEngines = primaryEngine === 'baidu' ? ['baidu', 'deepseek'] : ['deepseek', 'baidu'];

  // 严格的错误处理逻辑：主引擎重试1次，备用引擎失败立即停止
  for (let engineIndex = 0; engineIndex < orderedEngines.length; engineIndex++) {
    const engineName = orderedEngines[engineIndex];



    // 主引擎可以重试1次，备用引擎失败立即停止
    const maxRetries = engineIndex === 0 ? 2 : 1; // 主引擎2次（初次+重试），备用引擎1次
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 简化日志显示 - 只在第一个字段翻译时显示
        const isFirstField = options.isFirstField || false;
        if (isFirstField && attempt === 1 && engineIndex === 0) {
          console.log(`产品 ${productId} 开始翻译 (${from} -> ${to})`);
        } else if (isFirstField && attempt === 1 && engineIndex > 0) {
          console.log(`产品 ${productId} 切换到备用 ${engineName} 翻译`);
        }

        let result;
        if (engineName === 'deepseek') {
          result = await translateWithDeepseek(text, from, to);
        } else {
          result = await translateWithBaidu(text, from, to);
        }

        if (result.success) {
          if (isFirstField) {
            console.log(`产品 ${productId} 翻译成功`);
          }
          // 添加引擎信息到结果中
          return {
            ...result,
            engine: engineName
          };
        } else {
          lastError = new Error(result.error);

          // 只在第一个字段翻译时显示详细错误信息
          if (isFirstField) {
            console.error(`产品 ${productId} ${engineName} 翻译引擎失败: ${result.error}`);
          }

          // 记录引擎失败状态
          translationMonitoringService.updateEngineStatus(engineName, false, result.error);

          // 如果是备用引擎失败，立即停止翻译系统
          if (engineIndex > 0) {
            if (isFirstField) {
              console.error(`产品 ${productId} 备用引擎 ${engineName} 翻译失败，停止翻译系统`);
            }
            await stopTranslationSystem();
            throw new Error('TRANSLATION_SYSTEM_STOPPED');
          } else {
            // 主引擎失败，准备重试或切换
            if (attempt < maxRetries) {
              if (isFirstField) {
                console.warn(`产品 ${productId} ${engineName} 翻译失败，准备重试 (${attempt + 1}/${maxRetries})`);
              }
            } else {
              if (isFirstField) {
                console.warn(`产品 ${productId} ${engineName} 翻译失败，已重试 ${maxRetries} 次，准备切换引擎`);
              }
            }
          }
        }

      } catch (error) {
        lastError = error;

        // 只在第一个字段翻译时显示详细异常信息
        if (isFirstField) {
          console.error(`产品 ${productId} ${engineName} 翻译引擎异常: ${error.message}`);
        }

        // 记录引擎异常状态
        translationMonitoringService.updateEngineStatus(engineName, false, error.message);

        // 如果是备用引擎失败，立即停止翻译系统
        if (engineIndex > 0) {
          if (isFirstField) {
            console.error(`产品 ${productId} 备用引擎 ${engineName} 异常，停止翻译系统`);
          }
          await stopTranslationSystem();
          throw new Error('TRANSLATION_SYSTEM_STOPPED');
        } else {
          // 主引擎异常，准备重试或切换
          if (attempt < maxRetries) {
            if (isFirstField) {
              console.warn(`产品 ${productId} ${engineName} 翻译异常，准备重试 (${attempt + 1}/${maxRetries})`);
            }
          } else {
            if (isFirstField) {
              console.warn(`产品 ${productId} ${engineName} 翻译异常，已重试 ${maxRetries} 次，准备切换引擎`);
            }
          }
        }
      }

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const delay = 1000; // 固定1秒延迟
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 所有引擎都失败了
  console.error(`产品 ${productId} 翻译失败，取消翻译`);

  return {
    success: false,
    error: '所有翻译引擎都不可用',
    translatedText: `[${to.toUpperCase()}] ${text}`
  };
};

/**
 * 停止翻译系统
 */
const stopTranslationSystem = async () => {
  try {
    console.error('翻译系统遇到严重错误，正在停止...');

    // 设置系统停止标志
    global.translationSystemStopped = true;
    global.translationSystemStoppedAt = new Date();

    // 通知翻译管理器停止
    const translationManager = require('./core/TranslationManager');
    if (translationManager) {
      await translationManager.stop();
    }

    console.error('翻译系统已停止，队列中的车辆将保留等待重启');
  } catch (error) {
    console.error('停止翻译系统时出错:', error);
  }
};



/**
 * 通用多语言翻译函数，根据配置调用对应的翻译API
 * @param {string} text - 待翻译文本
 * @param {Array<string>} targetLangs - 目标语言列表，默认为[en, ru, fr, es]
 * @param {string} fromLang - 源语言，默认为zh
 * @returns {Promise<Object>} - 包含各语言翻译结果的对象
 */
const translateToMultipleLanguages = async (text, targetLangs = ['en', 'ru', 'fr', 'es'], fromLang = 'zh') => {
  const debugMode = process.env.TRANSLATION_DEBUG === 'true';
  
  if (debugMode) {
    // console.log(`[翻译服务] 准备进行多语言翻译: ${targetLangs.join(', ')}`);
    // console.log(`[翻译服务] 文本: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}" 从 ${fromLang}`);
    // console.log(`[翻译服务] 使用${TRANSLATION_SERVICE === 'youdao' ? '有道翻译' : TRANSLATION_SERVICE === 'doubao' ? 'Doubao翻译' : '百度翻译'}服务`);
  }
  
  // 过滤掉不支持的语言
  const validLangs = targetLangs.filter(lang => isSupportedLanguage(lang));
  if (validLangs.length === 0) {
    // console.error('[翻译服务] 没有有效的目标语言');
    return {
      success: false,
      error: '没有有效的目标语言',
      translations: {}
    };
  }
  
  try {
    // 根据配置选择翻译服务
    let result;
    if (TRANSLATION_SERVICE === 'deepseek') {
      result = await translateToMultipleLanguagesWithDeepseek(text, validLangs, fromLang);
    } else {
      result = await translateToMultipleLanguagesWithBaidu(text, validLangs, fromLang);
    }
    
    // 如果服务调用成功
    if (result.success) {
      if (debugMode) {
        // console.log('[翻译服务] 多语言翻译成功', Object.keys(result.translations));
      }
      return result;
    }
    
    // 如果选择的翻译服务失败，尝试其他服务
    // console.warn(`[翻译服务] ${TRANSLATION_SERVICE}多语言翻译失败: ${result.error}，尝试单独翻译每种语言`);
    
    // 如果批量翻译API失败，则使用单语种翻译API逐一翻译
    const fallbackResults = {};
    let allSuccess = true;
    
    for (const lang of validLangs) {
      try {
        // console.log(`[翻译服务] 尝试单独翻译 ${lang}...`);
        const singleResult = await translate(text, fromLang, lang);
        
        if (singleResult.success) {
          fallbackResults[lang] = singleResult.translatedText;
          // console.log(`[翻译服务] ${lang} 翻译成功`);
        } else {
          fallbackResults[lang] = `[${lang.toUpperCase()}] ${text}`;
          allSuccess = false;
          // console.error(`[翻译服务] ${lang} 翻译失败: ${singleResult.error}`);
        }
      } catch (langError) {
        fallbackResults[lang] = `[${lang.toUpperCase()}] ${text}`;
        allSuccess = false;
        // console.error(`[翻译服务] ${lang} 翻译异常: ${langError.message}`);
      }
    }
    
    return {
      success: allSuccess,
      translations: fallbackResults,
      error: allSuccess ? null : '部分语言翻译失败',
      partialSuccess: true
    };
    
  } catch (error) {
    // console.error('[翻译服务] 多语言翻译出错:', error);
    
    // 出错时返回错误，不再使用模拟翻译
    const errorResults = {};
    for (const lang of validLangs) {
      errorResults[lang] = `[${lang.toUpperCase()}] ${text}`;
    }
    
    return {
      success: false,
      error: `翻译服务错误: ${error.message}`,
      translations: errorResults
    };
  }
};

/**
 * 切换翻译服务提供商 ? * @param {string} service - 服务提供商，可选值为'baidu' ?youdao' ?doubao'
 * @returns {boolean} - 是否切换成功
 */
const switchTranslationService = (service) => {
  const validServices = ['baidu', 'deepseek'];
  if (!validServices.includes(service)) {
    // console.error(`[翻译服务] 无效的服务提供商: ${service}`);
    return false;
  }
  process.env.TRANSLATION_SERVICE = service;
  // console.log(`[翻译服务] 已切换到 ${service} 服务`);
  return true;
};

/**
 * 保存翻译记录
 * @param {number} carId - 车辆ID
 * @param {string} productId - 商品编号
 * @param {string} field - 字段名称
 * @param {string} originalContent - 原始内容
 * @param {string} translatedContent - 翻译后内容
 * @param {string} language - 目标语言
 * @param {string} source - 翻译来源: auto-自动翻译, manual-人工翻译
 * @returns {Promise<Translation>} - 翻译记录
 */
const saveTranslation = async (carId, productId, field, originalContent, translatedContent, language = LANGUAGES.EN, source = 'auto') => {
  if (!isSupportedLanguage(language)) {
    throw new Error(`不支持的语言代码: ${language}`);
  }

  try {
    const translation = await Translation.create({
      carId,
      productId,
      field,
      originalContent,
      translatedContent,
      language,
      translationSource: source,
      translationTime: new Date(),
      status: 1
    });
    
    return translation;
  } catch (error) {
    console.error('保存翻译记录错误:', error);
    throw new Error('保存翻译记录错误');
  }
};

/**
 * 获取车辆的翻译记录 ? * @param {number} carId - 车辆ID
 * @param {string} language - 目标语言
 * @returns {Promise<Object>} - 翻译记录映射对象
 */
const getCarTranslations = async (carId, language = LANGUAGES.EN) => {
  if (!isSupportedLanguage(language)) {
    throw new Error(`不支持的语言代码: ${language}`);
  }

  try {
    // 检查缓存
    const cacheKey = `car:translations:${carId}:${language}`;
    const cachedTranslations = await getCache(cacheKey);
    if (cachedTranslations) {
      return cachedTranslations;
    }

    const translations = await Translation.findAll({
      where: {
        carId,
        language,
        status: 1
      }
    });
    
    // 构建字段映射
    const translationMap = {};
    translations.forEach(translation => {
      translationMap[translation.field] = translation.translatedContent;
    });
    
    // 设置缓存
    await setCache(cacheKey, translationMap, 3600); // 1小时缓存
    
    return translationMap;
  } catch (error) {
    console.error('获取翻译记录错误:', error);
    return {};
  }
};

/**
 * 获取车辆的多语言翻译记录
 * @param {number} carId - 车辆ID
 * @param {Array<string>} languages - 目标语言数组
 * @returns {Promise<Object>} - 多语言翻译记录 {langCode: {field: translatedContent}}
 */
const getCarMultiLanguageTranslations = async (carId, languages = Object.values(LANGUAGES)) => {
  try {
    const validLanguages = languages.filter(lang => isSupportedLanguage(lang));
    const results = {};
    
    // 并行获取所有语言的翻译
    const translationPromises = validLanguages.map(async lang => {
      try {
        const translations = await getCarTranslations(carId, lang);
        return { lang, translations };
      } catch (error) {
        console.error(`获取 ${lang} 翻译失败:`, error.message);
        return { lang, translations: {} };
      }
    });
    
    const allTranslations = await Promise.all(translationPromises);
    
    // 构建结果对象
    allTranslations.forEach(({ lang, translations }) => {
      results[lang] = translations;
    });
    
    return results;
  } catch (error) {
    console.error('获取多语言翻译记录错误:', error);
    return {};
  }
};

/**
 * 更新车辆翻译内容
 * @param {number} id - 商品编号或配置车辆ID
 * @param {string} language - 目标语言
 * @param {Object} translations - 翻译内容
 * @param {string} type - 更新类型，默认为'product'
 * @returns {Promise<boolean>} - 成功返回true
 */
const updateCarTranslations = async (id, language, translations, type = 'product') => {
  try {
    console.log(`开始处理翻译更新 - 类型: ${type}, 语言: ${language}, ID: ${id}...`);
    
  if (!isSupportedLanguage(language)) {
    throw new Error(`不支持的语言代码: ${language}`);
  }
    
    if (Object.keys(translations).length === 0) {
      console.log(`没有需要更新的翻译内容`);
      return true;
    }
    
    // 根据类型调用相应的upsert函数
    if (type === 'product') {
      await upsertProductTranslation(id, translations, language);
    } else if (type === 'configCar') {
      await upsertConfigCarTranslation(id, language, translations);
    } else {
      throw new Error(`不支持的更新类型: ${type}`);
    }
    
    return true;
  } catch (error) {
    console.error(`更新${type} ID ${id} 的 ${language} 翻译失败:`, error);
    throw error;
  }
};

/**
 * 更新产品或配置车的多语言翻译（原子性操作）
 * @param {string} type - 类型（PRODUCT 或 CONFIG_CAR）
 * @param {Object} record - 产品或配置车记录
 * @param {string[]} languages - 要翻译的语言数组
 * @returns {Promise<Object>} - 翻译结果
 */
const updateCarMultiLanguageTranslations = async (type, record, languages = Object.values(LANGUAGES)) => {
  if (!record || !record.id) {
    return { success: false, error: '无效的记录' };
  }

  if (!Array.isArray(languages) || languages.length === 0) {
    return { success: false, error: '无效的语言数组' };
  }

  // 初始化翻译状态
  const entityType = type === 'PRODUCT' ? 'product' : 'configCar';
  const entityId = type === 'PRODUCT' ? record.productId : record.id;

  await TranslationStatusService.initializeTranslationStatus(entityType, entityId, languages);

  // 使用事务服务确保原子性
  const TransactionService = require('./transactionService');

  return TransactionService.executeMultiLanguageUpdate(
    type,
    record,
    languages,
    async (record, language, transaction) => {
      try {
        if (!isSupportedLanguage(language)) {
          await TranslationStatusService.failLanguageTranslation(
            entityType,
            entityId,
            language,
            new Error(`不支持的语言: ${language}`),
            transaction
          );
          return { success: false, error: `不支持的语言: ${language}` };
        }

        // 开始语言翻译
        await TranslationStatusService.startLanguageTranslation(entityType, entityId, language, transaction);

        let result;
        if (type === 'PRODUCT') {
          result = await updateProductTranslationWithCheck(record, language, transaction);
        } else if (type === 'CONFIG_CAR') {
          result = await updateConfigCarTranslationWithCheck(record, language, record.modelId, transaction);
        } else {
          await TranslationStatusService.failLanguageTranslation(
            entityType,
            entityId,
            language,
            new Error(`不支持的类型: ${type}`),
            transaction
          );
          return { success: false, error: `不支持的类型: ${type}` };
        }

        // 根据结果更新状态
        if (result.success) {
          await TranslationStatusService.completeLanguageTranslation(entityType, entityId, language, transaction);
        } else {
          await TranslationStatusService.failLanguageTranslation(
            entityType,
            entityId,
            language,
            new Error(result.error),
            transaction
          );
        }

        return result;
      } catch (error) {
        console.error(`[翻译服务] ${type} ${language} 翻译失败:`, error);
        await TranslationStatusService.failLanguageTranslation(entityType, entityId, language, error, transaction);
        return { success: false, error: error.message };
      }
    }
  );
};

// --- 重新添加辅助函数 ---
const getProductTranslatableFields = () => {
  // --- 修改: 只返回明确需要翻译的字段 ---
  return [
    'color', 'title', 'interiorColor', 'condition', 'brand',
    // 新增的车辆规格字段，需要翻译
    'energyType', 'bodyStructure', 'gearboxType', 'driveType'
  ];
  /*
  // 旧的排除逻辑
  const excludeFields = new Set([
    'productId', 'modelId', 'dealerId',
    'price', 'priceUsd', 'mainImage', 'images',
    'status', 'createdAt', 'updatedAt',
    'test_en', 'test_ru', 'test_fr', 'test_es',
    // 明确排除非文本或不需要翻译的字段
    'manufactureDate', 'firstRegistration', 'mileage', 'transferCount'
  ]);
  return Object.keys(Product.rawAttributes).filter(field => !excludeFields.has(field));
  */
};

/**
 * 获取ConfigCar中需要翻译的字段列表
 * 不包含peizhi_xx字段
 * @returns {Array<string>} 需要翻译的字段名称列表
 */
const getConfigCarTranslatableFields = () => {
  // 保持 ConfigCar 的逻辑不变，如果也需要修改请告知
  return [
    'brandName',
    'seriesName',
    'manufacturer',
    'modelName',
    'saleStatus',
    'level',
    'energyType',
    'emissionStandard',
    'gearbox',
    'bodyStructure',
    'engine',
    'warranty',
    'doorOpeningType',
    'intakeForm',
    'cylinderArrangement',
    'valvetrainType',
    'fuelGrade',
    'fuelSupplyType',
    'cylinderHeadMaterial',
    'cylinderBodyMaterial',
    'gearboxType',
    'driveType',
    'fourWheelDriveType',
    'centralDifferentialStructure',
    'frontSuspensionType',
    'rearSuspensionType',
    'powerSteeringType',
    'bodyType',
    'frontBrakeType',
    'rearBrakeType',
    'parkingBrakeType',
    'spareTireSpec'
  ];
};

/**
 * 检查字符串是否包含中文
 * @param {string} str - 要检查的字符串
 * @returns {boolean} - 包含中文返回true
 */
function containsChinese(str) {
  if (!str || typeof str !== 'string') return false;
  // 增强中文检测，确保能检测到所有中文字符
  return /[\u4E00-\u9FFF\u3400-\u4DBF\uF900-\uFAFF]/.test(str);
}

/**
 * 创建或更新产品翻译
 * @param {number} productId - 产品ID
 * @param {string} language - 目标语言
 * @param {object} translations - 翻译内容 { field: translatedValue }
 * @param {object} options - 选项，包含事务等
 * @returns {Promise<object>} - 创建或更新的记录
 */
const upsertProductTranslation = async (productId, language, translations = {}, options = {}) => {
  // 精简日志
  console.log(`[产品翻译] 开始处理: productId=${productId}, 语言=${language}`);
  
  try {
    if (!isSupportedLanguage(language)) {
      console.error(`[产品翻译] 不支持的语言代码: ${language}`);
      throw new Error(`不支持的语言代码: ${language}`);
    }

    const TargetModel = PRODUCT_MODELS[language];
    if (!TargetModel) {
      console.error(`[产品翻译] 未找到语言 ${language} 对应的产品模型`);
      throw new Error(`未找到语言 ${language} 对应的产品模型`);
    }

    // 查找原始产品数据 - 明确指定需要的字段
    const product = await Product.findOne({
      where: { productId: productId },
      attributes: [
        'productId', 'modelId', 'brand', 'title', 'energyType', 'bodyStructure',
        'gearboxType', 'driveType', 'color', 'interiorColor', 'manufactureDate',
        'firstRegistration', 'mileage', 'transferCount', 'condition', 'price',
        'priceUsd', 'mainImage', 'images', 'status', 'dealerId', 'createdAt', 'updatedAt'
      ]
    });
    if (!product) {
      console.error(`[产品翻译] 无法找到ID为${productId}的产品`);
      throw new Error(`无法找到ID为${productId}的产品`);
    }

    const productData = product.get({ plain: true });
    console.log(`[产品翻译] 获取到产品数据，dealerId: ${productData.dealerId}`);

    // 如果原始数据中没有dealerId，从数据库重新查询完整数据
    if (!productData.dealerId) {
      console.log(`[产品翻译] 原始数据缺少dealerId，重新查询完整产品数据`);
      const fullProduct = await Product.findByPk(productId);
      if (fullProduct && fullProduct.dealerId) {
        productData.dealerId = fullProduct.dealerId;
        console.log(`[产品翻译] 从完整查询中获取到dealerId: ${productData.dealerId}`);
      }
    }

    const modelId = productData.modelId;
    
    // 不应该复制的字段
    const excludeFields = [
      'id', 
      'test_en', 'test_ru', 'test_fr', 'test_es', 'test_all',
      'createdAt', 'updatedAt'
    ];
    
    // 创建要保存的数据对象
    const saveData = {};
    
    // 使用getAllProductFields获取完整的字段列表
    const allFields = getAllProductFields();
    
    // 特别关注的字段
    const criticalFields = ['manufactureDate', 'firstRegistration', 'mileage', 'transferCount', 'price', 'priceUsd', 'mainImage', 'images', 'dealerId', 'modelId'];
    
    // 检查是否为产品修改翻译（只翻译变更字段）
    const isProductUpdate = options.changedFields && Object.keys(options.changedFields).length > 0;
    console.log(`[产品翻译] 检查产品修改翻译: isProductUpdate=${isProductUpdate}, changedFields=${JSON.stringify(options.changedFields)}`);

    // 如果传入了翻译对象，使用传入的翻译
    if (Object.keys(translations).length > 0) {
      console.log(`[产品翻译] 使用提供的翻译数据(${Object.keys(translations).length}个字段)`);

      if (isProductUpdate) {
        // 产品修改翻译：只保存真正变更的字段
        Object.keys(translations).forEach(field => {
          saveData[field] = translations[field];
        });

        console.log(`[产品修改翻译] 只保存变更的字段: ${Object.keys(saveData).join(', ')}`);
      } else {
        // 产品上架翻译：保存所有字段
        allFields.forEach(field => {
          // 跳过不应该复制的字段
          if (excludeFields.includes(field)) {
            return;
          }

          // 优先使用翻译中的值
          if (field in translations) {
            saveData[field] = translations[field];
          }
          // 否则从原始产品数据中复制字段值
          else if (field in productData) {
            saveData[field] = productData[field];
          }
        });
      }
    } else {
      // 没有翻译数据时，直接复制所有字段
      allFields.forEach(field => {
        // 跳过不应该复制的字段
        if (excludeFields.includes(field)) {
          return;
        }

        // 从原始产品数据中复制字段值
        if (field in productData) {
          saveData[field] = productData[field];
        }
      });
    }

    // 对于产品上架翻译，确保关键字段存在
    if (!isProductUpdate && !saveData.productId) saveData.productId = productId;

    // 对于产品修改翻译，不需要设置其他字段
    if (!isProductUpdate) {
      if (!saveData.modelId) saveData.modelId = modelId;

      // 确保dealerId字段被正确复制（如果还没有设置的话）
      if (!saveData.dealerId && productData.dealerId) {
        saveData.dealerId = productData.dealerId;
        console.log(`[产品翻译] 确保dealerId字段: ${saveData.dealerId}`);
      } else if (!productData.dealerId) {
        console.warn(`[产品翻译] 警告: 原始产品数据中没有dealerId字段`);
      }

      // 保留原始产品的状态（如果还没有设置的话）
      if (!saveData.status) saveData.status = productData.status || 'pending';
    }
    
    // 根据翻译类型选择保存方式
    console.log(`[产品翻译] 正在保存 ${Object.keys(saveData).length} 个字段到 ${language} 表: ${Object.keys(saveData).join(', ')}`);

    try {
      let record, created;

      if (isProductUpdate) {
        // 产品修改翻译：使用纯更新操作，只更新变更的字段
        const [affectedRows] = await TargetModel.update(saveData, {
          where: { productId: productId },
          transaction: options.transaction,
          logging: false
        });

        if (affectedRows > 0) {
          // 获取更新后的记录
          record = await TargetModel.findOne({
            where: { productId: productId },
            transaction: options.transaction,
            logging: false
          });
          created = false;
        } else {
          throw new Error(`未找到要更新的记录: productId=${productId}`);
        }
      } else {
        // 产品上架翻译：使用upsert操作
        [record, created] = await TargetModel.upsert(saveData, {
          returning: true,
          logging: false,
          transaction: options.transaction
        });
      }
      
      console.log(`[产品翻译] ${created ? '创建' : '更新'}成功: productId=${productId}, 语言=${language}`);
      
      return record ? record.get({ plain: true }) : null;
    } catch (upsertError) {
      console.error(`[产品翻译] 保存失败: ${upsertError.message}`);

      if (upsertError.name === 'SequelizeForeignKeyConstraintError') {
        console.error(`[产品翻译] 外键约束错误! 检查 modelId=${saveData.modelId} 在 config_car 表中是否存在!`);

        // 尝试自动修复外键约束错误
        const ErrorRecoveryService = require('./errorRecoveryService');
        const repairResult = await ErrorRecoveryService.handleForeignKeyConstraintError(upsertError, {
          modelId: saveData.modelId,
          productId: productId
        });

        if (repairResult.success && repairResult.repaired) {
          console.log(`[产品翻译] 外键约束错误已修复，重试保存操作`);
          // 重试保存操作
          const [record, created] = await TargetModel.upsert(saveData, {
            returning: true,
            logging: false,
            transaction: options.transaction
          });
          console.log(`[产品翻译] 重试保存成功: productId=${productId}, 语言=${language}`);
          return record ? record.get({ plain: true }) : null;
        } else {
          // 修复失败，抛出自定义错误
          const customError = new Error(`ModelId ${saveData.modelId} 在 config_car 表中不存在，且自动修复失败: ${repairResult.error}`);
          customError.name = 'ConfigCarNotFoundError';
          customError.modelId = saveData.modelId;
          throw customError;
        }
      }
      throw upsertError; // 重新抛出原始错误
    }
  } catch (error) {
    console.error(`[产品翻译] 处理失败: ${error.message}`);
    throw error; // 将错误抛出给调用者
  }
};

/**
 * @param {number} modelId - 车型ID
 * @param {string} language - 目标语言
 * @param {Object} translations - 翻译内容对象（可选，如果未提供则会自动进行翻译） << **注意：此参数在新逻辑下会被忽略**
 * @returns {Promise<Object>} - 包含操作结果和数据的对象
 */
const upsertConfigCarTranslation = async (modelId, language, translations = {}, options = {}) => {
    if (!isSupportedLanguage(language)) {
    console.error(`[配置翻译] 不支持的语言: ${language}`);
    return { success: false, error: `不支持的语言: ${language}` };
  }

  if (!modelId) {
    console.error(`[配置翻译] 缺少modelId参数`);
    return { success: false, error: `缺少modelId参数` };
    }

    const TargetModel = CONFIG_CAR_MODELS[language];
    if (!TargetModel) {
    console.error(`[配置翻译] 未找到${language}对应的翻译模型`);
    return { success: false, error: `未找到${language}对应的翻译模型` };
  }

  let fieldsNeedTranslation = 0;
  let apiTranslated = 0;
  let apiFailedOrKept = 0;

  try {
    // 精简日志，只保留关键信息
    console.log(`[配置翻译] 开始处理 modelId=${modelId}, 语言=${language}`);

    // 1. 获取原始配置车型数据
    const configCar = await ConfigCar.findOne({
      where: Sequelize.or({ modelId: modelId }, { '车型_id': modelId }),
      raw: true // 获取纯数据对象
    });

    if (!configCar) {
      console.error(`[配置翻译] 未找到modelId为 ${modelId} 的配置车型记录`);
      return { success: false, error: `未找到modelId为 ${modelId} 的配置车型记录` };
    }

    // 2. 确定关联ID字段
    const sourceModelIdKey = configCar.modelId !== undefined ? 'modelId' : '车型_id';
    const sourceModelIdValue = configCar[sourceModelIdKey];

    // 3. 目标模型的关联ID字段
    const targetModelAttributes = TargetModel.rawAttributes;
    const targetModelIdKey = targetModelAttributes.modelId ? 'modelId' :
                            targetModelAttributes.车型_id ? '车型_id' : null;

    if (!targetModelIdKey) {
      console.error(`[配置翻译] 目标模型 ${TargetModel.name} 缺少 modelId 或 车型_id 字段定义`);
      return { success: false, error: `目标模型缺少必要的ID字段定义` };
    }

    // 4. 准备数据对象
    const dataToUpsert = {};
    dataToUpsert[targetModelIdKey] = sourceModelIdValue;

    // 5. 获取目标模型的字段列表
    const modelAttributes = new Set(Object.keys(TargetModel.rawAttributes));

    // 6. 查找所有包含中文的字段，并应用新逻辑
    const skipFields = new Set([
      'id', 'createdAt', 'updatedAt',
      ...Object.values(LANGUAGES).map(lang => `peizhi_${lang}`)
    ]);

    // 获取所有原始字段
    const configCarFields = Object.keys(configCar);
    const fieldsWithChinese = [];
    const textsToTranslate = [];
    const fieldMap = {};
    const fieldProcessingPromises = []; // 用于异步处理字段

    // --- 修改： 提取品牌名称以备后用 ---
    const originalBrandName = configCar.brandName || configCar['品牌']; // 获取品牌名称

    // 先找出所有包含中文的字段并处理非特殊字段
    for (const field of configCarFields) {
      if (skipFields.has(field)) continue;
      if (!modelAttributes.has(field)) continue;

      const value = configCar[field];
      const isChinese = typeof value === 'string' && containsChinese(value);

      // --- 修改：应用新的翻译逻辑 ---
      if (field === 'brandName' || field === '品牌') { // --- 品牌字段处理 ---
        if (value) { // 确保品牌值存在
            const englishBrandName = getBrandEnglishName(value);
            if (englishBrandName) {
                dataToUpsert[field] = englishBrandName; // 使用对照表翻译
                console.log(`[配置翻译] 字段 ${field} 使用对照表翻译为: ${englishBrandName}`);
            } else if (isChinese) { // 对照表没有，且是中文，则调用API翻译成英文
                fieldsWithChinese.push(field);
                textsToTranslate.push(value);
                fieldMap[textsToTranslate.length - 1] = { name: field, forceEnglish: true }; // 标记强制英文
      } else {
                dataToUpsert[field] = value; // 非中文且不在对照表，保留原样
            }
        } else {
             dataToUpsert[field] = value; // 空值或非字符串，保留原样
        }
      } else if (field === 'modelName' || field === '车型名称') { // --- 车型名称字段处理 ---
        if (isChinese) {
          // 首先检查是否在车型翻译表中存在
          const carModelResult = getCarModelEnglishName(value);
          
          if (carModelResult && carModelResult.found) {
            // 找到了车型，使用英文车型名称替换中文车型名称
            dataToUpsert[field] = carModelResult.englishName;
            console.log(`[配置翻译] 字段 ${field} 使用车型翻译表翻译: "${value}" -> "${carModelResult.englishName}"`);
            
            // 如果有剩余文本，需要处理
            if (carModelResult.remainingText && carModelResult.remainingText.trim() !== '') {
              // 剩余文本放入待翻译队列
              fieldsWithChinese.push(`${field}_remaining`);
              textsToTranslate.push(carModelResult.remainingText);
              // 标记这是残余文本，稍后特殊处理
              fieldMap[textsToTranslate.length - 1] = { 
                name: field, 
                forceEnglish: false,
                isRemainingText: true,
                originalValue: dataToUpsert[field] // 保存已翻译的车型名
              };
            }
            continue; // 直接处理下一个字段
          }
          
          // 如果车型翻译表中没有，尝试移除品牌名
          let textToTranslate = value;
          if (originalBrandName) {
            // 尝试移除品牌名（包括处理 "xx汽车" vs "xx"）
            let normalizedBrand = originalBrandName.trim();
            if (normalizedBrand.endsWith('汽车')) {
              normalizedBrand = normalizedBrand.substring(0, normalizedBrand.length - 2).trim();
            }
            if (textToTranslate.startsWith(normalizedBrand)) {
               textToTranslate = textToTranslate.substring(normalizedBrand.length).trim();
               logger.debug(`配置翻译字段 ${field} 移除品牌 "${normalizedBrand}" 后变为: "${textToTranslate}"`);
            } else if (textToTranslate.startsWith(originalBrandName.trim())) { // 再次尝试原始品牌名
               textToTranslate = textToTranslate.substring(originalBrandName.trim().length).trim();
               logger.debug(`配置翻译字段 ${field} 移除品牌 "${originalBrandName.trim()}" 后变为: "${textToTranslate}"`);
            }
          }
          fieldsWithChinese.push(field);
          textsToTranslate.push(textToTranslate); // 使用可能已修改的文本
          fieldMap[textsToTranslate.length - 1] = { name: field, forceEnglish: false }; // 不强制英文
        } else {
          dataToUpsert[field] = value; // 非中文，保留原样
        }
      } else { // --- 其他字段处理 ---
        if (isChinese) {
          fieldsWithChinese.push(field);
          textsToTranslate.push(value);
          fieldMap[textsToTranslate.length - 1] = { name: field, forceEnglish: false }; // 不强制英文
        } else if (value !== null && value !== undefined) {
          // 非中文字段直接复制到目标对象
          dataToUpsert[field] = value;
        }
      }
    } // 结束字段遍历

    // 记录包含中文且需要API翻译的字段数量
    fieldsNeedTranslation = fieldsWithChinese.length;
    console.log(`[配置翻译] 检测到 ${fieldsNeedTranslation} 个字段需要调用API翻译`);

    if (fieldsNeedTranslation > 0) {
      // --- 修改：调整翻译调用以处理强制英文 ---
      let translationResults;
      const logger = require('../utils/logger');

      const translationPromises = textsToTranslate.map((text, index) => {
          const fieldInfo = fieldMap[index];
          const targetLang = fieldInfo.forceEnglish ? 'en' : language; // 决定目标语言
          logger.debug(`配置翻译准备字段 ${fieldInfo.name} (原文: "${text.substring(0,20)}...") 到 ${targetLang}`);
          return translate(text, 'zh', targetLang, {
            productId: `config-${modelId}`,
            isFirstField: index === 0
          });
      });

      // --- 修改：使用 Promise.all 并行处理 ---
      logger.debug(`配置翻译使用并行模式翻译 ${fieldsNeedTranslation} 个字段 (目标语言: ${language} / en)`);
      translationResults = await Promise.all(translationPromises);


      // 处理翻译结果
      translationResults.forEach((result, index) => {
        const fieldInfo = fieldMap[index];
        const field = fieldInfo.name;
        const originalText = textsToTranslate[index]; // 获取原始（可能已修改的）文本

        // 处理残余文本的特殊情况
        if (fieldInfo.isRemainingText) {
          if (result && result.success) {
            // 残余文本翻译成功，拼接到原始值后
            const combinedText = `${fieldInfo.originalValue} ${result.translatedText}`.trim();
            dataToUpsert[field] = combinedText;
            apiTranslated++;
            console.log(`[配置翻译] 字段 ${field} 残余文本翻译成功，组合结果: "${combinedText.substring(0,30)}..."`);
          } else {
            // 残余文本翻译失败，只使用已翻译的车型名
            console.log(`[配置翻译] 字段 ${field} 残余文本翻译失败，仅使用车型名: "${fieldInfo.originalValue}"`);
            // dataToUpsert[field] 已经设置为车型名，无需更新
            apiFailedOrKept++;
          }
          return; // 跳过此次循环
        }

        if (result && result.success) {
          // 翻译成功，使用翻译后的文本
          dataToUpsert[field] = result.translatedText;
          apiTranslated++;
          logger.debug(`配置翻译字段 ${field} 翻译成功: "${result.translatedText.substring(0,30)}..."`);
        } else {
          // 翻译失败，保留原文（可能已修改的文本，如移除品牌后的车型）
          dataToUpsert[field] = originalText;
          apiFailedOrKept++;

          // 只对失败情况记录错误日志
          logger.simpleError(`配置翻译字段 ${field} 翻译失败: ${result?.error || '未知错误'}`);
        }
      });

      // 精简成功日志，只记录总体情况
      logger.debug(`配置翻译完成API翻译: ${apiTranslated}个成功, ${apiFailedOrKept}个失败/保留原文`);
    }

    // 8. 执行 upsert 操作
    logger.debug(`配置翻译写入 ${Object.keys(dataToUpsert).length} 个字段到 ${language} 表`);
    const [record, created] = await TargetModel.upsert(dataToUpsert, {
      returning: true,
      transaction: options.transaction
    });

    console.log(`[配置翻译] ${created ? '创建' : '更新'}成功: modelId=${modelId}, API翻译=${apiTranslated}/${fieldsNeedTranslation}, 语言=${language}`);

    return {
      success: true,
      fieldsNeedTranslation,
      apiTranslated,
      apiFailedOrKept
    };
  } catch (error) {
    console.error(`[配置翻译] 处理失败 (modelId=${modelId}, 语言=${language}): ${error.message}`);
    return {
      success: false,
      error: `意外错误: ${error.message}`,
      fieldsNeedTranslation,
      apiTranslated,
      apiFailedOrKept
    };
  }
};

/**
 * 获取产品翻译状态字段名
 * @param {string} language - 语言代码
 * @returns {string} - 状态字段名
 */
const getProductStatusField = (language) => {
  return `test_${language}`;
};

/**
 * 获取配置车翻译状态字段名
 * @param {string} language - 语言代码
 * @returns {string} - 状态字段名
 */
const getConfigCarStatusField = (language) => {
  return `peizhi_${language}`;
};

/**
 * 检查对象所有字段是否有空值
 * @param {Object} obj - 要检查的对象
 * @param {Array<string>} excludeFields - 排除检查的字段列表
 * @returns {boolean} - 如果所有字段都不为空返回true，否则返回false
 */
const checkAllFieldsNotEmpty = (obj, excludeFields = []) => {
  if (!obj || typeof obj !== 'object') {
    console.error(`[字段检查] 无效对象: ${obj === null ? 'null' : typeof obj}`);
    return false;
  }
  
  const skipFields = new Set([
    'id', 'createdAt', 'updatedAt', ...excludeFields
  ]);
  
  const allFields = Object.keys(obj).filter(field => !skipFields.has(field));
  console.log(`[字段检查] 开始检查 ${allFields.length} 个字段是否有空值`);
  
  const emptyFields = [];
  
  for (const field in obj) {
    if (skipFields.has(field)) {
      continue;
    }
    
    const value = obj[field];
    // 检查null, undefined和空字符串（但对于数字0是有效值）
    if (value === null || value === undefined || 
       (typeof value === 'string' && value.trim() === '')) {
      console.log(`[字段检查] 字段 ${field} 为空或无效 (值: ${value})`);
      emptyFields.push(field);
    }
  }
  
  const result = emptyFields.length === 0;
  if (result) {
    console.log(`[字段检查] 所有 ${allFields.length} 个字段检查通过，没有发现空值`);
  } else {
    console.error(`[字段检查] 发现 ${emptyFields.length} 个空字段: ${emptyFields.join(', ')}`);
  }
  
  return result;
};

/**
 * 检查并更新配置车型翻译状态
 * @param {Object} data - 包含modelId、language和translations的对象
 * @param {string} language - 目标语言
 * @param {number} modelId - 车型ID
 * @param {Transaction} transaction - 可选的事务对象
 * @returns {Promise<Object>} - 翻译结果
 */
const updateConfigCarTranslationWithCheck = async (data, language = null, modelId = null, transaction = null) => {
  const debugMode = true;
  // 支持新的函数签名和旧的data结构
  const actualModelId = modelId || (data && data.modelId);
  const actualLanguage = language || (data && data.language);
  console.log(`[配置翻译检查] 开始处理 modelId=${actualModelId}, language=${actualLanguage}...`); // 精简日志

  if (!actualModelId) {
    console.error('updateConfigCarTranslationWithCheck: 缺少必须的modelId参数');
    return { success: false, error: '缺少必须的modelId参数' };
  }

  if (!actualLanguage) {
    console.error('updateConfigCarTranslationWithCheck: 缺少必须的language参数');
    return { success: false, error: '缺少必须的language参数' };
  }

  try {
    // 直接调用 upsertConfigCarTranslation，它内部会处理所有逻辑
    // 不再需要传递 translations 参数，因为会被忽略
    // console.log(`[配置翻译检查][简化流程] 调用 upsertConfigCarTranslation(modelId=${modelId}, language=${language})...`); // 可选精简
    const result = await upsertConfigCarTranslation(actualModelId, actualLanguage, {}, { transaction });
    
    if (result.success) {
      console.log(`[配置翻译检查] Upsert成功: modelId=${actualModelId}, lang=${actualLanguage}. (${result.fieldsNeedTranslation}需翻译, ${result.apiTranslated} API成功)`);

      // 更新翻译状态
      // console.log(`[配置翻译检查][简化流程] 调用 checkAndUpdateConfigCarTestStatus(modelId=${actualModelId}) 更新状态...`); // 可选精简
      await checkAndUpdateConfigCarTestStatus(actualModelId);
      
      return {
        success: true,
        modelId: actualModelId,
        language: actualLanguage,
        // 返回 upsert 函数的结果中更有用的信息
        fieldsNeedTranslation: result.fieldsNeedTranslation,
        apiTranslated: result.apiTranslated,
        apiFailedOrKept: result.apiFailedOrKept
      };
    } else {
      console.error(`[配置翻译检查] Upsert失败: modelId=${actualModelId}, lang=${actualLanguage}: ${result.error}`);
      return {
        success: false,
        error: result.error,
        modelId: actualModelId,
        language: actualLanguage
      };
    }
  } catch (error) {
    const logger = require('../utils/logger');
    logger.simpleError(`配置翻译检查失败 modelId=${actualModelId}, lang=${actualLanguage}`, error);
    return {
      success: false,
      error: error.message,
      modelId: actualModelId,
      language: actualLanguage
    };
  }
};

/**
 * 更新配置车翻译状态
 * @param {number} modelId - 车型ID
 * @param {string} language - 目标语言
 * @param {boolean} status - 翻译状态
 * @returns {Promise<object>} - 更新结果
 */
const updateConfigCarTranslationStatus = async (modelId, language, status = true) => {
  const logger = require('../utils/logger');
  logger.debug(`更新配置车型状态: modelId=${modelId}, peizhi_${language}=${status}`);

  if (!modelId) {
    logger.simpleError('配置翻译缺少modelId参数，无法更新翻译状态');
    return { success: false, message: '缺少modelId参数' };
  }
  
  try {
    // 获取语言对应的状态字段
    const statusField = getConfigCarStatusField(language);
    if (!statusField) {
      return { success: false, message: `不支持的语言: ${language}` };
    }
    
    // 先尝试使用车型_id查询
    let configCar = await ConfigCar.findOne({
      where: { 车型_id: modelId }
    });
    
    // 如果没找到，再尝试使用id直接查询
    if (!configCar) {
      configCar = await ConfigCar.findOne({
        where: { id: modelId }
      });
    }
    
    if (!configCar) {
      console.error(`未找到modelId为${modelId}的配置车型记录，无法更新状态`);
      return { success: false, message: `未找到记录` };
    }
    
    // 更新翻译状态
    const updateData = {};
    updateData[statusField] = status;
    
    await configCar.update(updateData);
    if (debugMode) console.log(`成功更新配置车型ID ${configCar.id} 的 ${statusField} 状态为 ${status}`);
    
    return { success: true, message: `成功更新状态`, configCar };
  } catch (error) {
    console.error(`更新配置车翻译状态失败:`, error);
    return { success: false, message: `更新失败: ${error.message}` };
  }
};

/**
 * 优化的翻译验证
 * @param {Object} originalProduct - 原始产品数据
 * @param {Object} translatedRecord - 翻译后的记录
 * @param {string} language - 目标语言
 * @param {Array} requiredFields - 必翻字段
 * @returns {Promise<Object>} - 验证结果
 */
const validateTranslationQuality = async (originalProduct, translatedRecord, language, requiredFields) => {
  try {
    const issues = [];

    // 1. 核心验证：检查所有保存的字段都不为空
    const ignoreFields = new Set(['id', 'createdAt', 'updatedAt']);
    const allFields = getAllProductFields().filter(field => !ignoreFields.has(field));

    console.log(`[翻译验证] 检查所有 ${allFields.length} 个字段是否有值`);

    for (const field of allFields) {
      if (field in translatedRecord) {
        const value = translatedRecord[field];
        // 检查 null, undefined, 空字符串。数字0是有效值
        if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
          issues.push(`字段 ${field} 为空`);
        }
      } else {
        // 字段不存在也视为问题
        issues.push(`字段 ${field} 不存在`);
      }
    }

    // 2. 必翻字段特殊检查：确保翻译了中文内容
    for (const field of requiredFields) {
      const originalValue = originalProduct[field];
      const translatedValue = translatedRecord[field];

      // 如果原文包含中文，检查是否已翻译
      if (originalValue && containsChinese(originalValue)) {
        if (translatedValue && containsChinese(translatedValue)) {
          issues.push(`必翻字段 ${field} 仍包含中文字符，翻译不完整`);
        }
      }
    }

    // 3. 数据完整性检查
    if (!translatedRecord.productId || translatedRecord.productId !== originalProduct.productId) {
      issues.push('翻译记录的productId不匹配');
    }

    return {
      isValid: issues.length === 0,
      reason: issues.length > 0 ? `发现 ${issues.length} 个问题` : '验证通过',
      issues: issues,
      validatedFields: allFields.length
    };

  } catch (error) {
    console.error(`[翻译验证] 验证过程出错:`, error);
    return {
      isValid: false,
      reason: '验证过程出错',
      error: error.message
    };
  }
};





/**
 * 验证翻译记录的完整性
 * @param {string} entityId - 实体ID
 * @param {string} language - 语言代码
 * @param {string} entityType - 实体类型
 * @returns {Promise<Object>} - 验证结果
 */
const validateTranslationRecord = async (entityId, language, entityType) => {
  try {
    const { LANGUAGES, isSupportedLanguage } = require('../config/languages');

    if (!isSupportedLanguage(language)) {
      return { isValid: false, issues: ['unsupported_language'] };
    }

    // 获取对应的语言模型
    const languageModels = {
      'product': {
        'en': ProductEn,
        'es': ProductEs,
        'fr': ProductFr,
        'ru': ProductRu
      },
      'configCar': {
        'en': ConfigCarEn,
        'es': ConfigCarEs,
        'fr': ConfigCarFr,
        'ru': ConfigCarRu
      }
    };

    const LanguageModel = languageModels[entityType]?.[language];
    if (!LanguageModel) {
      return { isValid: false, issues: ['language_model_not_found'] };
    }

    // 查找翻译记录
    const whereClause = entityType === 'product'
      ? { productId: entityId }
      : { id: entityId };

    const record = await LanguageModel.findOne({
      where: whereClause,
      raw: true
    });

    if (!record) {
      return { isValid: false, issues: ['translation_record_not_found'] };
    }

    // 检查必要字段的完整性
    const requiredFields = entityType === 'product'
      ? ['title', 'brand']
      : ['brandName', 'modelName'];

    const issues = [];
    for (const field of requiredFields) {
      const value = record[field];
      if (!value || value.trim() === '') {
        issues.push(`missing_${field}`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      record
    };

  } catch (error) {
    console.error(`[记录验证] 验证失败 - ${entityType} ${entityId} ${language}:`, error);
    return {
      isValid: false,
      issues: ['validation_error'],
      error: error.message
    };
  }
};

/**
 * 检查产品翻译状态并更新翻译（如果需要）
 * 注意：此函数仅负责单语言翻译，不处理test_all状态
 * @param {Object} product - 产品记录
 * @param {string} language - 目标语言
 * @param {Transaction} transaction - 可选的事务对象
 * @param {Object} options - 优化选项
 * @returns {Promise<Object>} - 翻译结果
 */
const updateProductTranslationWithCheck = async (product, language = LANGUAGES.EN, transaction = null, options = {}) => {
  if (!isSupportedLanguage(language)) {
    return { success: false, error: `不支持的语言: ${language}` };
  }

  const {
    forceCheck = false,           // 是否强制检查翻译记录
    skipIfTranslated = true,      // 是否跳过已翻译的内容
    validateRecord = false        // 是否验证翻译记录完整性
  } = options;

  const statusField = getProductStatusField(language);

  try {
    // 详细日志记录
    logger.debug(`翻译服务开始处理产品 ID ${product.productId} 的 ${language} 翻译`);

    // 简单状态检查：仅检查状态字段
    if (skipIfTranslated && product[statusField]) {
      console.log(`[翻译服务] 产品 ID ${product.productId} 已经翻译成 ${language}`);
      return { success: true, message: '产品已翻译', alreadyTranslated: true };
    }
    
    // 检查该产品是否有 modelId
    if (product.modelId) {
      logger.debug(`翻译服务产品 ID ${product.productId} 的 modelId 是 ${product.modelId}`);
    } else {
      logger.debug(`翻译服务产品 ID ${product.productId} 没有 modelId`);
      // 无需处理，继续执行
    }

    let requiredFields = getProductTranslatableFields();

    // 如果提供了changedFields，只翻译变更的字段
    const isProductUpdate = options.changedFields && Object.keys(options.changedFields).length > 0;
    if (isProductUpdate) {
      const changedFieldNames = Object.keys(options.changedFields);
      requiredFields = requiredFields.filter(field => changedFieldNames.includes(field));
      console.log(`[产品修改翻译] 只翻译变更的字段: ${requiredFields.join(', ')}`);
    }

    // 将isProductUpdate传递给后续使用
    options.isProductUpdate = isProductUpdate;

    logger.debug(`翻译服务产品 ID ${product.productId}, 检查指定的 ${requiredFields.length} 个字段到 ${language}`);

    const translations = {};
    let allRequiredFieldsValidAndTranslated = true;
    let validationFailReason = '';
    let translationSuccessCount = 0;
    let translationFailCount = 0;

    // --- 新增：获取原始品牌名称用于后续处理 ---
    const originalBrand = product.brand;
    let brandTranslated = false;

    // 1. 并行检查必翻字段的原文和API翻译 (应用新逻辑)
    console.log(`[产品翻译] 开始处理: productId=${product.productId}, 语言=${language}`);

    // 并行处理所有字段翻译
    const fieldTranslationPromises = requiredFields.map(async (field, index) => {
      const originalContent = product[field];
      logger.debug(`翻译服务检查产品 ID ${product.productId} 的字段 ${field} 原始值: "${originalContent ? (originalContent.substring(0, 30) + (originalContent.length > 30 ? '...' : '')) : '空值'}"`);

      if (!originalContent || (typeof originalContent === 'string' && originalContent.trim() === '')) {
        return {
          field,
          success: false,
          error: `必翻字段 ${field} 原始值为空`
        };
      }

      try {
        let result;
        let textToTranslate = originalContent; // 默认使用原始内容

        // --- 修改：应用新逻辑 ---
        if (field === 'brand') { // --- 品牌字段处理 ---
          const englishBrandName = getBrandEnglishName(originalContent);
          if (englishBrandName) {
            logger.debug(`翻译服务产品 ID ${product.productId} 字段 ${field} 使用对照表翻译为: ${englishBrandName}`);
            return {
              field,
              success: true,
              translatedText: englishBrandName
            };
          } else {
            // 对照表未找到，调用API翻译，强制目标为英语
            logger.debug(`翻译服务开始翻译产品 ID ${product.productId} 的字段 ${field} (强制 en)...`);
            result = await translate(originalContent, 'zh', 'en', {
              productId: product.productId,
              isFirstField: index === 0
            }); // 强制 'en'
            
            // 直接处理 brand 字段的 API 翻译结果，不依赖下面的通用处理块
            if (result && result.success) {
              logger.debug(`翻译服务产品 ID ${product.productId} 字段 ${field} (强制 en) API翻译成功: "${result.translatedText.substring(0, 30)}${result.translatedText.length > 30 ? '...' : ''}"`);
              return {
                field,
                success: true,
                translatedText: result.translatedText
              };
            } else {
              // API 翻译失败
              return {
                field,
                success: false,
                error: `必翻字段 ${field} API 翻译失败: ${result?.error || '未知'}`
              };
            }
          }
        } else if (field === 'title') { // --- 标题字段处理 (新增车型翻译逻辑) ---
          // 首先检查标题中是否包含车型名称
          const carModelResult = getCarModelEnglishName(textToTranslate);
          
          if (carModelResult && carModelResult.found) {
            // 找到了车型，使用英文车型名称替换中文车型名称
            console.log(`[翻译服务] 产品 ID ${product.productId} 字段 ${field} 发现车型 "${carModelResult.chineseModel}" -> "${carModelResult.englishName}"`);
            
            // 处理剩余文本
            if (carModelResult.remainingText) {
              // 如果有剩余文本，调用API翻译
              console.log(`[翻译服务] 产品 ID ${product.productId} 字段 ${field} 翻译剩余文本: "${carModelResult.remainingText}"`);
              result = await translate(carModelResult.remainingText, 'zh', language, {
                productId: product.productId,
                isFirstField: index === 0
              });
              
              if (result && result.success) {
                // 拼接：英文车型名 + 翻译好的剩余文本
                const translatedText = `${carModelResult.englishName} ${result.translatedText}`;
                console.log(`[翻译服务] 产品 ID ${product.productId} 字段 ${field} 组合翻译成功: "${translatedText.substring(0, 30)}${translatedText.length > 30 ? '...' : ''}"`);
                return {
                  field,
                  success: true,
                  translatedText
                };
              } else {
                // 剩余文本翻译失败，但我们可以只使用车型名，或者选择失败
                const translatedText = carModelResult.englishName; // 至少保留车型名
                console.log(`[翻译服务] 产品 ID ${product.productId} 字段 ${field} 剩余文本翻译失败，仅使用车型名: "${translatedText}"`);
                return {
                  field,
                  success: true,
                  translatedText
                };
              }
            } else {
              // 没有剩余文本，直接使用英文车型名
              const translatedText = carModelResult.englishName;
              console.log(`[翻译服务] 产品 ID ${product.productId} 字段 ${field} 仅包含车型: "${translatedText}"`);
              return {
                field,
                success: true,
                translatedText
              };
            }
          }
          
          // 如果没有找到车型或处理车型后仍需处理，尝试移除品牌名
          if (originalBrand) {
            // 尝试移除品牌名
            let normalizedBrand = originalBrand.trim();
            if (normalizedBrand.endsWith('汽车')) {
                normalizedBrand = normalizedBrand.substring(0, normalizedBrand.length - 2).trim();
            }
            if (textToTranslate.startsWith(normalizedBrand)) {
                textToTranslate = textToTranslate.substring(normalizedBrand.length).trim();
                logger.debug(`翻译服务产品 ID ${product.productId} 字段 ${field} 移除品牌 "${normalizedBrand}" 后变为: "${textToTranslate}"`);
            } else if (textToTranslate.startsWith(originalBrand.trim())) { // 再次尝试原始品牌名
                 textToTranslate = textToTranslate.substring(originalBrand.trim().length).trim();
                 logger.debug(`翻译服务产品 ID ${product.productId} 字段 ${field} 移除品牌 "${originalBrand.trim()}" 后变为: "${textToTranslate}"`);
            }
          }
          // 使用高级重试机制进行翻译，并添加监控
          logger.debug(`翻译服务开始翻译产品 ID ${product.productId} 的字段 ${field} (${language})... 原文（可能已处理）: "${textToTranslate.substring(0,30)}..."`);

          const translationId = `${product.productId}_${field}_${language}_${Date.now()}`;

          // 记录到旧监控系统（保持兼容性）
          translationMonitoringService.recordTranslationStart(translationId, {
            productId: product.productId,
            field,
            language,
            service: 'auto'
          });

          // 注意：字段级别的监控记录已移至语言级别，避免重复计算

          const advancedRetryService = require('./advancedRetryService');
          const retryConfig = advancedRetryService.getTranslationRetryConfig();

          try {
            result = await advancedRetryService.executeWithRetry(
              () => translate(textToTranslate, 'zh', language, {
                productId: product.productId,
                isFirstField: index === 0
              }),
              retryConfig,
              `translation_${language}_${field}`
            );

            if (result && result.success) {
              // 记录到旧监控系统
              translationMonitoringService.recordTranslationSuccess(translationId, result);

              // 字段级别的成功记录已移至语言级别
            } else {
              const error = new Error(result?.error || '翻译失败');
              // 记录到旧监控系统
              translationMonitoringService.recordTranslationFailure(translationId, error);

              // 字段级别的失败记录已移至语言级别
            }
          } catch (error) {
            // 记录到旧监控系统
            translationMonitoringService.recordTranslationFailure(translationId, error);

            // 字段级别的异常记录已移至语言级别
            throw error;
          }
        } else { // --- 其他字段处理 ---
          logger.debug(`翻译服务开始翻译产品 ID ${product.productId} 的字段 ${field} (${language})...`);

          const otherFieldTranslationId = `${product.productId}_${field}_${language}_${Date.now()}`;

          // 其他字段的监控记录已移至语言级别

          const advancedRetryService = require('./advancedRetryService');
          const retryConfig = advancedRetryService.getTranslationRetryConfig();

          try {
            result = await advancedRetryService.executeWithRetry(
              () => translate(originalContent, 'zh', language, {
                productId: product.productId,
                isFirstField: index === 0
              }),
              retryConfig,
              `translation_${language}_${field}`
            );

            // 其他字段的翻译结果记录已移至语言级别
          } catch (error) {
            // 其他字段的异常记录已移至语言级别
            throw error;
          }
        }

        // 处理API翻译结果 (品牌字段已直接处理，所以这里不需要再判断)
        if (result && result.success) {
            logger.debug(`翻译服务产品 ID ${product.productId} 字段 ${field} (${language}) API翻译成功: "${result.translatedText.substring(0, 30)}${result.translatedText.length > 30 ? '...' : ''}"`);
            return {
              field,
              success: true,
              translatedText: result.translatedText
            };
        } else {
            logger.simpleError(`翻译服务产品 ID ${product.productId} (${language}) 失败: 必翻字段 ${field} API 翻译失败: ${result?.error || '未知'}`);
            return {
              field,
              success: false,
              error: `必翻字段 ${field} API 翻译失败: ${result?.error || '未知'}`
            };
        }
      } catch (error) {
        return {
          field,
          success: false,
          error: `必翻字段 ${field} 翻译执行出错: ${error.message}`
        };
      }
    }); // 结束字段并行处理

    // 等待所有字段翻译完成
    const fieldResults = await Promise.all(fieldTranslationPromises);

    // 处理并行翻译结果
    for (const fieldResult of fieldResults) {
      if (fieldResult.success) {
        translations[fieldResult.field] = fieldResult.translatedText;
        translationSuccessCount++;
      } else {
        allRequiredFieldsValidAndTranslated = false;
        translationFailCount++;
        validationFailReason = fieldResult.error;
        console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 失败: ${fieldResult.error}`);
        break; // 有字段翻译失败，直接失败
      }
    }
    
    logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 必翻字段检查与翻译尝试完成: API成功 ${translationSuccessCount}, 失败 ${translationFailCount}`);
    
    // 如果必翻字段检查或翻译失败，触发错误恢复
    if (!allRequiredFieldsValidAndTranslated) {
      console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 因 "${validationFailReason}" 不保存结果，状态保持 false`);

      // 触发翻译失败回滚
      const ErrorRecoveryService = require('./errorRecoveryService');
      try {
        await ErrorRecoveryService.handleTranslationFailureRollback(
          'product',
          product.productId,
          language,
          new Error(validationFailReason)
        );
      } catch (rollbackError) {
        console.error(`[翻译服务] 翻译失败回滚出错:`, rollbackError);
      }

      return {
        success: false,
        error: validationFailReason || 'Validation or API translation failed for required fields',
        translationStats: { required: requiredFields.length, success: translationSuccessCount, fail: translationFailCount + (validationFailReason.includes('为空') ? 1: 0) }
      };
    }
    
    // 2. 尝试保存数据
    if (Object.keys(translations).length === requiredFields.length) {
      logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 必翻字段处理成功，准备保存到 ${language} 表...`);

      let savedRecord;
      try {
          logger.debug(`翻译服务开始调用 upsertProductTranslation 保存产品 ID ${product.productId} 的 ${language} 翻译数据...`);
          savedRecord = await upsertProductTranslation(product.productId, language, translations, { transaction, ...options });
          logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 数据已成功保存到 ${language} 表`);
      } catch (upsertError) {
          // 处理特殊错误类型：ConfigCarNotFoundError
          if (upsertError.name === 'ConfigCarNotFoundError') {
              const missingModelId = upsertError.modelId || product.modelId;
              const errorMsg = `配置车型不存在: 产品使用了 modelId=${missingModelId}，但在 config_car 表中找不到对应记录`;
              console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 保存失败: ${errorMsg}`);
              
              // 返回特殊标记的错误，表明是配置车型缺失而不是普通翻译错误
              return { 
                success: false, 
                  error: `Failed to save translation: ${upsertError.message}`,
                  modelIdError: true,
                  missingModelId: missingModelId,
                  translationStats: { required: requiredFields.length, success: translationSuccessCount, fail: translationFailCount }
              };
          }
          
          console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 保存到 ${language} 表时失败:`, upsertError);
          return {
              success: false,
              error: `Failed to save translation: ${upsertError.message}`,
              translationStats: { required: requiredFields.length, success: translationSuccessCount, fail: translationFailCount }
          };
      }

      // 3. 进行智能翻译验证
      let translationValidationPassed = true;
      logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 进行智能翻译验证...`);

      // 确保 savedRecord 存在再进行检查
      if (!savedRecord) {
          translationValidationPassed = false;
          validationFailReason = '翻译验证失败：未能获取到保存后的记录';
          console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) ${validationFailReason}`);
      } else {
          // 如果是产品修改翻译，跳过完整性验证
          if (options.isProductUpdate) {
            console.log(`[产品修改翻译] 产品 ID ${product.productId} (${language}) 跳过翻译验证`);
            translationValidationPassed = true;
          } else {
            // 产品上架翻译进行完整验证
            const validationResult = await validateTranslationQuality(product, savedRecord, language, requiredFields);

            if (!validationResult.isValid) {
              translationValidationPassed = false;
              validationFailReason = `翻译验证失败：${validationResult.reason}`;
              console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) ${validationFailReason}`);

              // 记录详细的验证问题
              if (validationResult.issues && validationResult.issues.length > 0) {
                console.error(`[翻译服务] 验证问题详情: ${validationResult.issues.join(', ')}`);
              }
            } else {
              logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 翻译验证通过`);
            }
          }
      }

      // 4. 根据翻译类型决定是否更新状态字段
      if (translationValidationPassed) {
        logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 翻译验证通过`);

        let updateResult = { success: true, testAllStatus: null };

        // 如果是产品修改翻译，跳过状态更新
        if (isProductUpdate) {
          console.log(`[产品修改翻译] 产品 ID ${product.productId} (${language}) 跳过状态更新`);
        } else {
          // 只有产品上架翻译才更新状态
          logger.debug(`翻译服务产品 ID ${product.productId} (${language}) 原子性更新状态`);
          updateResult = await updateProductTranslationStatusAtomic(product.productId, language, true, { transaction });

          if (!updateResult.success) {
            console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 状态更新失败: ${updateResult.error}`);
            return {
              success: false,
              error: `状态更新失败: ${updateResult.error}`,
              savedRecord,
              translationStats: {
                required: requiredFields.length,
                success: translationSuccessCount,
                fail: translationFailCount,
                validated: true
              }
            };
          }
        }

        return {
          success: true,
          message: isProductUpdate ? 'Product update translation completed successfully.' : 'Translation completed successfully and validated.',
          savedRecord,
          testAllStatus: updateResult.testAllStatus,
          translationStats: {
            required: requiredFields.length,
            success: translationSuccessCount,
            fail: translationFailCount,
            validated: true
          }
        };
      } else {
        console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 翻译验证未通过 (原因: ${validationFailReason})，主表状态保持当前值`);

        return {
          success: false,
          error: validationFailReason || 'Translation validation failed',
          savedRecord,
          translationStats: {
            required: requiredFields.length,
            success: translationSuccessCount,
            fail: translationFailCount,
            validated: false
          }
        };
      }

    } else {
      // 这个分支理论上不应该进入
      console.error(`[翻译服务] 产品 ID ${product.productId} (${language}) 出现意外情况，必翻字段处理成功但翻译结果数量不足`);
      return {
        success: false,
        error: 'Unexpected state: required fields processed but translation count mismatch',
        translationStats: { required: requiredFields.length, success: translationSuccessCount, fail: translationFailCount }
      };
    }

  } catch (error) {
    console.error(`[翻译服务] 产品翻译检查失败 (ID: ${product?.productId || '未知'}, 语言: ${language}):`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 更新产品翻译状态
 * @param {number} productId - 产品ID
 * @param {string} language - 目标语言
 * @param {boolean} status - 翻译状态 true=已翻译，false=未翻译
 * @returns {Promise<boolean>} - 更新结果
 */
const updateProductTranslationStatus = async (productId, language, status = true) => {
  try {
    if (!isSupportedLanguage(language)) {
      throw new Error(`不支持的语言代码: ${language}`);
    }

    const statusField = `test_${language}`;
    await Product.update(
      { [statusField]: status },
      { where: { productId: productId } }
    );

    console.log(`产品ID ${productId} 的 ${language} 翻译状态已更新为 ${status ? '已翻译' : '未翻译'}`);
    return true;
  } catch (error) {
    console.error(`更新产品ID ${productId} 的 ${language} 翻译状态失败`, error);
    throw error;
  }
};

/**
 * 更新配置车辆的翻译状态
 * @param {number} configCarId - 配置车辆ID
 * @param {string} language - 目标语言
 * @param {boolean} status - 翻译状态 true=已翻译，false=未翻译
 * @returns {Promise<boolean>} - 成功返回true
 */
const updateConfigCarTransStatus = async (configCarId, language, status = true) => {
  const statusField = getConfigCarStatusField(language);
  if (!statusField) return;

  const logger = require('../utils/logger');

  try {
    await db.ConfigCar.update(
      { [statusField]: status },
      { where: { id: configCarId } }
    );
    logger.debug(`更新 ConfigCar ID ${configCarId} 的 ${statusField} 状态为 ${status}`);
    return true;
  } catch (error) {
    logger.simpleError(`更新 ConfigCar ${statusField} 状态失败`, error);
    return false;
  }
};

// --- 重新添加多语言分发函数 ---
const updateMultiLanguageTranslations = async (type, recordId, fieldsToTranslate, languages = Object.values(LANGUAGES)) => {
  const results = {};
  const recordIdentifier = type === 'product' ? `Product ${recordId}` : `ConfigCar ${recordId}`;
  let allSuccess = true;

  console.log(`[多语言翻译] 开始处理 ${recordIdentifier} 的多语言翻译，目标语言: ${languages.join(', ')}`);
  
  for (const lang of languages.filter(isSupportedLanguage)) {
    let success = false;
    try {
      console.log(`[多语言翻译][${recordIdentifier}] 正在处理 ${lang}...`);
      // 根据不同类型调用不同的更新函数
      if (type === 'product') {
        // 对于Product，首先获取产品记录
        const product = await Product.findByPk(recordId);
        if (!product) {
          console.error(`[多语言翻译] 找不到产品 ${recordId}`);
          results[lang] = { success: false, error: `找不到产品 ${recordId}` };
          allSuccess = false;
          continue;
        }
        
        // 使用检查函数处理翻译
        const updateResult = await updateProductTranslationWithCheck(product, lang);
        success = updateResult.success;
        results[lang] = updateResult;
      } else if (type === 'configCar') {
        // 对于ConfigCar，首先获取配置车辆记录
        const configCar = await ConfigCar.findOne({ where: { modelId: recordId } });
        if (!configCar) {
          console.error(`[多语言翻译] 找不到配置车辆 ${recordId}`);
          results[lang] = { success: false, error: `找不到配置车辆 ${recordId}` };
          allSuccess = false;
          continue;
        }
        
        // 使用检查函数处理翻译
        const updateResult = await updateConfigCarTranslationWithCheck(configCar, lang, recordId);
        success = updateResult.success;
        results[lang] = updateResult;
      } else {
        console.error(`[多语言翻译] 不支持的类型: ${type}`);
        results[lang] = { success: false, error: `不支持的类型: ${type}` };
        allSuccess = false;
        continue;
      }
      
      console.log(`[多语言翻译][${recordIdentifier}] ${lang} 处理完成. 结果: ${success ? '成功' : '失败'}`);
      
      if (!success) {
        allSuccess = false;
      }
    } catch (error) {
      console.error(`[多语言翻译][${recordIdentifier}] 处理 ${lang} 时出错:`, error);
      results[lang] = { success: false, error: error.message };
      allSuccess = false;
    }
  }
  
  console.log(`[多语言翻译] ${recordIdentifier} 的所有语言处理完成，总体结果: ${allSuccess ? '全部成功' : '部分或全部失败'}`);
  
  return {
    success: allSuccess,
    recordId,
    results
  };
};

/**
 * 检查并更新多语言翻译状态
 * @param {Object} item - 产品或配置对象
 * @param {string} type - 类型，'product'或'configCar'
 * @param {Array<string>} languages - 语言列表，默认为所有支持的语言
 * @returns {Promise<Object>} - 翻译结果
 */
const updateMultiLanguageTranslationsWithCheck = async (
  item, 
  type, 
  languages = Object.values(LANGUAGES)
) => {
  const results = {};
  
  if (!item) {
    return { success: false, error: '未提供有效对象' };
  }
  
  try {
    // 并行翻译所有语言（性能优化）
    console.log(`[翻译服务] 开始并行翻译 ${type} 的 ${languages.length} 种语言: ${languages.join(', ')}`);

    const translationPromises = languages.map(async (language) => {
      try {
        if (!isSupportedLanguage(language)) {
          return {
            language,
            result: { success: false, error: `不支持的语言: ${language}` },
            success: true // 这里返回true是因为这不算翻译失败，只是跳过
          };
        }

        let result;

        if (type === 'product') {
          result = await updateProductTranslationWithCheck(item, language);
        } else if (type === 'configCar') {
          result = await updateConfigCarTranslationWithCheck(item, language);
        } else {
          result = { success: false, error: `不支持的类型: ${type}` };
        }

        return {
          language,
          result: result,
          success: true
        };
      } catch (error) {
        console.error(`[翻译服务] ${type} 语言 ${language} 翻译异常:`, error.message);
        return {
          language,
          result: { success: false, error: error.message },
          success: true // 继续处理其他语言
        };
      }
    });

    // 等待所有语言翻译完成
    const translationResults = await Promise.all(translationPromises);

    // 处理翻译结果
    for (const { language, result } of translationResults) {
      results[language] = result;
    }

    return { success: true, results };
  } catch (error) {
    console.error(`[翻译服务] 多语言翻译更新失败:`, error);
    return { success: false, error: error.message, results };
  }
};

/**
 * 更新产品翻译内容
 * @param {number} productId - 产品ID
 * @param {object} translations - 翻译内容
 * @param {string} language - 目标语言
 * @returns {Promise<boolean>} - 成功返回true
 */
const updateProductTranslation = async (productId, translations, language = LANGUAGES.EN) => {
  try {
    if (!isSupportedLanguage(language)) {
      throw new Error(`不支持的语言代码: ${language}`);
    }

    // 保存翻译结果
    await upsertProductTranslation(productId, language, translations);
    
    // 更新产品翻译状态
    // Ensure Product model is available in scope
    const { Product } = require('../models'); 
    await Product.update(
      { [getProductStatusField(language)]: true },
      { where: { productId } } // 使用正确的productId字段作为查询条件
    );

    console.log(`产品ID ${productId} 的 ${language} 翻译更新成功`);
    return true;
  } catch (error) {
    console.error(`更新产品ID ${productId} 的 ${language} 翻译失败:`, error);
    // Return false on failure as indicated by updateMultiLanguageTranslations usage
    return false; 
  }
};

/**
 * 为产品检查并更新多语言翻译
 * @param {Object} product - 产品对象
 * @param {Array<string>} languages - 语言代码列表
 * @returns {Promise<Object>} - 翻译结果
 */
const updateProductMultiLanguageTranslationsWithCheck = async (product, languages = Object.values(LANGUAGES)) => {
  if (!product || !product.id) {
    return { success: false, error: '无效的产品对象' };
  }

    const results = {};
  let hasError = false;

  try {
    // 并行翻译所有语言（性能优化）
    console.log(`[翻译服务] 开始并行翻译产品 ${product.id} 的 ${languages.length} 种语言: ${languages.join(', ')}`);

    const translationPromises = languages.map(async (language) => {
      try {
        if (!isSupportedLanguage(language)) {
          return {
            language,
            result: { success: false, error: `不支持的语言: ${language}` },
            success: false
          };
        }

        // updateProductTranslationWithCheck should be defined later in the file
        const result = await updateProductTranslationWithCheck(product, language);

        return {
          language,
          result: result,
          success: result.success
        };
      } catch (error) {
        console.error(`[翻译服务] 产品 ${product.id} 语言 ${language} 翻译异常:`, error.message);
        return {
          language,
          result: { success: false, error: error.message },
          success: false
        };
      }
    });

    // 等待所有语言翻译完成
    const translationResults = await Promise.all(translationPromises);

    // 处理翻译结果
    for (const { language, result, success } of translationResults) {
      results[language] = result;

      if (!success) {
        hasError = true;
      }
    }

    return {
      success: !hasError,
      results
    };
  } catch (error) {
    console.error(`[翻译服务] 产品多语言翻译失败 (ID: ${product.id}):`, error);
    return {
      success: false,
      error: error.message,
      results // Include partial results if any
    };
  }
};

/**
 * 为配置车型检查并更新多语言翻译
 * @param {Object} configCar - 配置车型对象
 * @param {Array<string>} languages - 语言代码列表
 * @returns {Promise<Object>} - 翻译结果
 */
const updateConfigCarMultiLanguageTranslationsWithCheck = async (configCar, languages = Object.values(LANGUAGES)) => {
  if (!configCar || !configCar.id || !configCar.modelId) {
    return { success: false, error: '无效的配置车型对象或缺少modelId' };
  }

  const results = {};
  let hasError = false;

  try {
    // 并行翻译所有语言（性能优化）
    console.log(`[翻译服务] 开始并行翻译配置车型 ${configCar.id} (ModelID: ${configCar.modelId}) 的 ${languages.length} 种语言: ${languages.join(', ')}`);

    const translationPromises = languages.map(async (language) => {
      try {
        if (!isSupportedLanguage(language)) {
          return {
            language,
            result: { success: false, error: `不支持的语言: ${language}` },
            success: false
          };
        }

        // updateConfigCarTranslationWithCheck should be defined earlier in the file
        // Pass modelId which is required by the check function
        const result = await updateConfigCarTranslationWithCheck(configCar, language, configCar.modelId);

        return {
          language,
          result: result,
          success: result.success
        };
      } catch (error) {
        console.error(`[翻译服务] 配置车型 ${configCar.id} 语言 ${language} 翻译异常:`, error.message);
        return {
          language,
          result: { success: false, error: error.message },
          success: false
        };
      }
    });

    // 等待所有语言翻译完成
    const translationResults = await Promise.all(translationPromises);

    // 处理翻译结果
    for (const { language, result, success } of translationResults) {
      results[language] = result;

      if (!success) {
        hasError = true;
      }
    }

    return {
      success: !hasError,
      results
    };
  } catch (error) {
    console.error(`[翻译服务] 配置车型多语言翻译失败 (ID: ${configCar.id}, ModelID: ${configCar.modelId}):`, error);
    return {
      success: false,
      error: error.message,
      results // Include partial results if any
    };
  }
};

/**
 * 检查产品翻译字段并更新test_xx状态
 * @param {Object} product - 产品对象
 * @param {string} language - 目标语言代码
 * @returns {Promise<Object>} - 包含成功状态和结果的对象
 */
const checkAndUpdateProductTestStatus = async (product, language = LANGUAGES.EN) => {
  if (!isSupportedLanguage(language)) {
    return { success: false, error: `不支持的语言: ${language}` };
  }

  try {
    const statusField = getProductStatusField(language);
    const TargetModel = PRODUCT_MODELS[language];
    
    if (!TargetModel) {
      return { success: false, error: `未找到语言 ${language} 对应的模型` };
    }
    
    console.log(`[翻译状态检查] 开始检查产品 ID ${product.productId} 的 ${language} 翻译记录`);
    
    // 查找该产品的翻译记录
    const translationRecord = await TargetModel.findOne({
      where: { productId: product.productId }
    });
    
    if (!translationRecord) {
      console.log(`[翻译状态检查] 产品ID ${product.productId} 没有 ${language} 翻译记录，设置 ${statusField} 为 false`);
      await Product.update(
        { [statusField]: false },
        { where: { productId: product.productId } }
      );
      return { 
        success: true, 
        updated: true, 
        newStatus: false, 
        message: `无翻译记录，${statusField} 已更新为 false` 
      };
    }
    
    console.log(`[翻译状态检查] 已找到产品ID ${product.productId} 的 ${language} 翻译记录，检查字段完整性`);
    
    // 修改：使用标准字段列表而不是从模型中获取
    const excludeFields = ['id', 'createdAt', 'updatedAt', 'status'];
    const ignoreFieldsSet = new Set(excludeFields);
    
    // 使用getAllProductFields获取标准字段列表
    const allFieldsToCheck = getAllProductFields().filter(field => !ignoreFieldsSet.has(field));
    
    console.log(`[翻译状态检查] 产品ID ${product.productId} (${language}) 检查 ${allFieldsToCheck.length} 个字段`);
    console.log(`[翻译状态检查] 字段列表: ${allFieldsToCheck.join(', ')}`);
    
    // 使用改进的字段检查方法
    const record = translationRecord.toJSON();
    const emptyFields = [];
    
    for (const field of allFieldsToCheck) {
      if (field in record) {
        const value = record[field];
        if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
          emptyFields.push(field);
        }
      } else {
        // 如果字段不存在于保存的记录中，也视为空
        emptyFields.push(`${field}(不存在)`);
      }
    }
    
    const allFieldsHaveValue = emptyFields.length === 0;
    
    if (!allFieldsHaveValue) {
      console.error(`[翻译状态检查] 产品ID ${product.productId} (${language}) 发现 ${emptyFields.length} 个空字段: ${emptyFields.join(', ')}`);
    }
    
    // 更新产品的test_xx状态
    await Product.update(
      { [statusField]: allFieldsHaveValue },
      { where: { productId: product.productId } }
    );
    
    // 移除对 checkAndUpdateProductTestAll 的调用，由外层统一处理
    
    console.log(`[翻译状态检查] 产品ID ${product.productId} 的 ${language} 翻译状态已检查并更新为 ${allFieldsHaveValue}`);
    
    return {
      success: true,
      updated: true,
      recordChecked: record,
      newStatus: allFieldsHaveValue,
      emptyFields: emptyFields.length > 0 ? emptyFields : null,
      message: `${statusField} 已更新为 ${allFieldsHaveValue}`
    };
    
  } catch (error) {
    console.error(`[翻译状态检查] 检查产品ID ${product.productId} 的 ${language} 翻译状态时出错:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 检查配置车翻译字段并更新peizhi_xx状态
 * @param {string} modelId - 配置车模型ID
 * @returns {Promise<Object>} - 包含成功状态和结果的对象
 */
const checkAndUpdateConfigCarTestStatus = async (modelId, existingConfigCar = null) => {
  try {
    if (!modelId) {
      console.error('checkAndUpdateConfigCarTestStatus: 缺少必须的modelId参数');
      return { success: false, error: '缺少必须的modelId参数' };
    }

    // 1. 获取主记录 (如果未传入，则查询)
    let configCar = existingConfigCar;
    if (!configCar) {
      console.log(`[配置翻译状态检查] 未传入configCar实例，根据modelId=${modelId}查询...`);
      configCar = await ConfigCar.findOne({
        where: {
          [Op.or]: [
            { modelId: modelId },
            { '车型_id': modelId }
          ]
        }
      });
    }

    if (!configCar) {
      console.error(`[配置翻译状态检查] 未找到modelId为${modelId}的配置车型记录`);
      return { success: false, error: `未找到modelId为${modelId}的配置车型记录` };
    }

    // 使用传入或查询到的记录
    const currentModelId = configCar.modelId || configCar['车型_id']; // 确保使用正确的ID进行后续操作
    console.log(`[配置翻译状态检查] 开始检查 modelId=${currentModelId} (传入ID: ${modelId})`);


    const originalData = configCar.get({ plain: true });
    const configCarAttributes = Object.keys(ConfigCar.rawAttributes);
    const peizhi_all_exists = configCarAttributes.includes('peizhi_all');
    const supportedLanguages = Object.values(LANGUAGES);
    const langStatus = {}; // 记录每个语言的最终检查状态
    const updatesToApply = {}; // 收集需要更新到数据库的状态
    let allPassed = true; // 最终的 peizhi_all 状态

    // 2. 检查每种语言的翻译状态，收集结果，不立即更新
    for (const lang of supportedLanguages) {
      const statusField = getConfigCarStatusField(lang);
      const TargetModel = CONFIG_CAR_MODELS[lang];
      const statusFieldExists = configCarAttributes.includes(statusField);
      let currentLangPassed = false; // 当前语言是否通过检查

      if (!statusFieldExists) {
        // console.warn(`ConfigCar模型中不存在字段 ${statusField}，将跳过该字段的更新`); //减少日志
      }
    
    if (!TargetModel) {
        console.warn(`[配置翻译状态检查] 未找到${lang}对应的模型`);
        langStatus[lang] = false;
        allPassed = false;
        // 检查是否需要更新状态
        if (statusFieldExists && configCar[statusField] !== false) {
            updatesToApply[statusField] = false; // 记录需要更新为 false
        }
        continue;
      }

      const modelAttributeNames = new Set(Object.keys(TargetModel.rawAttributes));
      const translatableFieldsToCheck = getConfigCarTranslatableFields();
      const attributesToSelect = ['id'];
      if (modelAttributeNames.has('modelId')) attributesToSelect.push('modelId');
      if (modelAttributeNames.has('车型_id')) attributesToSelect.push('车型_id');
      translatableFieldsToCheck.forEach(field => {
        if (modelAttributeNames.has(field)) {
          attributesToSelect.push(field);
        }
      });
      const uniqueAttributesToSelect = [...new Set(attributesToSelect)];

    const translationRecord = await TargetModel.findOne({
        attributes: uniqueAttributesToSelect,
        where: {
          [Op.or]: [
            { modelId: currentModelId }, // 使用从记录中获取的ID
            { '车型_id': currentModelId } // 使用从记录中获取的ID
          ]
        }
    });
    
    if (!translationRecord) {
        // console.log(`[配置翻译状态检查] 配置车型${currentModelId}没有${lang}翻译记录，状态设为false`); //减少日志
        currentLangPassed = false;
      } else {
        const translationData = translationRecord.get({ plain: true });
        let allFieldsTranslated = true;
        const missingFields = [];
        for (const field of translatableFieldsToCheck) {
          if (modelAttributeNames.has(field)) {
            const originalValue = originalData[field];
            if (originalValue && typeof originalValue === 'string' && originalValue.trim() !== '' && containsChinese(originalValue)) {
              const translatedValue = translationData[field];
              if (!translatedValue || typeof translatedValue !== 'string' || translatedValue.trim() === '') {
                allFieldsTranslated = false;
                missingFields.push(field);
                // console.log(`[配置翻译状态检查] modelId=${currentModelId}, lang=${lang}, 字段 ${field} 缺失或翻译为空`); //减少日志
              }
            }
          }
        }
        currentLangPassed = allFieldsTranslated;
        if (!currentLangPassed) {
           console.log(`[配置翻译状态检查] 配置车型${currentModelId}的${lang}翻译检查不完整，缺少字段: ${missingFields.join(', ')}`);
        }
      }
      
      langStatus[lang] = currentLangPassed; // 记录最终状态
      
      // 如果当前语言检查不通过，则总状态也不通过
      if (!currentLangPassed) {
        allPassed = false;
      }
      
      // 如果状态字段存在，且数据库中的状态与当前检查结果不同，则记录下来准备更新
      if (statusFieldExists && configCar[statusField] !== currentLangPassed) {
        updatesToApply[statusField] = currentLangPassed;
      }
    } // 结束语言循环检查

    // 3. 一次性更新数据库
    let statusUpdated = false;
    // 检查 peizhi_all 是否需要更新
    if (peizhi_all_exists && configCar.peizhi_all !== allPassed) {
      updatesToApply.peizhi_all = allPassed;
    }

    // 如果有任何状态需要更新，则执行一次数据库操作
    if (Object.keys(updatesToApply).length > 0) {
      console.log(`[配置翻译状态检查] 准备更新 ${currentModelId} 的状态: ${JSON.stringify(updatesToApply)}`);
      try {
    await ConfigCar.update(
          updatesToApply,
          {
            where: {
              id: configCar.id // 使用主键更新，更准确安全
            }
          }
        );
        statusUpdated = true;
        console.log(`[配置翻译状态检查] ${currentModelId} 的状态更新成功`);
      } catch (updateErr) {
        console.error(`[配置翻译状态检查] 更新 ${currentModelId} (ID: ${configCar.id}) 状态失败: ${updateErr.message}`);
        statusUpdated = false; 
      }
    } else {
      console.log(`[配置翻译状态检查] ${currentModelId} (ID: ${configCar.id}) 的状态无需更新`);
    }
    
    return {
      success: true,
      modelId: currentModelId,
      statusUpdated, // 指示数据库记录是否实际被更新
      langStatus,    // 各语言的检查结果
      peizhi_all: allPassed // 计算出的总状态
    };
  } catch (error) {
    // 区分是查找错误还是检查/更新错误
    const errorMessage = `[配置翻译状态检查] 处理配置车型${modelId}状态时出错: ${error.message}`;
    console.error(errorMessage, error.stack); // 记录堆栈信息便于调试
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * 原子性更新产品翻译状态
 * 解决并发竞态条件问题，确保状态更新的一致性
 * @param {string} productId - 产品ID
 * @param {string} language - 语言代码
 * @param {boolean} status - 翻译状态
 * @param {Object} options - 选项，包含事务等
 * @returns {Promise<Object>} - 更新结果
 */
const updateProductTranslationStatusAtomic = async (productId, language, status, options = {}) => {
  const { transaction } = options;

  try {
    console.log(`[状态更新] 原子性更新产品 ${productId} 的 ${language} 状态为 ${status}`);

    // 使用乐观锁而非悲观锁，支持并行翻译
    const executeUpdate = async (tx) => {
      const statusField = getProductStatusField(language);

      // 1. 直接更新语言状态字段（无锁，支持并行）
      const [affectedRows] = await db.Product.update(
        { [statusField]: status },
        {
          where: { productId: productId },
          transaction: tx
        }
      );

      if (affectedRows === 0) {
        // 先检查产品是否真的存在
        const productExists = await db.Product.findOne({
          where: { productId: productId },
          transaction: tx
        });

        if (!productExists) {
          console.error(`[状态更新] 产品 ${productId} 在数据库中不存在`);
          throw new Error(`产品 ${productId} 在数据库中不存在`);
        } else {
          console.warn(`[状态更新] 产品 ${productId} 存在但更新失败，可能是并发冲突，跳过此次更新`);
          // 不抛出错误，允许继续执行，但需要返回当前的test_all状态
          return {
            success: true,
            updated: false,
            reason: 'concurrent_conflict',
            testAllStatus: productExists.test_all, // 返回当前的test_all状态
            message: `产品 ${productId} 更新跳过（可能的并发冲突）`
          };
        }
      }

      console.log(`[状态更新] 产品 ${productId} 的 ${statusField} 状态已更新为 ${status}`);

      // 4. 重新获取更新后的产品记录
      const updatedProduct = await db.Product.findOne({
        where: { productId: productId },
        transaction: tx
      });

      // 5. 检查并更新test_all状态
      let allStatusesTrue = true;
      const statusCheck = {};

      for (const lang of Object.values(LANGUAGES)) {
        const langStatusField = getProductStatusField(lang);
        const langStatus = updatedProduct[langStatusField];
        statusCheck[lang] = langStatus;

        if (!langStatus) {
          allStatusesTrue = false;
        }
      }

      console.log(`[状态更新] 产品 ${productId} 状态检查:`, statusCheck);

      // 6. 原子性更新test_all状态
      let testAllUpdated = false;
      let newTestAllStatus = updatedProduct.test_all;

      if (allStatusesTrue && !updatedProduct.test_all) {
        // 所有语言都完成，且test_all为false，更新为true
        await db.Product.update(
          { test_all: true },
          {
            where: { productId: productId },
            transaction: tx
          }
        );
        newTestAllStatus = true;
        testAllUpdated = true;
        console.log(`[状态更新] 产品 ${productId} 所有语言翻译完成，test_all 更新为 true`);

        // 记录车辆翻译完成到监控系统
        try {
          const translationManager = require('./core/TranslationManager');
          if (translationManager.monitor) {
            translationManager.monitor.recordVehicleTranslationComplete(productId, {
              allLanguagesComplete: true,
              statusCheck: statusCheck
            });
          }
        } catch (monitorError) {
          console.warn('[翻译监控] 记录车辆翻译完成失败:', monitorError.message);
        }
      } else if (!allStatusesTrue && updatedProduct.test_all) {
        // 有语言未完成，但test_all为true，更新为false
        await db.Product.update(
          { test_all: false },
          {
            where: { productId: productId },
            transaction: tx
          }
        );
        newTestAllStatus = false;
        testAllUpdated = true;
        console.log(`[状态更新] 产品 ${productId} 部分语言未完成，test_all 更新为 false`);
      }

      return {
        success: true,
        updated: true,
        testAllStatus: newTestAllStatus,
        testAllUpdated: testAllUpdated,
        allLanguagesComplete: allStatusesTrue,
        statusCheck: statusCheck
      };
    };

    // 如果已有事务，使用现有事务；否则创建新事务
    if (transaction) {
      return await executeUpdate(transaction);
    } else {
      return await db.sequelize.transaction(executeUpdate);
    }

  } catch (error) {
    console.error(`[状态更新] 原子性更新产品 ${productId} 状态失败:`, error);
    return {
      success: false,
      error: error.message,
      updated: false
    };
  }
};

/**
 * 检查并更新产品的test_all字段（已废弃，使用updateProductTranslationStatusAtomic替代）
 * @deprecated 使用 updateProductTranslationStatusAtomic 替代
 */
const checkAndUpdateProductTestAll = async (productId) => {
  console.warn(`[翻译服务] checkAndUpdateProductTestAll 已废弃，请使用 updateProductTranslationStatusAtomic`);

  try {
    const result = await updateProductTranslationStatusAtomic(productId, 'en', true);
    return result.testAllStatus;
  } catch (error) {
    console.error(`[翻译服务] 检查更新产品(${productId})的test_all状态时出错:`, error);
    return false;
  }
};

/**
 * 获取所有产品字段，确保所有语言检查相同数量的字段
 * @returns {Array<string>} 所有应该被检查的产品字段名称
 */
const getAllProductFields = () => {
  // 这是所有语言表中应该检查的完整字段列表
  return [
    'productId', 'modelId', 'title', 'brand', 'color', 'interiorColor', 'condition',
    'status', 'manufactureDate', 'firstRegistration', 'mileage', 'transferCount',
    'price', 'priceUsd', 'mainImage', 'images', 'dealerId',
    // 新增的车辆规格字段
    'energyType', 'bodyStructure', 'gearboxType', 'driveType'
    // 添加任何其他应该检查的字段
  ];
};

/**
 * 更新配置车翻译，检查中文内容（高级版本，直接接收参数）
 * @param {number} modelId - 车型ID
 * @param {string} targetLanguage - 目标语言
 * @param {boolean} forceUpdate - 强制更新
 * @returns {Promise<object>} - 翻译更新结果
 */
const updateConfigCarTranslationDirect = async (modelId, targetLanguage, forceUpdate = false) => {
  const debugMode = true;
  if (debugMode) console.log(`准备检查并更新配置车翻译: modelId=${modelId}, 目标语言=${targetLanguage}`);
  
  if (!modelId) {
    console.error('缺少modelId参数，无法更新翻译');
    return { success: false, message: '缺少modelId参数' };
  }

  try {
    // 检查语言支持
    if (!SUPPORTED_LANGUAGES.includes(targetLanguage)) {
      return { success: false, message: `不支持的语言: ${targetLanguage}` };
    }
    
    // 查询配置车记录
    let configCar = await ConfigCar.findOne({
      where: { 车型_id: modelId }
    });
    
    // 如果没找到，尝试用id查询
    if (!configCar) {
      configCar = await ConfigCar.findOne({
        where: { id: modelId }
      });
    }
    
    if (!configCar) {
      console.error(`未找到modelId为${modelId}的配置车型记录`);
      return { success: false, message: '未找到记录' };
    }
    
    // 获取配置车的全部数据
    const configCarData = configCar.toJSON();
    const fieldsToBeSaved = {};
    const fieldsToTranslate = {};
    
    if (debugMode) console.log(`开始扫描所有字段检查中文内容...`);
    
    // 系统字段，不需要翻译
    const systemFields = ['id', '车型_id', 'createdAt', 'updatedAt', 'peizhi_en', 'peizhi_all', 
      'test_en', 'test_de', 'test_fr', 'test_es', 'test_pt', 'test_it', 'test_nl', 'test_ar', 'test_all'];
    
    // 扫描所有字段，检查是否包含中文
    let chineseFieldCount = 0;
    for (const field in configCarData) {
      // 跳过系统字段
      if (systemFields.includes(field)) continue;
      
      // 保存所有字段值进行翻译
      fieldsToBeSaved[field] = configCarData[field];
      
      // 检查字段值是否包含中文
      const value = configCarData[field];
      if (value && typeof value === 'string' && /[\u4e00-\u9fa5]/.test(value)) {
        fieldsToTranslate[field] = value;
        chineseFieldCount++;
      }
    }
    
    if (debugMode) console.log(`发现${chineseFieldCount}个包含中文的字段需要翻译`);
    
    // 如果没有中文字段，返回
    if (chineseFieldCount === 0) {
      console.log(`配置车型ID ${modelId} 没有需要翻译的中文内容`);
      return { success: true, message: '没有中文内容需要翻译', modelId };
    }
    
    // 确保字段为对象类型
    if (typeof fieldsToBeSaved !== 'object') {
      return { success: false, message: '字段数据类型错误' };
    }
    
    // 调用翻译方法
    const result = await upsertConfigCarTranslation(
      modelId,
      targetLanguage,
      fieldsToTranslate
    );
    
    if (debugMode) console.log(`翻译完成，准备更新状态`);
    
    // 更新翻译状态
    if (result.success) {
      const statusResult = await updateConfigCarTranslationStatus(modelId, targetLanguage, true);
      
      if (!statusResult.success) {
        console.error(`更新配置车型 ${modelId} 的翻译状态失败:`, statusResult.message);
    return {
          ...result, 
          statusUpdateSuccess: false, 
          statusUpdateMessage: statusResult.message 
        };
      }
      
    return {
        ...result, 
        statusUpdateSuccess: true,
        fieldsCount: chineseFieldCount
      };
    }
    
    return result;
  } catch (error) {
    console.error(`更新配置车翻译时出错:`, error);
    return { success: false, message: `更新失败: ${error.message}` };
  }
};

/**
 * 批量翻译多个文本
 * @param {Array<string>} texts - 待翻译文本数组
 * @param {string} from - 源语言，默认为中文
 * @param {string} to - 目标语言，默认为英语
 * @param {number} batchSize - 每批处理的最大文本数量，默认为10
 * @returns {Promise<Array<Object>>} - 翻译结果对象数组
 */
const translateBatch = async (texts, from = 'zh', to = 'en', batchSize = 10) => {
  if (!texts || !Array.isArray(texts) || texts.length === 0) {
    return [];
  }
  
  // 去除空文本
  const validTexts = texts.filter(text => text && typeof text === 'string' && text.trim() !== '');
  if (validTexts.length === 0) {
    return [];
  }
  
  // 如果只有一个文本，直接调用单文本翻译
  if (validTexts.length === 1) {
    const result = await translate(validTexts[0], from, to);
    return [result];
  }
  
  // 结果数组
  const results = [];
  // 待处理文本索引映射
  const indexMap = new Map();
  // 合并后的文本
  let batchedText = '';
  // 特殊分隔符
  const separator = '\n<SPLIT_HERE>\n';
  
  // 预处理文本，记录原始索引
  for (let i = 0; i < validTexts.length; i++) {
    indexMap.set(i, validTexts[i]);
  }
  
  // 按批次处理
  const batches = [];
  for (let i = 0; i < validTexts.length; i += batchSize) {
    const batch = validTexts.slice(i, i + batchSize);
    batches.push(batch);
  }
  
  // 处理每个批次
  for (const batch of batches) {
    // 合并文本
    batchedText = batch.join(separator);
    
    try {
      // 调用翻译API
      const batchResult = await translate(batchedText, from, to);
      
      if (batchResult.success) {
        // 拆分翻译结果
        const translatedParts = batchResult.translatedText.split(separator);
        
        // 确保拆分后的部分数量与输入相同
        if (translatedParts.length === batch.length) {
          // 分配翻译结果
          for (let i = 0; i < batch.length; i++) {
            results.push({
              success: true,
              translatedText: translatedParts[i],
              originalText: batch[i]
            });
          }
        } else {
          // 拆分失败，回退到逐个翻译
          for (const text of batch) {
            const singleResult = await translate(text, from, to);
            results.push(singleResult);
          }
        }
      } else {
        // 翻译失败，回退到逐个翻译
        for (const text of batch) {
          const singleResult = await translate(text, from, to);
          results.push(singleResult);
        }
      }
    } catch (error) {
      // 发生异常，回退到逐个翻译
      for (const text of batch) {
        try {
          const singleResult = await translate(text, from, to);
          results.push(singleResult);
        } catch (innerError) {
          results.push({
            success: false,
            error: innerError.message,
            translatedText: `[${to.toUpperCase()}] ${text}`
          });
        }
      }
    }
  }
  
  return results;
};

/**
 * 并行翻译多个文本
 * @param {Array<string>} texts - 待翻译文本数组
 * @param {string} from - 源语言，默认为中文
 * @param {string} to - 目标语言，默认为英语
 * @param {number} concurrency - 并发数，默认为20
 * @returns {Promise<Array<Object>>} - 翻译结果对象数组
 */
const translateParallel = async (texts, from = 'zh', to = 'en', concurrency = 20) => {
  if (!texts || !Array.isArray(texts) || texts.length === 0) {
    return [];
  }
  
  // 去除空文本
  const validTexts = texts.filter(text => text && typeof text === 'string' && text.trim() !== '');
  if (validTexts.length === 0) {
    return [];
  }
  
  // 创建结果数组，初始化为与输入文本相同长度
  const results = new Array(validTexts.length);
  
  // 创建任务队列
  const queue = validTexts.map((text, index) => ({ text, index }));
  
  // 并发控制函数
  const runBatch = async () => {
    const batchPromises = [];
    // 同时运行concurrency个任务
    for (let i = 0; i < concurrency && queue.length > 0; i++) {
      const task = queue.shift();
      if (task) {
        batchPromises.push(
          translate(task.text, from, to)
            .then(result => {
              // 保存结果到对应位置
              results[task.index] = {
                ...result,
                originalText: task.text
              };
            })
            .catch(error => {
              // 处理失败的情况
              results[task.index] = {
                success: false,
                error: error.message,
                translatedText: `[${to.toUpperCase()}] ${task.text}`,
                originalText: task.text
              };
            })
        );
      }
    }
    
    await Promise.all(batchPromises);
    
    // 如果队列不为空，继续处理下一批
    if (queue.length > 0) {
      return runBatch();
    }
  };
  
  // 开始执行任务
  await runBatch();
  
  return results;
};

/**
 * 并行处理多语言翻译
 * @param {number} modelId - 车型ID
 * @param {Array<string>} languages - 要翻译的目标语言列表
 * @param {number} concurrency - 并发数，默认为10
 * @returns {Promise<Object>} - 包含各语言翻译结果的对象
 */
const upsertConfigCarTranslationParallel = async (modelId, languages = Object.values(LANGUAGES), concurrency = 10) => {
  if (!modelId) {
    console.error(`[配置翻译] 缺少modelId参数`);
    return { success: false, error: `缺少modelId参数` };
  }
  
  // 验证语言列表
  const validLanguages = languages.filter(lang => isSupportedLanguage(lang));
  if (validLanguages.length === 0) {
    console.error(`[配置翻译] 没有有效的目标语言`);
    return { success: false, error: `没有有效的目标语言` };
  }
  
  console.log(`[配置翻译] 开始并行处理 modelId=${modelId} 的多语言翻译，语言: ${validLanguages.join(', ')}`);
  
  // 结果对象
  const results = {};
  
  // 设置最大并发数
  const actualConcurrency = Math.min(concurrency, validLanguages.length);
  console.log(`[配置翻译] 使用 ${actualConcurrency} 个并发任务处理 ${validLanguages.length} 种语言`);
  
  // 创建任务队列
  const queue = [...validLanguages];
  
  // 并发控制函数
  const runBatch = async () => {
    const batchPromises = [];
    
    // 同时运行concurrency个任务
    for (let i = 0; i < actualConcurrency && queue.length > 0; i++) {
      const language = queue.shift();
      if (language) {
        batchPromises.push(
          upsertConfigCarTranslation(modelId, language)
            .then(result => {
              // 保存结果
              results[language] = result;
              console.log(`[配置翻译] 完成 ${language} 翻译: 字段=${result.apiTranslated}/${result.fieldsNeedTranslation}`);
            })
            .catch(error => {
              // 处理失败的情况
              results[language] = {
                success: false,
                error: error.message,
                language
              };
              console.error(`[配置翻译] ${language} 翻译失败: ${error.message}`);
            })
        );
      }
    }
    
    await Promise.all(batchPromises);
    
    // 如果队列不为空，继续处理下一批
    if (queue.length > 0) {
      return runBatch();
    }
  };
  
  // 开始执行任务
  await runBatch();
  
  // 统计成功和失败的语言
  const successLanguages = Object.keys(results).filter(lang => results[lang].success);
  const failedLanguages = Object.keys(results).filter(lang => !results[lang].success);
  
  console.log(`[配置翻译] 多语言处理完成: ${successLanguages.length}个成功, ${failedLanguages.length}个失败`);
  
    return {
    success: failedLanguages.length === 0,
    results,
    successCount: successLanguages.length,
    failedCount: failedLanguages.length,
    successLanguages,
    failedLanguages
  };
};

/**
 * 并行处理多语言产品翻译
 * @param {number} productId - 产品ID
 * @param {Array<string>} languages - 要翻译的目标语言列表
 * @param {number} concurrency - 并发数，默认为10
 * @returns {Promise<Object>} - 包含各语言翻译结果的对象
 */
const upsertProductTranslationParallel = async (productId, languages = Object.values(LANGUAGES), concurrency = 10) => {
  if (!productId) {
    console.error(`[产品翻译] 缺少productId参数`);
    return { success: false, error: `缺少productId参数` };
  }
  
  // 验证语言列表
  const validLanguages = languages.filter(lang => isSupportedLanguage(lang));
  if (validLanguages.length === 0) {
    console.error(`[产品翻译] 没有有效的目标语言`);
    return { success: false, error: `没有有效的目标语言` };
  }
  
  console.log(`[产品翻译] 开始并行处理 productId=${productId} 的多语言翻译，语言: ${validLanguages.join(', ')}`);
  
  // 结果对象
  const results = {};
  
  // 设置最大并发数
  const actualConcurrency = Math.min(concurrency, validLanguages.length);
  console.log(`[产品翻译] 使用 ${actualConcurrency} 个并发任务处理 ${validLanguages.length} 种语言`);
  
  // 创建任务队列
  const queue = [...validLanguages];
  
  // 并发控制函数
  const runBatch = async () => {
    const batchPromises = [];
    
    // 同时运行concurrency个任务
    for (let i = 0; i < actualConcurrency && queue.length > 0; i++) {
      const language = queue.shift();
      if (language) {
        batchPromises.push(
          upsertProductTranslation(productId, language)
            .then(result => {
              // 保存结果
              results[language] = {
                success: true,
                result
              };
              console.log(`[产品翻译] 完成 ${language} 翻译`);
            })
            .catch(error => {
              // 处理失败的情况
              results[language] = {
      success: false,
                error: error.message,
                language
              };
              console.error(`[产品翻译] ${language} 翻译失败: ${error.message}`);
            })
        );
      }
    }
    
    await Promise.all(batchPromises);
    
    // 如果队列不为空，继续处理下一批
    if (queue.length > 0) {
      return runBatch();
    }
  };
  
  // 开始执行任务
  await runBatch();
  
  // 统计成功和失败的语言
  const successLanguages = Object.keys(results).filter(lang => results[lang].success);
  const failedLanguages = Object.keys(results).filter(lang => !results[lang].success);
  
  console.log(`[产品翻译] 多语言处理完成: ${successLanguages.length}个成功, ${failedLanguages.length}个失败`);
  
  return {
    success: failedLanguages.length === 0,
    results,
    successCount: successLanguages.length,
    failedCount: failedLanguages.length,
    successLanguages,
    failedLanguages
  };
};

/**
 * 批量处理多个产品的翻译
 * @param {Array<number>} productIds - 产品ID数组
 * @param {Array<string>} languages - 要翻译的目标语言列表
 * @param {number} maxConcurrentProducts - 最大并发产品数，默认为20
 * @param {number} concurrencyPerProduct - 每个产品的语言并发数，默认为5
 * @returns {Promise<Object>} - 包含各产品翻译结果的对象
 */
const batchProcessProductTranslations = async (
  productIds, 
  languages = Object.values(LANGUAGES), 
  maxConcurrentProducts = 20,
  concurrencyPerProduct = 5
) => {
  if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
    console.error(`[批量翻译] 缺少productIds参数`);
    return { success: false, error: `缺少productIds参数` };
  }
  
  console.log(`[批量翻译] 开始处理 ${productIds.length} 个产品的翻译，目标语言: ${languages.join(', ')}`);
  
  // 创建结果对象
  const results = {};
  
  // 创建任务队列
  const queue = [...productIds];
  
  // 并发控制函数
  const runBatch = async () => {
    const batchPromises = [];
    
    // 同时处理maxConcurrentProducts个产品
    for (let i = 0; i < maxConcurrentProducts && queue.length > 0; i++) {
      const productId = queue.shift();
      if (productId) {
        batchPromises.push(
          upsertProductTranslationParallel(productId, languages, concurrencyPerProduct)
            .then(result => {
              // 保存结果
              results[productId] = {
                success: true,
                result
              };
              console.log(`[批量翻译] 完成产品 ${productId} 的翻译`);
            })
            .catch(error => {
              // 处理失败的情况
              results[productId] = {
                success: false,
                error: error.message,
                productId
              };
              console.error(`[批量翻译] 产品 ${productId} 翻译失败: ${error.message}`);
            })
        );
      }
    }
    
    await Promise.all(batchPromises);
    
    // 如果队列不为空，继续处理下一批
    if (queue.length > 0) {
      return runBatch();
    }
  };
  
  // 开始执行任务
  await runBatch();
  
  // 统计成功和失败的产品
  const successProducts = Object.keys(results).filter(id => results[id].success);
  const failedProducts = Object.keys(results).filter(id => !results[id].success);
  
  console.log(`[批量翻译] 多产品处理完成: ${successProducts.length}个成功, ${failedProducts.length}个失败`);
  
  return {
    success: failedProducts.length === 0,
    results,
    successCount: successProducts.length,
    failedCount: failedProducts.length,
    successProducts,
    failedProducts
  };
};

/**
 * 批量处理多个车型配置的翻译
 * @param {Array<number>} modelIds - 车型ID数组
 * @param {Array<string>} languages - 要翻译的目标语言列表
 * @param {number} maxConcurrentModels - 最大并发车型数，默认为20
 * @param {number} concurrencyPerModel - 每个车型的语言并发数，默认为5
 * @returns {Promise<Object>} - 包含各车型翻译结果的对象
 */
const batchProcessConfigCarTranslations = async (
  modelIds, 
  languages = Object.values(LANGUAGES), 
  maxConcurrentModels = 20,
  concurrencyPerModel = 5
) => {
  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    console.error(`[批量翻译] 缺少modelIds参数`);
    return { success: false, error: `缺少modelIds参数` };
  }
  
  console.log(`[批量翻译] 开始处理 ${modelIds.length} 个车型的翻译，目标语言: ${languages.join(', ')}`);
  
  // 创建结果对象
  const results = {};
  
  // 创建任务队列
  const queue = [...modelIds];
  
  // 并发控制函数
  const runBatch = async () => {
    const batchPromises = [];
    
    // 同时处理maxConcurrentModels个车型
    for (let i = 0; i < maxConcurrentModels && queue.length > 0; i++) {
      const modelId = queue.shift();
      if (modelId) {
        batchPromises.push(
          upsertConfigCarTranslationParallel(modelId, languages, concurrencyPerModel)
            .then(result => {
              // 保存结果
              results[modelId] = {
                success: true,
                result
              };
              console.log(`[批量翻译] 完成车型 ${modelId} 的翻译`);
            })
            .catch(error => {
              // 处理失败的情况
              results[modelId] = {
                success: false,
                error: error.message,
                modelId
              };
              console.error(`[批量翻译] 车型 ${modelId} 翻译失败: ${error.message}`);
            })
        );
      }
    }
    
    await Promise.all(batchPromises);
    
    // 如果队列不为空，继续处理下一批
    if (queue.length > 0) {
      return runBatch();
    }
  };
  
  // 开始执行任务
  await runBatch();
  
  // 统计成功和失败的车型
  const successModels = Object.keys(results).filter(id => results[id].success);
  const failedModels = Object.keys(results).filter(id => !results[id].success);
  
  console.log(`[批量翻译] 多车型处理完成: ${successModels.length}个成功, ${failedModels.length}个失败`);
  
  return {
    success: failedModels.length === 0,
    results,
    successCount: successModels.length,
    failedCount: failedModels.length,
    successModels,
    failedModels
  };
};

module.exports = {
  LANGUAGES,
  isSupportedLanguage,
  translate,
  translateToMultipleLanguages,
  switchTranslationService,
  getCurrentService: () => TRANSLATION_SERVICE,
  saveTranslation,
  getCarTranslations,
  getCarMultiLanguageTranslations,
  updateCarTranslations,
  updateCarMultiLanguageTranslations,
  getProductTranslatableFields,
  getConfigCarTranslatableFields,
  upsertProductTranslation,
  upsertConfigCarTranslation,
  getProductStatusField,
  getConfigCarStatusField,
  checkAllFieldsNotEmpty,
  updateConfigCarTranslationWithCheck,
  updateConfigCarTranslationDirect,
  updateConfigCarTranslationStatus,
  updateProductTranslationWithCheck,
  updateProductTranslationStatus,
  updateConfigCarTransStatus,
  updateMultiLanguageTranslations,
  updateMultiLanguageTranslationsWithCheck,
  updateProductTranslation,
  updateProductMultiLanguageTranslationsWithCheck,
  updateConfigCarMultiLanguageTranslationsWithCheck,
  checkAndUpdateProductTestStatus,
  checkAndUpdateConfigCarTestStatus,
  checkAndUpdateProductTestAll,
  updateProductTranslationStatusAtomic,  // 添加缺失的导出
  getAllProductFields,
  containsChinese,
  // 新增优化功能
  translateBatch,
  translateParallel,
  upsertConfigCarTranslationParallel,
  upsertProductTranslationParallel,
  batchProcessProductTranslations,
  batchProcessConfigCarTranslations
}; 













