/**
 * 验证自动翻译任务使用队列机制
 */

const fs = require('fs');
const path = require('path');

function verifyQueueUsage() {
    console.log('🔍 验证自动翻译任务的队列使用...\n');

    try {
        // 1. 检查自动翻译任务代码
        console.log('1. 检查自动翻译任务代码...');
        const taskServicePath = path.join(__dirname, 'src', 'services', 'newScheduledTasksService.js');
        const taskServiceContent = fs.readFileSync(taskServicePath, 'utf8');

        // 检查关键代码片段
        const checks = [
            {
                name: '使用队列标志',
                pattern: 'useQueue: true',
                found: taskServiceContent.includes('useQueue: true')
            },
            {
                name: '设置优先级',
                pattern: 'priority: \'normal\'',
                found: taskServiceContent.includes('priority: \'normal\'')
            },
            {
                name: '调用批量处理',
                pattern: 'translationManager.batchProcessProducts',
                found: taskServiceContent.includes('translationManager.batchProcessProducts')
            },
            {
                name: '批量大小设置',
                pattern: 'batchSize: 10',
                found: taskServiceContent.includes('batchSize: 10')
            }
        ];

        let allPassed = true;
        checks.forEach(check => {
            if (check.found) {
                console.log(`   ✅ ${check.name}: 找到`);
            } else {
                console.log(`   ❌ ${check.name}: 未找到`);
                allPassed = false;
            }
        });

        // 2. 检查翻译管理器代码
        console.log('\n2. 检查翻译管理器代码...');
        const managerPath = path.join(__dirname, 'src', 'services', 'core', 'TranslationManager.js');
        const managerContent = fs.readFileSync(managerPath, 'utf8');

        const managerChecks = [
            {
                name: 'batchProcessProducts方法',
                pattern: 'async batchProcessProducts',
                found: managerContent.includes('async batchProcessProducts')
            },
            {
                name: 'handleProductListing调用',
                pattern: 'this.handleProductListing',
                found: managerContent.includes('this.handleProductListing')
            },
            {
                name: 'useQueue参数处理',
                pattern: 'useQueue',
                found: managerContent.includes('useQueue')
            }
        ];

        managerChecks.forEach(check => {
            if (check.found) {
                console.log(`   ✅ ${check.name}: 找到`);
            } else {
                console.log(`   ❌ ${check.name}: 未找到`);
                allPassed = false;
            }
        });

        // 3. 检查调度器代码
        console.log('\n3. 检查调度器代码...');
        const schedulerPath = path.join(__dirname, 'src', 'services', 'core', 'TranslationScheduler.js');
        
        if (fs.existsSync(schedulerPath)) {
            const schedulerContent = fs.readFileSync(schedulerPath, 'utf8');
            
            const schedulerChecks = [
                {
                    name: 'scheduleProductTranslation方法',
                    pattern: 'scheduleProductTranslation',
                    found: schedulerContent.includes('scheduleProductTranslation')
                },
                {
                    name: '添加翻译任务',
                    pattern: 'addTranslationTask',
                    found: schedulerContent.includes('addTranslationTask')
                }
            ];

            schedulerChecks.forEach(check => {
                if (check.found) {
                    console.log(`   ✅ ${check.name}: 找到`);
                } else {
                    console.log(`   ❌ ${check.name}: 未找到`);
                }
            });
        } else {
            console.log('   ℹ️ TranslationScheduler.js 文件不存在');
        }

        // 4. 提取关键代码片段
        console.log('\n4. 关键代码片段:');
        console.log('================');
        
        // 提取自动翻译任务的关键部分
        const batchProcessMatch = taskServiceContent.match(
            /const result = await translationManager\.batchProcessProducts\([\s\S]*?\}\);/
        );
        
        if (batchProcessMatch) {
            console.log('自动翻译任务调用:');
            console.log('```javascript');
            console.log(batchProcessMatch[0]);
            console.log('```');
        }

        // 5. 总结
        console.log('\n📊 验证结果:');
        console.log('============');
        
        if (allPassed) {
            console.log('✅ 所有检查通过！');
            console.log('\n🎯 确认的队列机制:');
            console.log('1. ✅ 自动翻译任务使用 useQueue: true');
            console.log('2. ✅ 设置优先级为 normal');
            console.log('3. ✅ 调用 translationManager.batchProcessProducts()');
            console.log('4. ✅ 产品被添加到翻译队列而非直接翻译');
            console.log('5. ✅ 支持批量处理 (batchSize: 10)');
            
            console.log('\n🚀 手动触发按钮的作用:');
            console.log('- 执行与定时任务完全相同的逻辑');
            console.log('- 查找需要翻译的产品');
            console.log('- 将产品加入翻译队列');
            console.log('- 由翻译队列服务异步处理');
            console.log('- 不会阻塞用户界面');
            
        } else {
            console.log('⚠️ 部分检查未通过，需要进一步确认');
        }

    } catch (error) {
        console.error('❌ 验证失败:', error.message);
    }
}

// 运行验证
verifyQueueUsage();
