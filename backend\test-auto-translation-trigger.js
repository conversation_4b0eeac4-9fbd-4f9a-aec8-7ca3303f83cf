/**
 * 测试手动触发自动翻译功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testAutoTranslationTrigger() {
    console.log('🧪 测试手动触发自动翻译功能...\n');

    const results = {
        total: 0,
        passed: 0,
        failed: 0
    };

    // 测试1: 检查API端点是否存在
    console.log('1. 检查API端点是否存在...');
    results.total++;
    
    try {
        // 不带认证的请求应该返回401
        const response = await axios.post('http://localhost:3010/api/v1/translation-monitor/trigger-auto-translation');
        console.log('   ❌ API应该要求认证但没有');
        results.failed++;
    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.log('   ✅ API正确要求认证');
            results.passed++;
        } else {
            console.log(`   ❌ API返回意外错误: ${error.message}`);
            results.failed++;
        }
    }

    // 测试2: 检查前端按钮是否存在
    console.log('\n2. 检查前端按钮是否存在...');
    results.total++;
    
    try {
        const htmlPath = path.join(__dirname, 'public', 'translation-monitor.html');
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        
        const hasButton = htmlContent.includes('triggerAutoTranslation()') && 
                         htmlContent.includes('手动触发自动翻译');
        
        if (hasButton) {
            console.log('   ✅ 前端按钮已添加');
            results.passed++;
        } else {
            console.log('   ❌ 前端按钮缺失');
            results.failed++;
        }
    } catch (error) {
        console.log(`   ❌ 检查前端文件失败: ${error.message}`);
        results.failed++;
    }

    // 测试3: 检查JavaScript函数是否存在
    console.log('\n3. 检查JavaScript函数是否存在...');
    results.total++;
    
    try {
        const jsPath = path.join(__dirname, 'public', 'translation-monitor.js');
        const jsContent = fs.readFileSync(jsPath, 'utf8');
        
        const hasFunction = jsContent.includes('async function triggerAutoTranslation()') &&
                           jsContent.includes('/trigger-auto-translation');
        
        if (hasFunction) {
            console.log('   ✅ JavaScript函数已添加');
            results.passed++;
        } else {
            console.log('   ❌ JavaScript函数缺失');
            results.failed++;
        }
    } catch (error) {
        console.log(`   ❌ 检查JavaScript文件失败: ${error.message}`);
        results.failed++;
    }

    // 测试4: 检查后端路由是否正确添加
    console.log('\n4. 检查后端路由是否正确添加...');
    results.total++;
    
    try {
        const routePath = path.join(__dirname, 'src', 'api', 'v1', 'routes', 'translationMonitor.js');
        const routeContent = fs.readFileSync(routePath, 'utf8');
        
        const hasRoute = routeContent.includes('/trigger-auto-translation') &&
                        routeContent.includes('autoTranslateProductsTask');
        
        if (hasRoute) {
            console.log('   ✅ 后端路由已添加');
            results.passed++;
        } else {
            console.log('   ❌ 后端路由缺失');
            results.failed++;
        }
    } catch (error) {
        console.log(`   ❌ 检查后端路由失败: ${error.message}`);
        results.failed++;
    }

    // 测试5: 检查自动翻译任务函数是否可导入
    console.log('\n5. 检查自动翻译任务函数是否可导入...');
    results.total++;
    
    try {
        const { autoTranslateProductsTask } = require('./src/services/newScheduledTasksService');
        
        if (typeof autoTranslateProductsTask === 'function') {
            console.log('   ✅ 自动翻译任务函数可正常导入');
            results.passed++;
        } else {
            console.log('   ❌ 自动翻译任务函数导入失败');
            results.failed++;
        }
    } catch (error) {
        console.log(`   ❌ 导入自动翻译任务函数失败: ${error.message}`);
        results.failed++;
    }

    // 汇总结果
    console.log('\n📊 手动触发自动翻译功能测试结果:');
    console.log('================================');
    console.log(`总计: ${results.passed}/${results.total} 通过`);
    
    if (results.failed === 0) {
        console.log('✅ 所有测试通过！手动触发自动翻译功能已成功添加。');
        console.log('\n🎯 功能说明:');
        console.log('- 🔘 翻译监控页面新增"🚀 手动触发自动翻译"按钮');
        console.log('- 🔐 需要管理员权限才能使用');
        console.log('- 📋 执行与定时任务相同的自动翻译逻辑');
        console.log('- 📊 显示详细的执行结果统计');
        console.log('- ⚠️ 建议在系统空闲时使用');
        console.log('\n📍 使用方法:');
        console.log('1. 登录翻译监控管理系统');
        console.log('2. 在控制面板中点击"🚀 手动触发自动翻译"按钮');
        console.log('3. 确认执行后等待任务完成');
        console.log('4. 查看执行结果统计');
    } else {
        console.log(`⚠️ ${results.failed} 个测试失败，需要进一步检查。`);
    }
}

// 运行测试
testAutoTranslationTrigger().catch(console.error);
