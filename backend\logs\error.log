{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-03-23 19:55:37"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-03-30 21:53:13"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-03-30 21:53:43"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-03-30 21:55:04"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-03-30 21:57:27"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://carseries.market.alicloudapi.com","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":10000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/car_detail/query?carid=brands","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /car_detail/query?carid=brands HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: APPCODE 8edc3209e58945d9a88eb505d2e8abac\r\nUser-Agent: axios/1.8.4\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: carseries.market.alicloudapi.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"hostname":"carseries.market.alicloudapi.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/car_detail/query?carid=brands","pathname":"/car_detail/query","port":"","protocol":"https:","search":"?carid=brands"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"carseries.market.alicloudapi.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/car_detail/query?carid=brands","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 31 Mar 2025 13:13:43 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Keep-Alive","timeout=25","Vary","Origin","Vary","Access-Control-Request-Method","Vary","Access-Control-Request-Headers","Server","Kaede/3.5.3.917 (hz003a4am)","X-Ca-Request-Id","DDFF3BA1-546F-4F84-B901-7F6722CC8908"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://carseries.market.alicloudapi.com","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":10000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/car_detail/query?carid=brands","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"code":"1","data":null,"isFee":0,"msg":"车型ID错误","seqNo":"0umcp0391druubjr4jn9uqnubgehenel"},"headers":{"connection":"keep-alive","content-type":"application/json","date":"Mon, 31 Mar 2025 13:13:43 GMT","keep-alive":"timeout=25","server":"Kaede/3.5.3.917 (hz003a4am)","transfer-encoding":"chunked","vary":"Origin, Access-Control-Request-Method, Access-Control-Request-Headers","x-ca-request-id":"DDFF3BA1-546F-4F84-B901-7F6722CC8908"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /car_detail/query?carid=brands HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: APPCODE 8edc3209e58945d9a88eb505d2e8abac\r\nUser-Agent: axios/1.8.4\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: carseries.market.alicloudapi.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"hostname":"carseries.market.alicloudapi.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/car_detail/query?carid=brands","pathname":"/car_detail/query","port":"","protocol":"https:","search":"?carid=brands"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"carseries.market.alicloudapi.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/car_detail/query?carid=brands","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 31 Mar 2025 13:13:43 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Keep-Alive","timeout=25","Vary","Origin","Vary","Access-Control-Request-Method","Vary","Access-Control-Request-Headers","Server","Kaede/3.5.3.917 (hz003a4am)","X-Ca-Request-Id","DDFF3BA1-546F-4F84-B901-7F6722CC8908"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"service":"used-car-export","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:2031:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:3148:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncModelSpecDetail (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\src\\services\\aliyunCarApi.js:171:22)\n    at async getConfigurationById (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\src\\api\\v1\\controllers\\configurationController.js:54:25)","status":400,"timestamp":"2025-03-31 21:13:44"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://carseries.market.alicloudapi.com","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":10000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/car_detail/query?carid=brands","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /car_detail/query?carid=brands HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: APPCODE 8edc3209e58945d9a88eb505d2e8abac\r\nUser-Agent: axios/1.8.4\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: carseries.market.alicloudapi.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"hostname":"carseries.market.alicloudapi.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,147,4,183,45,197,61,216,192,240,45,254,53,111,134,209,148,175,4,132,112,181,92,180,225,148,136,116,226,67,223,123,144,4,48,123,192,111,53,83,240,34,237,59,225,219,137,42,111,115,237,202,72,159,212,107,243,124,44,160,170,235,152,171,65,163,204,255,207,123,79,32,222,217,188,105,223,171,166,92,131,167,7,161,6,2,4,103,234,149,48,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,240,122,156,65,59,167,247,142,243,231,45,139,206,52,77,58,79,186,24,36,3,31,135,57,229,118,122,174,144,110,178,254,138,54,216,250,198,164,175,121,36,6,202,43,53,133,204,185,174,213,139,210,180,26,137,231,216,115,51,18,101,82,8,111,10,207,14,99,83,33,64,116,250,1,233,99,11,24,245,227,26,115,87,55,176,218,157,211,68,243,149,83,200,211,181,212,97,186,209,80,121,229,132,128,13,47,156,50,13,9,27,214,220,119,115,196,67,30,161,249,113,36,181,186,107,204,239,200,66,124,35,75,24,116,178,150,4,30,85,64,21,183,123,112,147,45,222,161,136,74,188,19,186,43,184,86,27,241,177,66,208,11,220,44,225,118,132,125,206,114,30,95,189,158,236,5,52,91,211,98,110,90,29,186,169,177,118,11,196,130,77,244,11,125,69,173,39,245,161,26,101,40,25,120,179,136,227,32,116,237,208,55,130,45,201,53,122,250,21,86,10,160,87,165,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/car_detail/query?carid=brands","pathname":"/car_detail/query","port":"","protocol":"https:","search":"?carid=brands"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,147,4,183,45,197,61,216,192,240,45,254,53,111,134,209,148,175,4,132,112,181,92,180,225,148,136,116,226,67,223,123,144,4,48,123,192,111,53,83,240,34,237,59,225,219,137,42,111,115,237,202,72,159,212,107,243,124,44,160,170,235,152,171,65,163,204,255,207,123,79,32,222,217,188,105,223,171,166,92,131,167,7,161,6,2,4,103,234,149,48,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,240,122,156,65,59,167,247,142,243,231,45,139,206,52,77,58,79,186,24,36,3,31,135,57,229,118,122,174,144,110,178,254,138,54,216,250,198,164,175,121,36,6,202,43,53,133,204,185,174,213,139,210,180,26,137,231,216,115,51,18,101,82,8,111,10,207,14,99,83,33,64,116,250,1,233,99,11,24,245,227,26,115,87,55,176,218,157,211,68,243,149,83,200,211,181,212,97,186,209,80,121,229,132,128,13,47,156,50,13,9,27,214,220,119,115,196,67,30,161,249,113,36,181,186,107,204,239,200,66,124,35,75,24,116,178,150,4,30,85,64,21,183,123,112,147,45,222,161,136,74,188,19,186,43,184,86,27,241,177,66,208,11,220,44,225,118,132,125,206,114,30,95,189,158,236,5,52,91,211,98,110,90,29,186,169,177,118,11,196,130,77,244,11,125,69,173,39,245,161,26,101,40,25,120,179,136,227,32,116,237,208,55,130,45,201,53,122,250,21,86,10,160,87,165,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"carseries.market.alicloudapi.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/car_detail/query?carid=brands","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 31 Mar 2025 13:14:23 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Keep-Alive","timeout=25","Vary","Origin","Vary","Access-Control-Request-Method","Vary","Access-Control-Request-Headers","Server","Kaede/3.5.3.917 (hz003bos1)","X-Ca-Request-Id","B63506F3-A389-455D-B3A7-1F7AA8D53580"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://carseries.market.alicloudapi.com","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":10000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/car_detail/query?carid=brands","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"code":"1","data":null,"isFee":0,"msg":"车型ID错误","seqNo":"p8rf14t4icgy0cynsz7wiwu0jiazm5vh"},"headers":{"connection":"keep-alive","content-type":"application/json","date":"Mon, 31 Mar 2025 13:14:23 GMT","keep-alive":"timeout=25","server":"Kaede/3.5.3.917 (hz003bos1)","transfer-encoding":"chunked","vary":"Origin, Access-Control-Request-Method, Access-Control-Request-Headers","x-ca-request-id":"B63506F3-A389-455D-B3A7-1F7AA8D53580"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /car_detail/query?carid=brands HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: APPCODE 8edc3209e58945d9a88eb505d2e8abac\r\nUser-Agent: axios/1.8.4\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: carseries.market.alicloudapi.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"APPCODE 8edc3209e58945d9a88eb505d2e8abac","Content-Type":"application/json","User-Agent":"axios/1.8.4"},"hostname":"carseries.market.alicloudapi.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,147,4,183,45,197,61,216,192,240,45,254,53,111,134,209,148,175,4,132,112,181,92,180,225,148,136,116,226,67,223,123,144,4,48,123,192,111,53,83,240,34,237,59,225,219,137,42,111,115,237,202,72,159,212,107,243,124,44,160,170,235,152,171,65,163,204,255,207,123,79,32,222,217,188,105,223,171,166,92,131,167,7,161,6,2,4,103,234,149,48,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,240,122,156,65,59,167,247,142,243,231,45,139,206,52,77,58,79,186,24,36,3,31,135,57,229,118,122,174,144,110,178,254,138,54,216,250,198,164,175,121,36,6,202,43,53,133,204,185,174,213,139,210,180,26,137,231,216,115,51,18,101,82,8,111,10,207,14,99,83,33,64,116,250,1,233,99,11,24,245,227,26,115,87,55,176,218,157,211,68,243,149,83,200,211,181,212,97,186,209,80,121,229,132,128,13,47,156,50,13,9,27,214,220,119,115,196,67,30,161,249,113,36,181,186,107,204,239,200,66,124,35,75,24,116,178,150,4,30,85,64,21,183,123,112,147,45,222,161,136,74,188,19,186,43,184,86,27,241,177,66,208,11,220,44,225,118,132,125,206,114,30,95,189,158,236,5,52,91,211,98,110,90,29,186,169,177,118,11,196,130,77,244,11,125,69,173,39,245,161,26,101,40,25,120,179,136,227,32,116,237,208,55,130,45,201,53,122,250,21,86,10,160,87,165,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/car_detail/query?carid=brands","pathname":"/car_detail/query","port":"","protocol":"https:","search":"?carid=brands"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["carseries.market.alicloudapi.com:443:::::::::::::::::::::"],"map":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,147,4,183,45,197,61,216,192,240,45,254,53,111,134,209,148,175,4,132,112,181,92,180,225,148,136,116,226,67,223,123,144,4,48,123,192,111,53,83,240,34,237,59,225,219,137,42,111,115,237,202,72,159,212,107,243,124,44,160,170,235,152,171,65,163,204,255,207,123,79,32,222,217,188,105,223,171,166,92,131,167,7,161,6,2,4,103,234,149,48,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,240,122,156,65,59,167,247,142,243,231,45,139,206,52,77,58,79,186,24,36,3,31,135,57,229,118,122,174,144,110,178,254,138,54,216,250,198,164,175,121,36,6,202,43,53,133,204,185,174,213,139,210,180,26,137,231,216,115,51,18,101,82,8,111,10,207,14,99,83,33,64,116,250,1,233,99,11,24,245,227,26,115,87,55,176,218,157,211,68,243,149,83,200,211,181,212,97,186,209,80,121,229,132,128,13,47,156,50,13,9,27,214,220,119,115,196,67,30,161,249,113,36,181,186,107,204,239,200,66,124,35,75,24,116,178,150,4,30,85,64,21,183,123,112,147,45,222,161,136,74,188,19,186,43,184,86,27,241,177,66,208,11,220,44,225,118,132,125,206,114,30,95,189,158,236,5,52,91,211,98,110,90,29,186,169,177,118,11,196,130,77,244,11,125,69,173,39,245,161,26,101,40,25,120,179,136,227,32,116,237,208,55,130,45,201,53,122,250,21,86,10,160,87,165,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"carseries.market.alicloudapi.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"carseries.market.alicloudapi.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/car_detail/query?carid=brands","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"carseries.market.alicloudapi.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,8,102,2,1,1,2,2,3,3,4,2,192,47,4,32,140,104,190,63,82,140,234,243,92,103,162,124,162,136,17,173,55,160,55,173,215,252,189,109,216,161,189,205,143,83,166,175,4,48,59,75,11,193,233,194,94,187,142,111,174,35,80,157,64,161,38,234,211,43,7,239,42,197,147,87,103,195,125,196,165,115,213,106,225,209,141,184,65,3,209,66,30,241,171,91,29,67,161,6,2,4,103,234,149,8,162,4,2,2,28,32,163,130,6,215,48,130,6,211,48,130,5,187,160,3,2,1,2,2,12,103,251,31,34,218,226,253,133,54,131,216,159,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,48,83,49,11,48,9,6,3,85,4,6,19,2,66,69,49,25,48,23,6,3,85,4,10,19,16,71,108,111,98,97,108,83,105,103,110,32,110,118,45,115,97,49,41,48,39,6,3,85,4,3,19,32,71,108,111,98,97,108,83,105,103,110,32,71,67,67,32,82,51,32,79,86,32,84,76,83,32,67,65,32,50,48,50,52,48,30,23,13,50,53,48,51,49,49,48,56,50,55,48,49,90,23,13,50,53,48,57,48,52,48,48,48,48,48,48,90,48,129,133,49,11,48,9,6,3,85,4,6,19,2,67,78,49,17,48,15,6,3,85,4,8,19,8,90,104,101,74,105,97,110,103,49,17,48,15,6,3,85,4,7,19,8,72,97,110,103,90,104,111,117,49,45,48,43,6,3,85,4,10,19,36,65,108,105,98,97,98,97,32,40,67,104,105,110,97,41,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,32,76,116,100,46,49,33,48,31,6,3,85,4,3,12,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,130,1,34,48,13,6,9,42,134,72,134,247,13,1,1,1,5,0,3,130,1,15,0,48,130,1,10,2,130,1,1,0,174,206,159,172,171,244,81,164,107,180,196,23,237,97,57,150,198,248,55,244,13,13,235,21,192,100,120,62,216,176,190,89,72,0,3,52,119,142,13,110,2,199,112,132,196,230,72,92,236,232,202,238,32,240,110,31,136,86,215,107,30,218,100,136,196,33,175,195,220,160,72,155,18,14,204,145,178,248,63,34,170,126,232,171,151,173,42,31,36,124,191,173,143,166,133,74,205,245,250,241,72,101,185,186,177,201,231,144,43,64,50,243,193,44,68,84,27,136,245,97,49,99,206,135,136,33,30,237,70,105,101,17,13,195,100,40,253,8,212,251,123,1,70,131,174,90,203,57,86,83,30,115,219,171,252,254,77,103,227,200,210,239,96,58,75,241,255,100,171,73,85,67,105,187,135,230,254,199,92,199,71,59,65,230,132,235,232,87,190,157,81,15,125,108,222,237,115,127,182,142,126,104,2,107,48,182,25,240,200,29,3,232,187,71,79,67,146,255,140,130,134,109,4,120,110,67,182,133,87,89,26,191,72,98,113,165,43,12,91,14,242,185,3,72,229,59,203,193,226,33,136,220,221,207,169,231,2,3,1,0,1,163,130,3,114,48,130,3,110,48,14,6,3,85,29,15,1,1,255,4,4,3,2,5,160,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,129,147,6,8,43,6,1,5,5,7,1,1,4,129,134,48,129,131,48,70,6,8,43,6,1,5,5,7,48,2,134,58,104,116,116,112,58,47,47,115,101,99,117,114,101,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,99,97,99,101,114,116,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,116,48,57,6,8,43,6,1,5,5,7,48,1,134,45,104,116,116,112,58,47,47,111,99,115,112,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,48,87,6,3,85,29,32,4,80,48,78,48,8,6,6,103,129,12,1,2,2,48,66,6,10,43,6,1,4,1,160,50,10,1,2,48,52,48,50,6,8,43,6,1,5,5,7,2,1,22,38,104,116,116,112,115,58,47,47,119,119,119,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,114,101,112,111,115,105,116,111,114,121,47,48,65,6,3,85,29,31,4,58,48,56,48,54,160,52,160,50,134,48,104,116,116,112,58,47,47,99,114,108,46,103,108,111,98,97,108,115,105,103,110,46,99,111,109,47,103,115,103,99,99,114,51,111,118,116,108,115,99,97,50,48,50,52,46,99,114,108,48,59,6,3,85,29,17,4,52,48,50,130,24,42,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,130,22,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,48,29,6,3,85,29,37,4,22,48,20,6,8,43,6,1,5,5,7,3,1,6,8,43,6,1,5,5,7,3,2,48,31,6,3,85,29,35,4,24,48,22,128,20,218,211,168,8,72,12,52,55,88,238,229,167,117,46,89,252,214,220,60,56,48,29,6,3,85,29,14,4,22,4,20,104,221,204,138,252,86,111,240,186,109,199,217,177,7,13,134,111,191,245,136,48,130,1,126,6,10,43,6,1,4,1,214,121,2,4,2,4,130,1,110,4,130,1,106,1,104,0,117,0,175,24,26,40,214,140,163,224,169,138,76,156,103,171,9,248,187,188,34,186,174,188,177,56,163,161,157,211,249,182,3,13,0,0,1,149,132,80,140,196,0,0,4,3,0,70,48,68,2,32,53,39,147,30,152,90,2,189,103,55,110,4,107,10,176,196,103,67,214,202,130,171,134,25,249,145,33,151,118,115,73,70,2,32,6,78,7,248,159,174,68,146,131,30,124,231,35,103,85,145,196,186,171,82,98,165,32,53,56,230,144,169,43,10,182,96,0,118,0,18,241,78,52,189,83,114,76,132,6,25,195,143,63,122,19,248,231,181,98,135,136,156,109,48,5,132,235,229,134,38,58,0,0,1,149,132,80,141,159,0,0,4,3,0,71,48,69,2,32,126,224,160,205,136,64,201,2,158,214,98,46,115,121,44,232,17,8,179,65,113,118,69,160,240,45,72,217,177,186,58,196,2,33,0,186,176,219,202,20,166,102,28,167,29,243,125,153,202,188,156,251,171,173,151,187,202,167,19,13,8,222,59,90,13,124,237,0,119,0,13,225,242,48,43,211,13,193,64,98,18,9,234,85,46,252,71,116,124,177,215,233,48,239,14,66,30,180,126,78,170,52,0,0,1,149,132,80,138,165,0,0,4,3,0,72,48,70,2,33,0,231,115,147,128,15,72,196,206,246,130,19,165,199,200,240,124,202,161,199,220,65,181,240,174,41,175,141,200,81,91,73,203,2,33,0,132,114,211,107,171,69,116,114,61,125,9,39,149,192,121,77,222,189,145,9,251,244,203,255,210,41,146,247,97,238,234,59,48,13,6,9,42,134,72,134,247,13,1,1,11,5,0,3,130,1,1,0,4,206,46,49,120,243,81,45,54,188,243,239,173,75,37,96,60,47,171,207,167,199,61,181,186,77,38,127,142,33,143,237,197,238,64,139,205,228,198,202,4,129,44,110,18,145,67,253,56,7,201,56,46,26,83,5,178,207,86,226,145,119,106,16,181,249,131,142,167,101,229,95,1,107,161,16,77,69,88,251,219,242,206,187,80,117,157,235,210,32,216,195,13,24,100,214,213,162,59,250,235,253,73,8,72,99,210,74,158,132,206,195,170,79,59,34,160,159,38,173,229,97,186,44,8,23,192,107,178,235,217,66,109,85,121,203,253,157,79,109,94,42,178,94,55,42,109,161,230,192,21,212,7,228,146,177,170,237,254,251,247,96,72,213,222,162,29,184,148,124,150,14,126,184,112,93,226,22,217,224,210,130,178,71,236,160,137,206,147,146,38,18,208,54,179,133,182,63,112,139,248,210,134,245,185,162,43,201,50,3,194,145,232,113,59,8,252,254,80,217,151,0,64,173,113,198,188,232,1,142,218,182,50,122,210,34,152,193,73,6,150,108,226,155,110,219,209,17,78,89,159,61,198,12,213,202,164,2,4,0,166,34,4,32,99,97,114,115,101,114,105,101,115,46,109,97,114,107,101,116,46,97,108,105,99,108,111,117,100,97,112,105,46,99,111,109,169,4,2,2,2,88,170,129,227,4,129,224,96,39,74,4,222,113,242,105,164,233,196,178,212,63,97,239,198,78,10,253,176,255,45,61,81,211,0,112,244,234,47,214,54,160,107,107,72,40,255,15,23,149,183,234,67,192,255,196,68,184,164,209,40,36,173,244,53,231,66,110,121,53,26,196,235,106,157,182,247,176,100,152,26,143,90,99,71,111,191,24,254,48,93,101,114,66,66,164,227,189,242,201,48,32,55,234,154,67,253,36,253,30,194,127,140,108,247,173,232,201,43,113,190,105,112,223,127,188,166,42,26,108,6,49,201,23,176,113,86,33,129,98,236,253,225,30,9,245,24,182,37,124,92,132,171,138,66,171,12,133,29,113,42,56,239,118,2,211,205,67,95,177,163,176,12,5,229,254,10,193,194,252,50,139,193,38,211,200,95,150,65,69,239,172,1,47,144,79,217,249,145,120,250,245,19,6,146,173,184,61,203,145,40,49,176,93,196,96,227,1,231,168,174,223,125,44,218,210,114,202,188,90,89,164,173,3,2,1,1,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"carseries.market.alicloudapi.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 31 Mar 2025 13:14:23 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Keep-Alive","timeout=25","Vary","Origin","Vary","Access-Control-Request-Method","Vary","Access-Control-Request-Headers","Server","Kaede/3.5.3.917 (hz003bos1)","X-Ca-Request-Id","B63506F3-A389-455D-B3A7-1F7AA8D53580"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://carseries.market.alicloudapi.com/car_detail/query?carid=brands","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"service":"used-car-export","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:2031:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:3148:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncModelSpecDetail (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\src\\services\\aliyunCarApi.js:171:22)\n    at async getConfigurationById (C:\\Users\\<USER>\\Desktop\\0331数据模型重构上架详情页完成\\sgc -品牌页\\backend\\src\\api\\v1\\controllers\\configurationController.js:54:25)","status":400,"timestamp":"2025-03-31 21:14:24"}
{"duration":"2024ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/18","service":"used-car-export","statusCode":200,"timestamp":"2025-04-02 10:41:48"}
{"duration":"2022ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":200,"timestamp":"2025-04-02 10:43:43"}
{"duration":"2057ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/65","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 11:41:14"}
{"duration":"2099ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/65","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 11:41:56"}
{"duration":"2085ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/18","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 11:44:24"}
{"duration":"2020ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 11:53:40"}
{"duration":"2017ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 11:54:26"}
{"duration":"2535ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":200,"timestamp":"2025-04-02 14:52:39"}
{"duration":"2049ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/6576","service":"used-car-export","statusCode":304,"timestamp":"2025-04-02 16:35:27"}
{"duration":"2579ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":200,"timestamp":"2025-04-09 12:05:10"}
{"duration":"2300ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/692","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 14:24:29"}
{"duration":"2749ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/e52401cf-abb1-478f-94ff-40b2f8a3b0f8/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 15:49:29"}
{"duration":"2163ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/e52401cf-abb1-478f-94ff-40b2f8a3b0f8/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 15:57:13"}
{"duration":"37699ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/48a6c7d8-def3-46e2-84af-a35943c15057/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 18:32:32"}
{"duration":"28274ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/48a6c7d8-def3-46e2-84af-a35943c15057/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 19:12:59"}
{"duration":"34687ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/48a6c7d8-def3-46e2-84af-a35943c15057/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 19:29:51"}
{"duration":"33509ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/ce31215e-cf48-4924-bfb3-2cadb2fed7b0/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 19:31:31"}
{"duration":"32550ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/4f0f6fd7-d3b3-4f5b-8563-f645d6c4d29c/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 20:47:15"}
{"duration":"46109ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/16395bd0-4048-4d4d-81b9-e50127cd031d/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 20:54:38"}
{"duration":"51426ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/66ca5412-a0b3-4d89-b4a0-3a7263ea779f/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-11 22:49:07"}
{"duration":"2049ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/model/series/3294","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 09:36:22"}
{"duration":"11189ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/e5067112-c1f0-40a9-965d-068c87105e0e/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 09:39:44"}
{"duration":"29792ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/c60af717-113e-4d1b-ae06-3c96901c0c3b/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 10:21:11"}
{"duration":"30081ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/2ad13bb5-350a-4921-8a1b-6c6198795287/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 11:28:33"}
{"duration":"26339ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/c76f64d6-74d8-4993-bfbe-32c2405ac81a/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 12:19:36"}
{"duration":"82060ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/4994d00f-60ef-48dd-9827-a896c3fbe471/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 12:35:11"}
{"duration":"80076ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/4994d00f-60ef-48dd-9827-a896c3fbe471/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 12:41:31"}
{"duration":"78576ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 12:46:54"}
{"duration":"87064ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 14:44:50"}
{"duration":"277590ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 14:56:50"}
{"duration":"231162ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 15:10:24"}
{"duration":"232885ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 15:20:05"}
{"duration":"2404254ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 16:09:48"}
{"duration":"85777ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/b2dd724d-2bf5-4c4f-87b3-1a7198b208a2/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-12 18:07:06"}
{"duration":"6461ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/08b410a8-7639-4b9e-a12d-3e25329415c8/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-13 22:10:00"}
{"duration":"6368ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/08b410a8-7639-4b9e-a12d-3e25329415c8/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-14 08:56:36"}
{"duration":"43326ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/brands/grouped","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 11:55:56"}
{"duration":"48421ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/brands/grouped","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 11:56:38"}
{"duration":"69304ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/config/brands/grouped","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 11:57:20"}
{"duration":"7801ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/e9a6f293-5826-4a09-87ce-a2e53fd88b9d/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 20:20:43"}
{"duration":"5734ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/0dc12f24-8abf-4cbe-9bcb-9a74ae196520/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 20:24:59"}
{"duration":"5634ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/0dc12f24-8abf-4cbe-9bcb-9a74ae196520/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 20:46:30"}
{"duration":"6625ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/0dc12f24-8abf-4cbe-9bcb-9a74ae196520/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 20:49:29"}
{"duration":"6263ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/0dc12f24-8abf-4cbe-9bcb-9a74ae196520/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-14 21:16:45"}
{"duration":"132490ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/9901d591-d1c7-4257-a613-943e90422beb/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 21:40:39"}
{"duration":"129679ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/9901d591-d1c7-4257-a613-943e90422beb/translate","service":"used-car-export","statusCode":200,"timestamp":"2025-04-14 21:52:13"}
{"duration":"15835ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/cb122f3a-998c-4f93-9a1d-0998c7d777d4/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-15 10:51:42"}
{"duration":"25231ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/bcd250db-8b02-4855-a8d1-fdfd992d8fd6/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-15 19:55:44"}
{"duration":"5604ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/bcd250db-8b02-4855-a8d1-fdfd992d8fd6/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-15 20:29:35"}
{"duration":"7617ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/bcd250db-8b02-4855-a8d1-fdfd992d8fd6/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-15 22:25:36"}
{"duration":"7627ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/d875d7f2-d238-4447-aad4-3100a92ed1fe/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-16 08:08:23"}
{"duration":"8088ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/a3042b69-1663-4239-91cc-0090d273a779/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-16 09:46:49"}
{"duration":"7845ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/fe9e4feb-8870-4b90-99ec-6e473267ff2d/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-16 19:44:42"}
{"duration":"5556ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/fe9e4feb-8870-4b90-99ec-6e473267ff2d/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-16 20:09:01"}
{"duration":"5779ms","level":"error","message":"API响应时间过长","method":"POST","path":"/api/v1/products/fe9e4feb-8870-4b90-99ec-6e473267ff2d/translate","service":"used-car-export","statusCode":500,"timestamp":"2025-04-16 20:11:18"}
{"duration":"2001ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/products/a7ac2b31-9388-4aaa-a551-a7ab0824edb0?source=products","service":"used-car-export","statusCode":200,"timestamp":"2025-06-04 12:27:46"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-04 20:54:44"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-04 20:55:54"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-04 20:59:50"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-05 10:29:56"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-05 10:32:03"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:463:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 11:49:07"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:463:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 11:49:16"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:463:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 11:50:00"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:463:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 11:50:06"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:463:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 11:50:09"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:40:45"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:40:47"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:40:47"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:40:49"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:40:52"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:08"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:10"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:10"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:12"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:16"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:38"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:42"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:43"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:41:52"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:42:01"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:42:03"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:42:03"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:03"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:08"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:10"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:10"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:47"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:43:48"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:17"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:18"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:19"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:21"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:22"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:23"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:33"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:34"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:34"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:35"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:50:42"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:51:00"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:51:06"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:56:18"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 14:58:23"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 15:00:14"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats","user":{"id":1,"role":"admin"}},"level":"error","message":"Cannot read properties of undefined (reading 'count')","service":"used-car-export","stack":"TypeError: Cannot read properties of undefined (reading 'count')\n    at getStatistics (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:608:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-05 15:02:19"}
{"context":{"body":{},"method":"POST","params":{},"query":{},"url":"/api/v1/admin/login","user":"unauthenticated"},"level":"error","message":"Bad escaped character in JSON at position 12 (line 1 column 13)","service":"used-car-export","stack":"SyntaxError: Bad escaped character in JSON at position 12 (line 1 column 13)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-05 15:05:41"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:17:42"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:17:42"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:18:21"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:18:21"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:18:37"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:18:37"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:19:54"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:19:54"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:20:24"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:20:24"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/dealers","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.id' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getDealerStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:909:31)","timestamp":"2025-06-05 15:20:24"}
{"context":{"body":{},"method":"GET","params":{},"query":{},"url":"/api/v1/admin/stats/monthly","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'views' in 'field list'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getMonthlyStats (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:854:30)","timestamp":"2025-06-05 15:20:24"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-05 21:53:41"}
{"level":"error","message":"无法启动服务器: 数据库连接失败","service":"used-car-export","timestamp":"2025-06-06 14:33:09"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"active"},"url":"/api/v1/admin/cars?status=active","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:41"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"active"},"url":"/api/v1/admin/cars?status=active","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:42"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"active"},"url":"/api/v1/admin/cars?status=active","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:43"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"pending"},"url":"/api/v1/admin/cars?status=pending","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:46"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"sold"},"url":"/api/v1/admin/cars?status=sold","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:48"}
{"context":{"body":{},"method":"GET","params":{},"query":{"status":"sold"},"url":"/api/v1/admin/cars?status=sold","user":{"id":1,"role":"admin"}},"level":"error","message":"Column 'status' in where clause is ambiguous","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:556:18)","timestamp":"2025-06-06 21:18:48"}
{"duration":"2073ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/products/4c15106f-549f-4faa-84cc-fa1859903e79?source=products","service":"used-car-export","statusCode":200,"timestamp":"2025-06-06 22:52:42"}
{"duration":"2453ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/products/fe70f02b-ea1c-42cf-8128-a1e04cc61741?locale=zh","service":"used-car-export","statusCode":200,"timestamp":"2025-06-08 10:21:48"}
{"duration":"2029ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/config_car/detail/70159","service":"used-car-export","statusCode":200,"timestamp":"2025-06-08 10:50:03"}
{"duration":"2537ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/products/4a7453a6-54ef-4e16-b95a-b19776a157e4?source=products","service":"used-car-export","statusCode":200,"timestamp":"2025-06-13 17:17:14"}
{"context":{"body":{},"method":"GET","params":{},"query":{"keyword":"多少的"},"url":"/api/v1/admin/cars?keyword=%E5%A4%9A%E5%B0%91%E7%9A%84","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.model' in 'where clause'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:561:18)","timestamp":"2025-06-13 17:52:19"}
{"context":{"body":{},"method":"GET","params":{},"query":{"keyword":"多少的"},"url":"/api/v1/admin/cars?keyword=%E5%A4%9A%E5%B0%91%E7%9A%84","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.model' in 'where clause'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:561:18)","timestamp":"2025-06-13 17:52:19"}
{"context":{"body":{},"method":"GET","params":{},"query":{"keyword":"多少的"},"url":"/api/v1/admin/cars?keyword=%E5%A4%9A%E5%B0%91%E7%9A%84","user":{"id":1,"role":"admin"}},"level":"error","message":"Unknown column 'p.model' in 'where clause'","service":"used-car-export","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getAllCars (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\src\\controllers\\adminController.js:561:18)","timestamp":"2025-06-13 17:52:20"}
{"context":{"body":{},"method":"POST","params":{},"query":{},"url":"/api/v1/translations/smart-schedule","user":"unauthenticated"},"level":"error","message":"Bad escaped character in JSON at position 13 (line 1 column 14)","service":"used-car-export","stack":"SyntaxError: Bad escaped character in JSON at position 13 (line 1 column 14)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-14 14:37:44"}
{"context":{"body":{},"method":"POST","params":{},"query":{},"url":"/api/products/test-translation","user":"unauthenticated"},"level":"error","message":"Bad escaped character in JSON at position 13 (line 1 column 14)","service":"used-car-export","stack":"SyntaxError: Bad escaped character in JSON at position 13 (line 1 column 14)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\sgc\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-14 15:38:08"}
{"duration":"2171ms","level":"error","message":"API响应时间过长","method":"GET","path":"/api/v1/config_car/detail/46346","service":"used-car-export","statusCode":200,"timestamp":"2025-07-14 20:45:49"}
10:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
12:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
22:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
08:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
10:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
12:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
14:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
16:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
18:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
20:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
22:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
11:21:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
12:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
16:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
20:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
22:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
10:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
12:00:00 ERROR [SGC] ❌ 定时任务 自动翻译未翻译产品: 执行出错: autoTranslationService.batchProcessUntranslatedProducts is not a function
19:43:29 ERROR [SGC] API响应时间过长
21:42:48 ERROR [SGC] 💥 系统错误: Bad escaped character in JSON at position 8 (line 1 column 9)
10:52:00 ERROR [SGC] API响应时间过长
12:20:31 ERROR [SGC] 💥 系统错误: Bad escaped character in JSON at position 8 (line 1 column 9)
12:49:17 ERROR [SGC] 记录汇率历史失败: Validation error
12:49:45 ERROR [SGC] 记录汇率历史失败: Validation error
19:12:22 ERROR [SGC] API响应时间过长
15:07:04 ERROR [SGC] 新翻译系统启动失败:
21:38:59 ERROR [SGC] 新翻译系统启动失败:
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (en) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (ru) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (es) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
14:54:31 ERROR [SGC] ❌ 翻译服务产品 ID a0259bfa-0057-4c63-9099-f53cf3ecd67f (fr) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (en) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (ru) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (es) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 color API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 interiorColor API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 condition API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 energyType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 bodyStructure API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 gearboxType API 翻译失败: 所有翻译引擎都不可用
15:00:39 ERROR [SGC] ❌ 翻译服务产品 ID e4c51815-9a7f-4f08-a1d1-dea08fdf5ebb (fr) 失败: 必翻字段 driveType API 翻译失败: 所有翻译引擎都不可用
21:30:30 ERROR [SGC] API响应时间过长
12:22:52 ERROR [SGC] API响应时间过长
13:18:46 ERROR [SGC] API响应时间过长
13:25:24 ERROR [SGC] API响应时间过长
13:37:13 ERROR [SGC] API响应时间过长
13:42:53 ERROR [SGC] API响应时间过长
13:48:13 ERROR [SGC] API响应时间过长
14:05:36 ERROR [SGC] API响应时间过长
14:05:36 ERROR [SGC] API响应时间过长
14:05:36 ERROR [SGC] API响应时间过长
14:05:36 ERROR [SGC] API响应时间过长
14:05:36 ERROR [SGC] API响应时间过长
14:47:28 ERROR [SGC] API响应时间过长
14:47:40 ERROR [SGC] API响应时间过长
14:47:57 ERROR [SGC] API响应时间过长
14:51:38 ERROR [SGC] API响应时间过长
15:21:16 ERROR [SGC] API响应时间过长
15:33:59 ERROR [SGC] API响应时间过长
15:40:07 ERROR [SGC] API响应时间过长
16:38:57 ERROR [SGC] API响应时间过长
17:49:57 ERROR [SGC] 💥 系统错误: Bad escaped character in JSON at position 12 (line 1 column 13)
18:02:07 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (en) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:02:07 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (ru) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:02:07 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (es) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:02:07 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (fr) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:08:58 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (en) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:08:58 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (ru) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:08:58 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (es) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
18:08:58 ERROR [SGC] ❌ 翻译服务产品 ID 01227b7f-b88e-4baf-b476-e3aedf8830a5 (fr) 失败: 必翻字段 title API 翻译失败: 所有翻译引擎都不可用
21:27:52 ERROR [SGC] API响应时间过长
21:27:54 ERROR [SGC] API响应时间过长
21:27:55 ERROR [SGC] API响应时间过长
21:28:11 ERROR [SGC] API响应时间过长
21:39:18 ERROR [SGC] API响应时间过长
21:39:46 ERROR [SGC] API响应时间过长
21:40:59 ERROR [SGC] API响应时间过长
21:41:03 ERROR [SGC] API响应时间过长
21:46:32 ERROR [SGC] API响应时间过长
21:54:05 ERROR [SGC] API响应时间过长
22:04:12 ERROR [SGC] API响应时间过长
22:04:37 ERROR [SGC] API响应时间过长
22:04:43 ERROR [SGC] API响应时间过长
22:17:02 ERROR [SGC] API响应时间过长
22:17:04 ERROR [SGC] API响应时间过长
22:18:21 ERROR [SGC] API响应时间过长
12:06:29 ERROR [SGC] API响应时间过长
12:06:32 ERROR [SGC] API响应时间过长
12:07:36 ERROR [SGC] API响应时间过长
12:07:37 ERROR [SGC] API响应时间过长
12:07:43 ERROR [SGC] API响应时间过长
12:10:58 ERROR [SGC] API响应时间过长
12:10:58 ERROR [SGC] API响应时间过长
12:19:42 ERROR [SGC] API响应时间过长
12:19:43 ERROR [SGC] API响应时间过长
12:19:43 ERROR [SGC] API响应时间过长
12:19:44 ERROR [SGC] API响应时间过长
12:19:45 ERROR [SGC] API响应时间过长
12:19:45 ERROR [SGC] API响应时间过长
12:20:22 ERROR [SGC] API响应时间过长
12:23:01 ERROR [SGC] API响应时间过长
12:23:01 ERROR [SGC] API响应时间过长
12:23:57 ERROR [SGC] API响应时间过长
12:23:57 ERROR [SGC] API响应时间过长
13:23:03 ERROR [SGC] API响应时间过长
13:23:04 ERROR [SGC] API响应时间过长
13:23:37 ERROR [SGC] API响应时间过长
13:23:37 ERROR [SGC] API响应时间过长
18:10:11 ERROR [SGC] API响应时间过长
18:10:12 ERROR [SGC] API响应时间过长
18:51:47 ERROR [SGC] API响应时间过长
18:51:47 ERROR [SGC] API响应时间过长
18:51:48 ERROR [SGC] API响应时间过长
18:51:48 ERROR [SGC] API响应时间过长
18:51:48 ERROR [SGC] API响应时间过长
18:51:49 ERROR [SGC] API响应时间过长
18:52:32 ERROR [SGC] API响应时间过长
18:52:32 ERROR [SGC] API响应时间过长
18:53:00 ERROR [SGC] API响应时间过长
18:53:58 ERROR [SGC] API响应时间过长
18:53:59 ERROR [SGC] API响应时间过长
19:00:32 ERROR [SGC] API响应时间过长
19:00:32 ERROR [SGC] API响应时间过长
19:03:04 ERROR [SGC] API响应时间过长
19:09:58 ERROR [SGC] API响应时间过长
19:09:59 ERROR [SGC] API响应时间过长
19:20:12 ERROR [SGC] API响应时间过长
19:20:13 ERROR [SGC] API响应时间过长
19:21:45 ERROR [SGC] API响应时间过长
19:21:45 ERROR [SGC] API响应时间过长
22:09:21 ERROR [SGC] API响应时间过长
22:16:13 ERROR [SGC] API响应时间过长
22:24:49 ERROR [SGC] API响应时间过长
22:48:07 ERROR [SGC] API响应时间过长
11:11:55 ERROR [SGC] API响应时间过长
11:12:47 ERROR [SGC] API响应时间过长
11:17:30 ERROR [SGC] API响应时间过长
11:22:25 ERROR [SGC] API响应时间过长
12:14:07 ERROR [SGC] API响应时间过长
12:19:20 ERROR [SGC] API响应时间过长
12:22:01 ERROR [SGC] API响应时间过长
12:23:47 ERROR [SGC] API响应时间过长
12:38:31 ERROR [SGC] API响应时间过长
12:57:49 ERROR [SGC] API响应时间过长
12:57:50 ERROR [SGC] API响应时间过长
13:23:08 ERROR [SGC] API响应时间过长
14:38:31 ERROR [SGC] API响应时间过长
14:56:13 ERROR [SGC] API响应时间过长
15:38:26 ERROR [SGC] API响应时间过长
11:10:09 ERROR [SGC] API响应时间过长
11:10:11 ERROR [SGC] API响应时间过长
11:10:12 ERROR [SGC] API响应时间过长
11:16:48 ERROR [SGC] API响应时间过长
11:17:46 ERROR [SGC] API响应时间过长
11:20:29 ERROR [SGC] API响应时间过长
11:20:29 ERROR [SGC] API响应时间过长
11:20:29 ERROR [SGC] API响应时间过长
11:26:38 ERROR [SGC] API响应时间过长
16:16:40 ERROR [SGC] API响应时间过长
16:16:40 ERROR [SGC] API响应时间过长
16:16:47 ERROR [SGC] API响应时间过长
16:16:51 ERROR [SGC] API响应时间过长
16:18:35 ERROR [SGC] API响应时间过长
16:24:49 ERROR [SGC] API响应时间过长
16:51:11 ERROR [SGC] API响应时间过长
16:55:10 ERROR [SGC] API响应时间过长
17:43:04 ERROR [SGC] API响应时间过长
17:51:37 ERROR [SGC] API响应时间过长
17:59:08 ERROR [SGC] API响应时间过长
