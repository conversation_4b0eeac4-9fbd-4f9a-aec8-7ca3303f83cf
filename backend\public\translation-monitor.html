<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译监控管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #718096;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-panel {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .control-panel h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .config-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .config-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .report-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .report-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .report-content {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background: #48bb78;
        }

        .status-stopped {
            background: #f56565;
        }

        .status-warning {
            background: #ed8936;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert-info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #718096;
        }

        .error-details {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-left: 4px solid #f56565;
        }

        .error-details h2 {
            color: #e53e3e;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        /* 错误摘要区域 */
        .error-summary {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            color: #742a2a;
        }

        .error-summary .summary-item {
            display: inline-block;
            margin-right: 20px;
            font-size: 0.9rem;
        }

        .error-summary .summary-label {
            font-weight: bold;
        }

        /* 错误列表容器 - 添加滚动功能 */
        #errorList {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            background: #f7fafc;
        }

        /* 自定义滚动条样式 */
        #errorList::-webkit-scrollbar {
            width: 8px;
        }

        #errorList::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        #errorList::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        #errorList::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .error-item {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            color: #742a2a;
        }

        .error-item .error-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .error-item .error-details-text {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .system-status {
            margin-top: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-running {
            background: #48bb78;
            box-shadow: 0 0 10px rgba(72, 187, 120, 0.5);
        }

        .status-stopped {
            background: #f56565;
            box-shadow: 0 0 10px rgba(245, 101, 101, 0.5);
        }

        .status-warning {
            background: #ed8936;
            box-shadow: 0 0 10px rgba(237, 137, 54, 0.5);
        }

        .status-error {
            background: #e53e3e;
            box-shadow: 0 0 10px rgba(229, 62, 62, 0.8);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }





        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-label {
            font-weight: 500;
            color: #4a5568;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }





        /* 引擎配置样式 */
        .current-engine-status {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4299e1;
        }

        .engine-status-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .engine-switch-section {
            background: #fff5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ed8936;
        }

        .engine-config-management {
            background: #f0fff4;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #48bb78;
        }

        .engine-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tab-button {
            padding: 10px 20px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab-button:hover {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .tab-button.active {
            border-color: #4299e1;
            background: #4299e1;
            color: white;
        }

        .engine-config-panel {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .engine-config-panel.active {
            display: block;
        }

        .engine-config-panel h4 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #2d3748;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .config-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .test-results {
            background: #fffaf0;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #ed8936;
        }

        .test-result-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
        }

        .test-result-success {
            border-left: 4px solid #48bb78;
            background: #f0fff4;
        }

        .test-result-error {
            border-left: 4px solid #f56565;
            background: #fffafa;
        }

        .test-result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .test-result-engine {
            font-weight: bold;
            color: #2d3748;
        }

        .test-result-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .test-result-status.success {
            background: #c6f6d5;
            color: #22543d;
        }

        .test-result-status.error {
            background: #fed7d7;
            color: #742a2a;
        }

        .test-result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 0.9rem;
        }

        .test-result-detail {
            display: flex;
            justify-content: space-between;
        }

        .test-result-label {
            color: #4a5568;
            font-weight: 500;
        }

        .test-result-value {
            color: #2d3748;
            font-weight: 600;
        }

        .config-note {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f7fafc;
            border-left: 3px solid #4299e1;
            border-radius: 4px;
        }

        .config-note small {
            color: #4a5568;
            font-style: italic;
        }

        .config-management-section {
            background: #fefefe;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
        }

        .config-status {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f0f4f8;
            border-radius: 4px;
            border-left: 3px solid #38b2ac;
        }

        .config-status small {
            color: #2d3748;
        }

        .btn {
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .engine-health-details {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .health-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .health-item {
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .health-item.healthy {
            border-left: 4px solid #28a745;
        }

        .health-item.unhealthy {
            border-left: 4px solid #dc3545;
        }

        .health-label {
            font-weight: 600;
            color: #495057;
        }

        .health-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .health-status.healthy {
            color: #28a745;
        }

        .health-status.unhealthy {
            color: #dc3545;
        }

        .health-details {
            font-size: 0.9em;
            color: #6c757d;
        }

        .system-health {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .system-health.healthy {
            background: #d4edda;
            color: #155724;
        }

        .system-health.partial {
            background: #fff3cd;
            color: #856404;
        }

        .system-health.critical {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .engine-tabs {
                flex-direction: column;
            }

            .tab-button {
                text-align: center;
            }

            .config-actions {
                flex-direction: column;
            }

            .engine-status-info {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* 扫描结果样式 */
        .scan-results {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .scan-results h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .scan-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .scan-stat {
            background: #f7fafc;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3748;
        }

        .stat-value.highlight {
            color: #e53e3e;
        }

        .stat-value.success {
            color: #38a169;
        }

        .pending-vehicles {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .vehicle-item {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 4px solid #e53e3e;
        }

        .vehicle-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .vehicle-info {
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 5px;
        }

        .incomplete-languages {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .language-tag {
            background: #fed7d7;
            color: #c53030;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🔍 翻译监控管理系统</h1>
            <p>实时监控翻译系统性能，管理监控配置和生成报告</p>
            <div class="system-status">
                <span class="status-indicator" id="systemStatus"></span>
                <span id="systemStatusText">连接中...</span>
            </div>
        </div>

        <!-- 系统状态统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgResponseTime">N/A</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTranslations">0</div>
                <div class="stat-label">总翻译数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="translatedVehicleCount">0</div>
                <div class="stat-label">翻译车辆数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="queueSize">0</div>
                <div class="stat-label">队列大小</div>
            </div>
        </div>



        <!-- 错误详情 -->
        <div class="error-details" id="errorDetails" style="display: none;">
            <h2>❌ 翻译错误详情</h2>
            <div class="error-summary" id="errorSummary"></div>
            <div id="errorList">
                <div class="scroll-hint" style="text-align: center; color: #718096; font-size: 0.9rem; padding: 10px;">
                    📜 错误列表可上下滚动查看
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h2>🎛️ 控制面板</h2>

            <div class="controls">
                <button class="btn btn-primary" onclick="generateReport()">📊 生成性能报告</button>
                <button class="btn btn-danger" onclick="resetMetrics()">🔄 重置指标</button>
                <button class="btn btn-primary" onclick="refreshStatus()">🔄 刷新状态</button>
                <button class="btn btn-info" onclick="scanPendingVehicles()">🔍 扫描待翻译车辆</button>
                <button class="btn btn-success" onclick="triggerAutoTranslation()">🚀 手动触发自动翻译</button>
                <button class="btn btn-warning" onclick="testErrorDisplay()">🧪 测试错误滚动</button>
            </div>
            <div id="controlMessage" class="alert hidden"></div>
        </div>

        <!-- 车辆扫描结果 -->
        <div class="scan-results" id="scanResults" style="display: none;">
            <h2>🔍 车辆翻译状态扫描结果</h2>
            <div class="scan-summary">
                <div class="scan-stat">
                    <span class="stat-label">总车辆数:</span>
                    <span class="stat-value" id="totalVehicles">-</span>
                </div>
                <div class="scan-stat">
                    <span class="stat-label">需要翻译:</span>
                    <span class="stat-value highlight" id="needTranslationCount">-</span>
                </div>
                <div class="scan-stat">
                    <span class="stat-label">已完成:</span>
                    <span class="stat-value success" id="completedCount">-</span>
                </div>
                <div class="scan-stat">
                    <span class="stat-label">扫描时间:</span>
                    <span class="stat-value" id="scanTime">-</span>
                </div>
            </div>
            <div class="pending-vehicles" id="pendingVehicles"></div>
        </div>

        <!-- 监控配置 -->
        <div class="config-section">
            <h2>⚙️ 监控配置</h2>
            <form id="configForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="slowTranslationThreshold">慢翻译阈值 (秒)</label>
                        <input type="number" id="slowTranslationThreshold" min="1" step="1" value="10">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="enableMetrics">启用指标收集</label>
                        <select id="enableMetrics">
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="logLevel">日志级别</label>
                        <select id="logLevel">
                            <option value="debug">Debug</option>
                            <option value="info">Info</option>
                            <option value="warn">Warn</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                </div>
                <button type="button" class="btn btn-primary" onclick="updateConfig()">更新配置</button>
            </form>
        </div>

        <!-- 翻译引擎配置 -->
        <div class="config-section" id="engineConfigSection">
            <h2>🔧 翻译引擎配置</h2>

            <!-- 当前引擎状态 -->
            <div class="current-engine-status">
                <h3>当前引擎状态</h3>
                <div class="engine-status-info">
                    <div class="form-group">
                        <label>主要翻译引擎:</label>
                        <span id="currentEngineDisplay">加载中...</span>
                        <button class="btn btn-primary" onclick="loadEngineConfig()" style="margin-left: 10px;">🔄 刷新配置</button>
                    </div>
                    <div class="form-group">
                        <label>系统健康状态:</label>
                        <span id="systemHealthDisplay">检查中...</span>
                        <button class="btn btn-secondary" onclick="refreshEngineHealth()" style="margin-left: 10px;">🔄 刷新健康状态</button>
                    </div>
                </div>

                <!-- 引擎健康状态详情 -->
                <div class="engine-health-details" id="engineHealthDetails">
                    <h4>引擎健康状态详情</h4>
                    <div class="health-grid" id="healthGrid">
                        <div class="health-item">
                            <span class="health-label">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 引擎切换 -->
            <div class="engine-switch-section">
                <h3>切换翻译引擎</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="engineSelect">选择主要引擎:</label>
                        <select id="engineSelect">
                            <option value="">加载中...</option>
                        </select>
                        <button class="btn btn-warning" onclick="switchEngine()">切换引擎</button>
                    </div>
                </div>
            </div>

            <!-- 引擎配置管理 -->
            <div class="engine-config-management">
                <h3>引擎配置管理</h3>
                <div class="engine-tabs">
                    <button class="tab-button active" onclick="showEngineConfig('baidu')">百度翻译</button>
                    <button class="tab-button" onclick="showEngineConfig('deepseek')">DeepSeek</button>
                </div>

                <!-- 百度翻译配置 -->
                <div id="baiduConfig" class="engine-config-panel active">
                    <h4>百度翻译配置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="baiduAppId">APP ID:</label>
                            <input type="text" id="baiduAppId" placeholder="请输入百度翻译APP ID">
                        </div>
                        <div class="form-group">
                            <label for="baiduSecretKey">密钥:</label>
                            <input type="password" id="baiduSecretKey" placeholder="请输入百度翻译密钥">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="baiduApiUrl">API URL:</label>
                            <input type="url" id="baiduApiUrl" placeholder="https://fanyi-api.baidu.com/api/trans/vip/translate">
                        </div>
                        <div class="form-group">
                            <label for="baiduTimeout">超时时间 (毫秒):</label>
                            <input type="number" id="baiduTimeout" min="1000" step="1000" value="30000">
                        </div>
                    </div>
                    <div class="config-actions">
                        <button class="btn btn-primary" onclick="updateEngineConfig('baidu')">💾 保存配置</button>
                        <button class="btn btn-success" onclick="testEngine('baidu')">🧪 测试连接</button>
                    </div>
                    <div class="config-note">
                        <small>💡 配置将自动保存到 .env 文件，重启后端后仍然有效</small>
                    </div>
                </div>

                <!-- DeepSeek配置 -->
                <div id="deepseekConfig" class="engine-config-panel">
                    <h4>DeepSeek翻译配置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deepseekApiKey">API Key:</label>
                            <input type="password" id="deepseekApiKey" placeholder="请输入DeepSeek API Key">
                        </div>
                        <div class="form-group">
                            <label for="deepseekApiUrl">API URL:</label>
                            <input type="url" id="deepseekApiUrl" placeholder="https://api.deepseek.com/v1/chat/completions">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deepseekTimeout">超时时间 (毫秒):</label>
                            <input type="number" id="deepseekTimeout" min="1000" step="1000" value="30000">
                        </div>
                    </div>
                    <div class="config-actions">
                        <button class="btn btn-primary" onclick="updateEngineConfig('deepseek')">💾 保存配置</button>
                        <button class="btn btn-success" onclick="testEngine('deepseek')">🧪 测试连接</button>
                    </div>
                    <div class="config-note">
                        <small>💡 配置将自动保存到 .env 文件，重启后端后仍然有效</small>
                    </div>
                </div>


            </div>

            <!-- 配置管理 -->
            <div class="config-management-section">
                <h3>配置管理</h3>
                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-info" onclick="createConfigBackup()">📋 创建配置备份</button>
                        <button class="btn btn-warning" onclick="restoreConfigBackup()">🔄 恢复配置备份</button>
                        <button class="btn btn-secondary" onclick="reloadConfig()">♻️ 重新加载配置</button>
                    </div>
                </div>
                <div class="config-status" id="configStatus">
                    <small>配置状态：<span id="configStatusText">检查中...</span></small>
                </div>
            </div>

            <!-- 测试结果显示 -->
            <div id="testResults" class="test-results" style="display: none;">
                <h3>测试结果</h3>
                <div id="testResultContent"></div>
            </div>
        </div>

        <!-- 性能报告 -->
        <div class="report-section">
            <h2>📊 性能报告</h2>
            <div class="controls" style="margin-bottom: 20px;">
                <span>系统状态: <span class="status-indicator" id="systemStatus"></span><span id="systemStatusText">检查中...</span></span>
            </div>
            <div id="reportContent" class="report-content">
                点击"生成性能报告"或"刷新状态"来查看最新数据...
            </div>
        </div>
    </div>

    <script src="translation-monitor.js"></script>
</body>
</html>
