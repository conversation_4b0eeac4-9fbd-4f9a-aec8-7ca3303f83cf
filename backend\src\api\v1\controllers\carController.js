/**
 * 商品控制器
 */
const { Product, User, Dealer, Brand, Series, Model, ModelSpec, sequelize } = require('../../../models');
const { validateInput, isValidStatus } = require('../../../utils/validators');
const { getPagination, getPagingData } = require('../../../utils/pagination');
const { TranslationService } = require('../../../services/translationService');
const { Op } = require('sequelize');
const { handleProductOnShelf, handleProductUpdate } = require('../../../services/autoTranslationService');
const { createProductOptimized } = require('../../../services/productCreationService');

/**
 * 增强的产品数据验证函数
 */
function validateProductData(data) {
  const errors = [];

  // 必填字段验证
  if (!data.title || data.title.trim().length === 0) {
    errors.push({ field: 'title', message: '商品标题不能为空' });
  } else if (data.title.length > 200) {
    errors.push({ field: 'title', message: '商品标题不能超过200个字符' });
  }

  if (!data.price || isNaN(data.price) || data.price <= 0) {
    errors.push({ field: 'price', message: '商品价格必须是大于0的数字' });
  }

  if (!data.modelId || isNaN(data.modelId)) {
    errors.push({ field: 'modelId', message: '车型ID必须是有效数字' });
  }

  // 可选字段验证
  if (data.mileage && (isNaN(data.mileage) || data.mileage < 0)) {
    errors.push({ field: 'mileage', message: '里程数必须是非负数字' });
  }

  if (data.manufactureDate && !isValidDate(data.manufactureDate)) {
    errors.push({ field: 'manufactureDate', message: '生产日期格式无效' });
  }

  if (data.firstRegistration && !isValidDate(data.firstRegistration)) {
    errors.push({ field: 'firstRegistration', message: '首次注册日期格式无效' });
  }

  // 图片验证
  if (data.images && Array.isArray(data.images) && data.images.length > 9) {
    errors.push({ field: 'images', message: '图片数量不能超过9张' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 日期验证辅助函数
 */
function isValidDate(dateString) {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
}

/**
 * 设置缺省值
 */
const setDefaultValues = (product) => {
  // 设置默认的活动状态
  if (!product.status) {
    product.status = 'active';
  }

  // 如果有出版日期，进行转换
  if (product.publishedAt && typeof product.publishedAt === 'string') {
    product.publishedAt = new Date(product.publishedAt);
  } else if (!product.publishedAt) {
    product.publishedAt = new Date();
  }

  // 如果没有上传日期，设置为当前时间
  if (!product.createdAt) {
    product.createdAt = new Date();
  }

  // 如果没有更新日期，设置为当前时间
  if (!product.updatedAt) {
    product.updatedAt = new Date();
  }

  // 设置最低价格为0
  if (product.price < 0) {
    product.price = 0;
  }

  // 确保里程数为正数
  if (product.mileage < 0) {
    product.mileage = 0;
  }

  // 确保VIN码为大写
  if (product.vin) {
    product.vin = product.vin.toUpperCase();
  }

  return product;
};

/**
 * 搜索商品列表
 */
exports.searchProductList = async (req, res) => {
  try {
    // 1. 提取查询参数
    const { 
      page = 1, 
      limit = 10,
      sort = 'createdAt',
      order = 'DESC',
      keyword,
      brand,
      series,
      minPrice,
      maxPrice,
      status = 'active',
      dealerId
    } = req.query;

    // 2. 计算分页参数
    const { offset, limit: limitValue } = getPagination(page, limit);

    // 3. 构建查询条件
    const whereClause = {};
    const sortOrders = [];

    // 3.1 处理状态过滤
    if (status && isValidStatus(status)) {
      whereClause.status = status;
    } else if (status === 'all') {
      // 不添加状态过滤
    } else {
      // 默认只显示活跃商品
      whereClause.status = 'active';
    }

    // 3.2 处理价格范围
    if (minPrice && !isNaN(minPrice)) {
      whereClause.price = { ...whereClause.price, [Op.gte]: minPrice };
    }

    if (maxPrice && !isNaN(maxPrice)) {
      whereClause.price = { ...whereClause.price, [Op.lte]: maxPrice };
    }

    // 3.3 处理关键词搜索
    if (keyword) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } },
        { vin: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 3.4 处理品牌过滤
    if (brand) {
      whereClause.brandId = brand;
    }

    // 3.5 处理车系过滤
    if (series) {
      whereClause.seriesId = series;
    }

    // 3.6 处理经销商过滤
    if (dealerId) {
      whereClause.dealerId = dealerId;
    }

    // 3.7 处理排序
    if (sort) {
      const direction = order && (order.toUpperCase() === 'ASC' || order.toUpperCase() === 'DESC') 
        ? order.toUpperCase() 
        : 'DESC';
      sortOrders.push([sort, direction]);
    } else {
      // 默认排序：创建时间降序
      sortOrders.push(['createdAt', 'DESC']);
    }

    // 4. 执行查询
    const products = await Product.findAndCountAll({
      where: whereClause,
      limit: limitValue,
      offset: offset,
      order: sortOrders,
      include: [
        {
          model: Dealer,
          as: 'dealer',
          attributes: ['id', 'name', 'logo', 'address', 'phone']
        },
        {
          model: Brand,
          as: 'brand',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Series,
          as: 'series',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Model,
          as: 'model',
          attributes: ['id', 'name']
        },
        {
          model: ModelSpec,
          as: 'modelSpec',
          attributes: ['id', 'name', 'year']
        }
      ]
    });

    // 5. 格式化响应数据
    const response = getPagingData(products, page, limitValue);

    // 6. 返回搜索结果
    return res.status(200).json({
      success: true,
      message: '获取商品列表成功',
      ...response
    });
  } catch (error) {
    console.error('搜索商品列表出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取商品列表失败: ' + error.message
    });
  }
};

/**
 * 获取商品详情
 */
exports.getProductDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false, 
        message: '商品ID不能为空'
      });
    }

    // 查询商品详情，包括关联信息
    const product = await Product.findOne({
      where: { id },
      include: [
        {
          model: Dealer,
          as: 'dealer',
          attributes: ['id', 'name', 'logo', 'address', 'phone'],
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'username', 'avatar', 'email']
            }
          ]
        },
        {
          model: Brand,
          as: 'brand',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Series,
          as: 'series',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Model,
          as: 'model',
          attributes: ['id', 'name']
        },
        {
          model: ModelSpec,
          as: 'modelSpec',
          attributes: ['id', 'name', 'year']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false, 
        message: '未找到该商品'
      });
    }

    // 返回商品详情
    return res.status(200).json({
      success: true,
      message: '获取商品详情成功',
      data: product
    });
  } catch (error) {
    console.error('获取商品详情出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取商品详情失败: ' + error.message
    });
  }
};

/**
 * 获取车商的商品列表
 */
exports.getDealerProducts = async (req, res) => {
  try {
    const { id: userId } = req.user;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      });
    }

    // 查找用户关联的车商
    const dealer = await Dealer.findOne({
      where: { userId }
    });

    if (!dealer) {
      return res.status(403).json({
        success: false,
        message: '用户不是车商'
      });
    }

    // 提取查询参数
    const { 
      page = 1, 
      limit = 10,
      status = 'all'
    } = req.query;

    // 计算分页
    const { offset, limit: limitValue } = getPagination(page, limit);

    // 构建查询条件
    const whereClause = { dealerId: dealer.id };
    
    // 处理状态过滤
    if (status && status !== 'all' && isValidStatus(status)) {
      whereClause.status = status;
    }

    // 查询车商的商品列表
    const products = await Product.findAndCountAll({
      where: whereClause,
      limit: limitValue,
      offset: offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Brand,
          as: 'brand',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Series,
          as: 'series',
          attributes: ['id', 'name', 'logo']
        },
        {
          model: Model,
          as: 'model',
          attributes: ['id', 'name']
        },
        {
          model: ModelSpec,
          as: 'modelSpec',
          attributes: ['id', 'name', 'year']
        }
      ]
    });

    // 格式化响应
    const response = getPagingData(products, page, limitValue);

    return res.status(200).json({
      success: true,
      message: '获取车商商品列表成功',
      dealerId: dealer.id,
      dealerName: dealer.name,
      ...response,
      products: response.items, // 兼容前端
      totalItems: response.totalItems
    });
  } catch (error) {
    console.error('获取车商商品列表出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取车商商品列表失败: ' + error.message
    });
  }
};

/**
 * 创建商品 - 优化版本
 */
exports.createProduct = async (req, res) => {
  const startTime = Date.now();

  try {
    console.log('开始优化的商品创建流程...');

    // 1. 身份验证
    const { user } = req;
    if (!user || !user.id) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        code: 'UNAUTHORIZED'
      });
    }

    // 2. 获取上传的文件
    const files = req.files || [];
    console.log(`接收到 ${files.length} 个图片文件`);

    // 3. 使用优化的创建服务
    const result = await createProductOptimized(user, req.body, files);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error,
        code: 'CREATION_FAILED',
        creationLog: result.creationLog
      });
    }

    // 4. 记录性能指标
    const totalTime = Date.now() - startTime;
    console.log(`商品创建完成，总耗时: ${totalTime}ms`);

    // 5. 返回成功响应
    return res.status(201).json({
      success: true,
      message: '商品创建成功',
      data: {
        productId: result.product.productId,
        title: result.product.title,
        price: result.product.price,
        status: result.product.status,
        imageCount: result.imageCount,
        createdAt: result.product.createdAt
      },
      performance: {
        totalTime: `${totalTime}ms`,
        steps: result.creationLog.steps.length,
        imageProcessed: result.imageCount
      }
    });

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('创建商品出错:', error);

    // 处理不同类型的错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '数据库验证失败',
        code: 'DATABASE_VALIDATION_ERROR',
        errors: error.errors.map(e => ({ field: e.path, message: e.message })),
        performance: { totalTime: `${totalTime}ms` }
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        success: false,
        message: '商品已存在',
        code: 'DUPLICATE_PRODUCT',
        performance: { totalTime: `${totalTime}ms` }
      });
    }

    return res.status(500).json({
      success: false,
      message: '创建商品失败: ' + error.message,
      code: 'INTERNAL_ERROR',
      performance: { totalTime: `${totalTime}ms` }
    });
  }
};

/**
 * 更新商品信息
 */
exports.updateProduct = async (req, res) => {
  try {
    const { productId } = req.params; // 修改为productId，与路由参数名一致
    const { id: userId } = req.user;
    const updateData = req.body;

    console.log('更新商品请求 - productId:', productId, 'userId:', userId);

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    // 查找要更新的商品 - 使用productId字段查找
    const product = await Product.findOne({
      where: { productId: productId }
    });

    console.log('查找商品结果:', product ? '找到商品' : '未找到商品');
    if (product) {
      console.log('商品信息:', {
        productId: product.productId,
        dealerId: product.dealerId,
        title: product.title
      });
    }

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '未找到该商品'
      });
    }

    // 验证用户是否有权限更新（检查是否为车商所有者）
    // 注意：JWT中的id就是dealerId，不需要再查找dealer表
    console.log('权限检查 - product.dealerId:', product.dealerId, 'userId:', userId, '类型:', typeof product.dealerId, typeof userId);

    if (product.dealerId !== userId) {
      console.log('权限检查失败 - 车商ID不匹配');
      return res.status(403).json({
        success: false,
        message: '没有权限更新此商品'
      });
    }

    console.log('权限检查通过，开始更新商品');

    // 设置默认值
    const dataToUpdate = setDefaultValues({
      ...product.toJSON(),
      ...updateData
    });

    // 获取原始产品数据用于比较
    const originalProduct = product.toJSON();

    // 更新商品
    console.log('🔄 开始更新商品数据...');
    await product.update(dataToUpdate);
    console.log('✅ 商品数据更新完成');

    // 🌍 检测字段变更并触发智能翻译
    console.log(`🌍 产品 ${product.productId} 更新完成，检测字段变更并触发智能翻译`);

    // 导入需要翻译的字段列表
    const { getProductTranslatableFields } = require('../../../services/translationService');

    // 检测变更的字段
    const changedFields = {};
    const translatableFields = getProductTranslatableFields();

    for (const field of translatableFields) {
      if (dataToUpdate.hasOwnProperty(field) && originalProduct[field] !== dataToUpdate[field]) {
        changedFields[field] = dataToUpdate[field];
        console.log(`🔍 检测到需要翻译的字段变更: ${field}`);
      }
    }

    // 检查handleProductUpdate函数是否存在
    if (typeof handleProductUpdate === 'function') {
      if (Object.keys(changedFields).length > 0) {
        console.log(`✅ 发现 ${Object.keys(changedFields).length} 个需要翻译的字段变更，开始调用智能翻译...`);
        // 异步触发智能翻译，传递变更的字段
        handleProductUpdate(product.productId, changedFields, {
          useQueue: true,
          priority: 'normal'
        }).catch(error => {
          console.error(`❌ 智能翻译更新产品 ${product.productId} 失败:`, error);
        });
      } else {
        console.log(`✅ 没有需要翻译的字段变更，跳过翻译`);
      }
    } else {
      console.error('❌ handleProductUpdate函数不存在！');
    }

    // 返回更新后的商品
    return res.status(200).json({
      success: true,
      message: '商品更新成功',
      data: product
    });
  } catch (error) {
    console.error('更新商品出错:', error);
    
    // 处理验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '商品数据验证失败',
        errors: error.errors.map(e => ({ field: e.path, message: e.message }))
      });
    }
    
    return res.status(500).json({
      success: false,
      message: '更新商品失败: ' + error.message
    });
  }
};

/**
 * 删除商品
 */
exports.removeProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { id: userId } = req.user;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    // 查找要删除的商品
    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '未找到该商品'
      });
    }

    // 验证用户是否有权限删除（检查是否为车商所有者）
    const dealer = await Dealer.findOne({ where: { userId } });
    
    if (!dealer || product.dealerId !== dealer.id) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除此商品'
      });
    }

    // 软删除商品（将状态设置为deleted）
    await product.update({ status: 'deleted', deletedAt: new Date() });

    // 返回结果
    return res.status(200).json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    console.error('删除商品出错:', error);
    return res.status(500).json({
      success: false,
      message: '删除商品失败: ' + error.message
    });
  }
};

/**
 * 修改商品状态
 */
exports.changeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const { id: userId } = req.user;

    // 验证输入
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    if (!status || !isValidStatus(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    // 查找要更新的商品
    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '未找到该商品'
      });
    }

    // 验证用户是否有权限更新（检查是否为车商所有者）
    const dealer = await Dealer.findOne({ where: { userId } });
    
    if (!dealer || product.dealerId !== dealer.id) {
      return res.status(403).json({
        success: false,
        message: '没有权限更新此商品状态'
      });
    }

    // 更新状态
    await product.update({ 
      status,
      updatedAt: new Date(),
      
      // 如果状态为sold，设置soldAt字段
      ...(status === 'sold' ? { soldAt: new Date() } : {}),
      
      // 如果状态为expired，设置expiredAt字段
      ...(status === 'expired' ? { expiredAt: new Date() } : {})
    });

    // 返回结果
    return res.status(200).json({
      success: true,
      message: '商品状态更新成功',
      data: {
        id: product.productId,
        status: product.status
      }
    });
  } catch (error) {
    console.error('更新商品状态出错:', error);
    return res.status(500).json({
      success: false,
      message: '更新商品状态失败: ' + error.message
    });
  }
};

/**
 * 上架商品
 * 与createProduct类似，但有不同的逻辑和响应格式
 */
exports.listProduct = async (req, res) => {
  try {
    const { id: userId } = req.user;
    
    // 查找用户关联的车商
    const dealer = await Dealer.findOne({
      where: { userId }
    });

    if (!dealer) {
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法上架商品'
      });
    }

    // 从请求体获取商品数据
    let productData = { ...req.body };
    
    // 设置经销商ID
    productData.dealerId = dealer.id;

    // 验证必要字段
    if (!productData.title) {
      return res.status(400).json({
        success: false,
        message: '商品标题不能为空'
      });
    }

    // 如果有modelId，从config_car表获取车辆规格信息
    if (productData.modelId) {
      try {
        const { ConfigCar } = require('../../../models');
        const carConfig = await ConfigCar.findOne({
          where: { modelId: productData.modelId.toString() },
          attributes: ['energyType', 'bodyStructure', 'gearboxType', 'driveType', 'brandName'],
          raw: true
        });

        if (carConfig) {
          console.log('从config_car表获取到车辆规格信息:', carConfig);

          // 设置车辆规格字段
          productData.energyType = carConfig.energyType;
          productData.bodyStructure = carConfig.bodyStructure;
          productData.gearboxType = carConfig.gearboxType;
          productData.driveType = carConfig.driveType;

          // 如果没有设置品牌，从config_car表获取
          if (!productData.brand && carConfig.brandName) {
            productData.brand = carConfig.brandName;
          }
        } else {
          console.warn(`未找到modelId ${productData.modelId} 对应的车型配置信息`);
        }
      } catch (configError) {
        console.error('获取车型配置信息失败:', configError);
        // 不阻止商品创建，只是记录错误
      }
    }

    // 设置上架状态
    productData.status = 'active';
    productData.publishedAt = new Date();

    // 设置默认值
    productData = setDefaultValues(productData);

    // 使用事务创建商品记录和触发翻译
    const TransactionService = require('../../../services/transactionService');
    const { product, needsTranslation } = await TransactionService.executeProductCreationWithTranslation(
      productData,
      null // 翻译触发函数将在事务外执行
    );

    // 事务提交后异步执行翻译处理，不阻塞响应
    if (needsTranslation) {
      console.log(`新产品 ${product.productId} 状态为 active，触发自动翻译`);

      // 统一使用旧翻译系统（内部已经整合了新翻译引擎的降级机制）
      handleProductOnShelf(product.productId).catch(error => {
        console.error(`自动翻译新产品 ${product.productId} 失败:`, error);
      });
    }

    // 返回上架的商品
    return res.status(201).json({
      success: true,
      message: '商品上架成功',
      data: {
        id: product.productId,
        title: product.title,
        price: product.price,
        status: product.status,
        publishedAt: product.publishedAt
      }
    });
  } catch (error) {
    console.error('上架商品出错:', error);
    
    // 处理验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '商品数据验证失败',
        errors: error.errors.map(e => ({ field: e.path, message: e.message }))
      });
    }
    
    return res.status(500).json({
      success: false,
      message: '上架商品失败: ' + error.message
    });
  }
};

/**
 * 重新翻译商品
 */
exports.translateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { id: userId } = req.user;

    // 检查商品ID
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    // 查找商品
    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '未找到该商品'
      });
    }

    // 验证用户是否有权限（检查是否为车商所有者）
    const dealer = await Dealer.findOne({ where: { userId } });
    
    if (!dealer || product.dealerId !== dealer.id) {
      return res.status(403).json({
        success: false,
        message: '没有权限翻译此商品'
      });
    }

    // 调用翻译服务
    const translationService = new TranslationService();
    
    // 获取需要翻译的字段
    const { title, description } = product;
    
    // 翻译标题和描述
    const translatedTitle = await translationService.translateText(title, 'auto', 'en');
    const translatedDescription = description 
      ? await translationService.translateText(description, 'auto', 'en') 
      : '';

    // 更新商品的国际化字段
    await product.update({
      titleEn: translatedTitle,
      descriptionEn: translatedDescription,
      translatedAt: new Date()
    });

    // 返回翻译结果
    return res.status(200).json({
      success: true,
      message: '商品翻译成功',
      data: {
        id: product.productId,
        title: product.title,
        titleEn: product.titleEn,
        description: product.description,
        descriptionEn: product.descriptionEn,
        translatedAt: product.translatedAt
      }
    });
  } catch (error) {
    console.error('翻译商品出错:', error);
    return res.status(500).json({
      success: false,
      message: '翻译商品失败: ' + error.message
    });
  }
}; 