/**
 * 测试翻译引擎直接调用
 */

require('dotenv').config();

async function testTranslationEngines() {
    console.log('🧪 测试翻译引擎直接调用...\n');

    try {
        // 1. 测试百度翻译
        console.log('1. 测试百度翻译...');
        const { translateWithBaidu } = require('./src/services/baiduTranslationService');
        const baiduResult = await translateWithBaidu('测试文本', 'zh', 'en');
        console.log('百度翻译结果:', {
            success: baiduResult.success,
            error: baiduResult.error,
            translatedText: baiduResult.translatedText
        });

        // 2. 测试DeepSeek翻译
        console.log('\n2. 测试DeepSeek翻译...');
        const { translateWithDeepseek } = require('./src/services/deepseekTranslationService');
        const deepseekResult = await translateWithDeepseek('测试文本', 'zh', 'en');
        console.log('DeepSeek翻译结果:', {
            success: deepseekResult.success,
            error: deepseekResult.error,
            translatedText: deepseekResult.translatedText
        });

        // 3. 测试旧翻译系统
        console.log('\n3. 测试旧翻译系统...');
        const translationService = require('./src/services/translationService');
        const oldSystemResult = await translationService.translate('测试文本', 'zh', 'en', { productId: 'test' });
        console.log('旧翻译系统结果:', {
            success: oldSystemResult.success,
            engine: oldSystemResult.engine,
            error: oldSystemResult.error,
            translatedText: oldSystemResult.translatedText
        });

        // 4. 测试核心翻译引擎
        console.log('\n4. 测试核心翻译引擎...');
        const TranslationEngines = require('./src/services/core/TranslationEngines');
        const logger = { warn: console.warn, error: console.error, info: console.info };
        const engines = new TranslationEngines(logger);
        
        // 测试百度引擎
        const coreEngineResult = await engines.executeTranslation('baidu', '测试文本', 'zh', 'en');
        console.log('核心翻译引擎结果:', {
            success: coreEngineResult.success,
            error: coreEngineResult.error,
            translatedText: coreEngineResult.translatedText
        });

        // 5. 分析结果
        console.log('\n📊 结果分析:');
        console.log('============');
        
        if (baiduResult.success) {
            console.log('✅ 百度翻译直接调用成功');
        } else {
            console.log(`❌ 百度翻译直接调用失败: ${baiduResult.error}`);
        }
        
        if (deepseekResult.success) {
            console.log('✅ DeepSeek翻译直接调用成功');
        } else {
            console.log(`❌ DeepSeek翻译直接调用失败: ${deepseekResult.error}`);
        }
        
        if (oldSystemResult.success) {
            console.log(`✅ 旧翻译系统成功，使用引擎: ${oldSystemResult.engine}`);
        } else {
            console.log(`❌ 旧翻译系统失败: ${oldSystemResult.error}`);
        }
        
        if (coreEngineResult.success) {
            console.log('✅ 核心翻译引擎成功');
        } else {
            console.log(`❌ 核心翻译引擎失败: ${coreEngineResult.error}`);
        }

        // 6. 结论
        console.log('\n🎯 结论:');
        if (oldSystemResult.success && !coreEngineResult.success) {
            console.log('问题确认: 旧翻译系统有降级机制，核心翻译引擎没有');
            console.log('解决方案: 核心翻译引擎需要实现降级机制');
        } else if (!oldSystemResult.success && !coreEngineResult.success) {
            console.log('问题确认: 所有翻译引擎都不可用，需要检查API密钥');
        } else {
            console.log('需要进一步分析...');
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 运行测试
testTranslationEngines().catch(console.error);
