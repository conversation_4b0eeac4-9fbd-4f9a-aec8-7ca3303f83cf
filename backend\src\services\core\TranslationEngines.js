/**
 * 翻译引擎管理器
 * 统一管理所有翻译引擎，支持自动降级和重试
 */

const { 
  TRANSLATION_ENGINES,
  RETRY_CONFIG,
  getCurrentEngineConfig,
  getEnginesPriority,
  isSupportedLanguage 
} = require('../../config/translationConfig');

// 注意：现在统一使用旧翻译系统的逻辑，不再直接导入翻译服务

class TranslationEngines {
  constructor(logger, translationManager = null) {
    this.logger = logger;
    this.translationManager = translationManager;
    
    // 引擎状态
    this.engineStatus = {};
    
    // 熔断器状态
    this.circuitBreakers = new Map();
    
    // 统计信息
    this.stats = {
      totalTranslations: 0,
      successfulTranslations: 0,
      failedTranslations: 0,
      engineUsage: {},
      averageResponseTime: 0
    };
    
    // 初始化引擎状态
    this.initializeEngineStatus();
  }

  /**
   * 初始化翻译引擎
   */
  async initialize() {
    this.logger.info('初始化翻译引擎管理器...');

    // 只在明确启用健康检查时才执行
    if (process.env.TRANSLATION_ENABLE_STARTUP_HEALTH_CHECK === 'true') {
      await this.checkEnginesHealth();
    } else {
      // 默认将所有引擎标记为可用，在实际使用时再检查
      this.initializeEngineStatusAsAvailable();
      this.logger.info('跳过启动时健康检查，将在首次使用时检查引擎状态');
    }

    this.logger.info('翻译引擎管理器初始化完成');
  }

  /**
   * 初始化引擎状态
   */
  initializeEngineStatus() {
    Object.keys(TRANSLATION_ENGINES).forEach(engine => {
      this.engineStatus[engine] = {
        available: false, // 默认设为不可用，需要健康检查
        lastError: null,
        errorCount: 0,
        lastUsed: null,
        responseTime: 0
      };

      this.stats.engineUsage[engine] = {
        requests: 0,
        successes: 0,
        failures: 0,
        totalResponseTime: 0
      };
    });
  }

  /**
   * 将所有引擎状态初始化为可用（跳过健康检查时使用）
   */
  initializeEngineStatusAsAvailable() {
    Object.keys(TRANSLATION_ENGINES).forEach(engine => {
      this.engineStatus[engine] = {
        available: true, // 假设可用，实际使用时再检查
        lastError: null,
        errorCount: 0,
        lastUsed: null,
        responseTime: 0
      };

      this.stats.engineUsage[engine] = {
        requests: 0,
        successes: 0,
        failures: 0,
        totalResponseTime: 0
      };
    });

    this.logger.info('所有翻译引擎状态已初始化为可用');
  }

  /**
   * 重新初始化翻译引擎
   * 用于配置更新后重新加载引擎状态
   */
  async reinitialize() {
    try {
      this.logger.info('正在重新初始化翻译引擎...');

      // 重新加载配置
      delete require.cache[require.resolve('../../config/translationConfig')];
      const {
        TRANSLATION_ENGINES,
        getEnginesPriority
      } = require('../../config/translationConfig');

      // 重置引擎状态
      this.engines = {};
      this.stats = {
        totalRequests: 0,
        totalSuccesses: 0,
        totalFailures: 0,
        engineUsage: {}
      };

      // 重新初始化引擎状态
      const engines = getEnginesPriority();
      engines.forEach(engine => {
        this.engines[engine] = {
          available: true,
          errorCount: 0,
          lastUsed: null,
          responseTime: 0
        };

        this.stats.engineUsage[engine] = {
          requests: 0,
          successes: 0,
          failures: 0,
          totalResponseTime: 0
        };
      });

      this.logger.info('翻译引擎重新初始化完成');
      return { success: true, message: '翻译引擎重新初始化成功' };

    } catch (error) {
      this.logger.error('重新初始化翻译引擎失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 翻译单个文本 - 新的错误处理逻辑
   * @param {string} text - 待翻译文本
   * @param {string} from - 源语言
   * @param {string} to - 目标语言
   * @param {Object} options - 翻译选项
   * @returns {Promise<Object>} - 翻译结果
   */
  async translate(text, from = 'zh', to = 'en', options = {}) {
    // 直接使用旧翻译系统的统一翻译函数，它已经有完整的降级机制
    const translationService = require('../translationService');

    try {
      const result = await translationService.translate(text, from, to, options);

      // 确保返回格式与新系统兼容
      return {
        success: result.success,
        translatedText: result.translatedText,
        error: result.error,
        engine: result.engine || 'unknown'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        translatedText: `[${to.toUpperCase()}] ${text}`,
        engine: 'error'
      };
    }
  }

  /**
   * 翻译到多种语言 - 新的错误处理逻辑
   * @param {string} text - 待翻译文本
   * @param {Array} targetLanguages - 目标语言列表
   * @param {string} fromLanguage - 源语言
   * @param {Object} options - 翻译选项
   * @returns {Promise<Object>} - 翻译结果
   */
  async translateToMultipleLanguages(text, targetLanguages, fromLanguage = 'zh', options = {}) {
    const startTime = Date.now();
    const productId = options.productId || 'unknown';

    try {
      // 参数验证
      if (!text || typeof text !== 'string') {
        throw new Error('待翻译文本不能为空');
      }

      const validLanguages = targetLanguages.filter(lang => isSupportedLanguage(lang));
      if (validLanguages.length === 0) {
        throw new Error('没有有效的目标语言');
      }

      // 获取引擎优先级列表
      const engines = getEnginesPriority();

      // 对每个引擎进行重试逻辑
      for (let engineIndex = 0; engineIndex < engines.length; engineIndex++) {
        const engineName = engines[engineIndex];

        if (!this.isEngineAvailable(engineName)) {
          continue;
        }

        // 对当前引擎进行重试（最多2次：初次 + 重试1次）
        const maxRetries = 2;
        let lastError = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            // 简化日志显示
            if (attempt === 1 && engineIndex === 0) {
              this.logger.info(`产品 ${productId} 开始多语言翻译`);
            } else if (attempt === 1 && engineIndex > 0) {
              this.logger.info(`产品 ${productId} 切换到备用 ${engineName} 翻译`);
            }

            const result = await this.executeMultipleLanguagesTranslation(engineName, text, validLanguages, fromLanguage);

            if (result.success) {
              // 翻译成功
              this.recordEngineSuccess(engineName, Date.now() - startTime);
              this.stats.totalTranslations++;
              this.stats.successfulTranslations++;

              this.logger.info(`产品 ${productId} 多语言翻译成功`);
              return result;
            } else {
              lastError = new Error(result.error);
              this.recordEngineFailure(engineName, lastError);
            }

          } catch (error) {
            lastError = error;
            this.recordEngineFailure(engineName, error);
            // 不显示详细的API错误信息
          }

          // 如果不是最后一次尝试，等待后重试
          if (attempt < maxRetries) {
            const delay = 1000; // 固定1秒延迟
            await this.sleep(delay);
          }
        }
      }

      // 所有引擎都失败了
      this.stats.totalTranslations++;
      this.stats.failedTranslations++;

      this.logger.error(`产品 ${productId} 多语言翻译失败，取消翻译`);

      return {
        success: false,
        error: '所有翻译引擎都不可用',
        translations: {}
      };

    } catch (error) {
      this.logger.error(`产品 ${productId} 多语言翻译异常: ${error.message}`);

      return {
        success: false,
        error: error.message,
        translations: {}
      };
    }
  }

  /**
   * 执行单个翻译
   * @param {string} engineName - 引擎名称
   * @param {string} text - 待翻译文本
   * @param {string} from - 源语言
   * @param {string} to - 目标语言
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeTranslation(engineName, text, from, to) {
    // 直接使用旧翻译系统的统一翻译函数，它已经有完整的降级机制
    const translationService = require('../translationService');

    try {
      const result = await translationService.translate(text, from, to, {
        productId: 'core-engine',
        isFirstField: true
      });

      // 确保返回格式与新系统兼容
      return {
        success: result.success,
        translatedText: result.translatedText,
        error: result.error,
        engine: result.engine || 'unknown'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        translatedText: `[${to.toUpperCase()}] ${text}`,
        engine: 'error'
      };
    }
  }

  /**
   * 执行多语言翻译
   * @param {string} engineName - 引擎名称
   * @param {string} text - 待翻译文本
   * @param {Array} languages - 目标语言列表
   * @param {string} fromLanguage - 源语言
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeMultipleLanguagesTranslation(engineName, text, languages, fromLanguage) {
    // 直接使用旧翻译系统的多语言翻译函数，它已经有完整的降级机制
    const translationService = require('../translationService');

    try {
      const result = await translationService.translateToMultipleLanguages(text, languages, fromLanguage);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error.message,
        translations: {}
      };
    }
  }

  /**
   * 检查引擎是否可用
   * @param {string} engineName - 引擎名称
   * @returns {boolean} - 是否可用
   */
  isEngineAvailable(engineName) {
    const status = this.engineStatus[engineName];
    if (!status) {
      return false;
    }

    // 检查熔断器状态
    if (this.isCircuitBreakerOpen(engineName)) {
      return false;
    }

    return status.available;
  }

  /**
   * 检查熔断器是否打开
   * @param {string} engineName - 引擎名称
   * @returns {boolean} - 熔断器是否打开
   */
  isCircuitBreakerOpen(engineName) {
    const breaker = this.circuitBreakers.get(engineName);
    if (!breaker) {
      return false;
    }

    const now = Date.now();
    
    // 检查是否超过重置时间
    if (now - breaker.openedAt > RETRY_CONFIG.circuitBreaker.resetTimeout) {
      this.circuitBreakers.delete(engineName);
      this.logger.info(`熔断器已重置: ${engineName}`);
      return false;
    }

    return breaker.isOpen;
  }

  /**
   * 记录引擎成功
   * @param {string} engineName - 引擎名称
   * @param {number} responseTime - 响应时间
   */
  recordEngineSuccess(engineName, responseTime) {
    const status = this.engineStatus[engineName];
    const usage = this.stats.engineUsage[engineName];

    // 更新状态
    status.lastUsed = new Date();
    status.responseTime = responseTime;
    status.errorCount = 0;
    status.available = true;

    // 更新统计
    usage.requests++;
    usage.successes++;
    usage.totalResponseTime += responseTime;

    // 重置熔断器
    this.circuitBreakers.delete(engineName);
  }

  /**
   * 记录引擎失败
   * @param {string} engineName - 引擎名称
   * @param {Error} error - 错误信息
   */
  recordEngineFailure(engineName, error) {
    const status = this.engineStatus[engineName];
    const usage = this.stats.engineUsage[engineName];

    // 更新状态
    status.lastError = error.message;
    status.errorCount++;
    status.lastUsed = new Date();

    // 更新统计
    usage.requests++;
    usage.failures++;

    // 检查是否需要触发熔断器
    if (status.errorCount >= RETRY_CONFIG.circuitBreaker.threshold) {
      this.openCircuitBreaker(engineName);
    }
  }

  /**
   * 打开熔断器
   * @param {string} engineName - 引擎名称
   */
  openCircuitBreaker(engineName) {
    this.circuitBreakers.set(engineName, {
      isOpen: true,
      openedAt: Date.now(),
      failureCount: this.engineStatus[engineName].errorCount
    });

    this.logger.warn(`熔断器已打开: ${engineName}`, { 
      errorCount: this.engineStatus[engineName].errorCount 
    });
  }

  /**
   * 检查引擎健康状态
   * @param {boolean} skipApiCall - 是否跳过实际API调用，只检查配置
   */
  async checkEnginesHealth(skipApiCall = false) {
    const engines = Object.keys(TRANSLATION_ENGINES);

    for (const engineName of engines) {
      try {
        if (skipApiCall) {
          // 只检查配置是否完整，不进行实际API调用
          const hasValidConfig = this.checkEngineConfig(engineName);
          this.engineStatus[engineName].available = hasValidConfig;

          if (hasValidConfig) {
            this.logger.debug(`引擎配置检查通过: ${engineName}`);
          } else {
            this.logger.warn(`引擎配置检查失败: ${engineName}`);
          }
        } else {
          // 进行实际的API调用健康检查
          const result = await this.executeTranslation(engineName, 'test', 'zh', 'en');

          if (result.success) {
            this.engineStatus[engineName].available = true;
            this.logger.debug(`引擎健康检查通过: ${engineName}`);
          } else {
            this.engineStatus[engineName].available = false;
            this.logger.warn(`引擎健康检查失败: ${engineName}`, { error: result.error });
          }
        }

      } catch (error) {
        this.engineStatus[engineName].available = false;
        this.logger.warn(`引擎健康检查异常: ${engineName}`, { error: error.message });
      }
    }
  }

  /**
   * 检查引擎配置是否完整
   * @param {string} engineName - 引擎名称
   * @returns {boolean} - 配置是否完整
   */
  checkEngineConfig(engineName) {
    switch (engineName) {
      case 'baidu':
        return !!(process.env.BAIDU_APP_ID && process.env.BAIDU_SECRET_KEY);
      case 'deepseek':
        return !!(process.env.DEEPSEEK_API_KEY);
      default:
        return false;
    }
  }

  /**
   * 计算重试延迟
   * @param {number} attempt - 当前尝试次数
   * @returns {number} - 延迟时间（毫秒）
   */
  calculateRetryDelay(attempt) {
    const baseDelay = RETRY_CONFIG.baseDelay;
    const maxDelay = RETRY_CONFIG.maxDelay;
    const backoffMultiplier = RETRY_CONFIG.backoffMultiplier;

    let delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1);

    // 添加抖动
    if (RETRY_CONFIG.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return Math.min(delay, maxDelay);
  }

  /**
   * 睡眠函数
   * @param {number} ms - 睡眠时间（毫秒）
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 停止翻译系统
   */
  async stopTranslationSystem() {
    try {
      this.logger.error('翻译系统遇到严重错误，正在停止...');

      // 设置系统停止标志
      global.translationSystemStopped = true;
      global.translationSystemStoppedAt = new Date();

      // 通知翻译管理器停止
      if (this.translationManager) {
        await this.translationManager.stop();
      }

      this.logger.error('翻译系统已停止，队列中的车辆将保留等待重启');
    } catch (error) {
      this.logger.error('停止翻译系统时出错:', error);
    }
  }

  /**
   * 获取引擎统计信息
   */
  getStats() {
    return {
      ...this.stats,
      engineStatus: this.engineStatus,
      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([engine, breaker]) => ({
        engine,
        ...breaker
      }))
    };
  }

  /**
   * 获取健康状态
   */
  getHealthStatus() {
    const availableEngines = Object.entries(this.engineStatus)
      .filter(([_, status]) => status.available)
      .map(([engine, _]) => engine);

    return {
      totalEngines: Object.keys(TRANSLATION_ENGINES).length,
      availableEngines: availableEngines.length,
      engines: availableEngines,
      circuitBreakers: this.circuitBreakers.size
    };
  }
}

module.exports = TranslationEngines;
