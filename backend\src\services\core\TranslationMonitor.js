/**
 * 翻译监控系统
 * 统一监控翻译系统的性能和健康状态
 */

const EventEmitter = require('events');
const { MONITORING_CONFIG } = require('../../config/translationConfig');

class TranslationMonitor extends EventEmitter {
  constructor(logger) {
    super();

    this.logger = logger;
    this.config = MONITORING_CONFIG;

    // 性能指标
    this.metrics = {
      translations: {
        total: 0,
        successful: 0,
        failed: 0,
        retried: 0
      },
      performance: {
        averageTranslationTime: 0,
        totalTranslationTime: 0,
        slowestTranslation: 0,
        fastestTranslation: Infinity,
        translationsInLastHour: 0,
        translationsInLastDay: 0
      },
      errors: {
        byType: {},
        byLanguage: {},
        byEngine: {},
        recentErrors: []
      },
      queue: {
        totalQueued: 0,
        currentQueueSize: 0,
        averageWaitTime: 0,
        maxQueueSize: 0
      },
      engines: {
        availability: {},
        responseTime: {},
        errorRate: {}
      },
      vehicles: {
        translatedCount: 0
      }
    };

    // 活跃翻译跟踪
    this.activeTranslations = new Map();

    // 时间窗口统计
    this.hourlyStats = [];
    this.dailyStats = [];

    // 监控系统状态
    this.enabled = false; // 默认禁用，需要手动启用

    // 日志频率控制
    this.lastVehicleCountLogTime = 0;
    this.vehicleCountLogInterval = 5 * 60 * 1000; // 5分钟间隔
  }

  /**
   * 记录翻译开始
   * @param {string} translationId - 翻译ID
   * @param {Object} context - 翻译上下文
   */
  recordTranslationStart(translationId, context) {
    const startTime = Date.now();
    
    this.activeTranslations.set(translationId, {
      ...context,
      startTime,
      status: 'in_progress'
    });
    
    this.metrics.translations.total++;
    
    this.logger.logTranslationStart(translationId, context);
    this.emit('translationStarted', { translationId, context, startTime });
  }

  /**
   * 记录翻译成功
   * @param {string} translationId - 翻译ID
   * @param {Object} result - 翻译结果
   */
  recordTranslationSuccess(translationId, result) {
    const translation = this.activeTranslations.get(translationId);
    if (!translation) return;
    
    const endTime = Date.now();
    const duration = endTime - translation.startTime;
    
    // 更新性能指标
    this.updatePerformanceMetrics(duration);
    this.metrics.translations.successful++;
    
    // 更新翻译记录
    translation.status = 'completed';
    translation.endTime = endTime;
    translation.duration = duration;
    translation.result = result;
    
    // 记录引擎性能
    if (translation.engine) {
      this.recordEnginePerformance(translation.engine, duration, true);
    }
    
    this.logger.logTranslationSuccess(translationId, result);
    this.emit('translationCompleted', { translationId, translation, result });
    
    // 清理活跃翻译记录
    this.activeTranslations.delete(translationId);
  }

  /**
   * 记录翻译失败
   * @param {string} translationId - 翻译ID
   * @param {Error} error - 错误信息
   * @param {Object} context - 上下文
   */
  recordTranslationFailure(translationId, error, context = {}) {
    const translation = this.activeTranslations.get(translationId);
    if (translation) {
      const duration = Date.now() - translation.startTime;
      translation.status = 'failed';
      translation.error = error.message;
      translation.duration = duration;
      
      // 记录引擎性能
      if (translation.engine) {
        this.recordEnginePerformance(translation.engine, duration, false);
      }
    }
    
    this.metrics.translations.failed++;
    
    // 记录错误统计
    this.recordError(error, { ...context, translationId });
    
    this.logger.logTranslationFailure(translationId, error, context);
    this.emit('translationFailed', { translationId, error, context });
    
    // 清理活跃翻译记录
    this.activeTranslations.delete(translationId);
  }

  /**
   * 记录翻译重试
   * @param {string} translationId - 翻译ID
   * @param {number} retryCount - 重试次数
   * @param {string} reason - 重试原因
   */
  recordTranslationRetry(translationId, retryCount, reason) {
    this.metrics.translations.retried++;
    
    this.logger.info(`翻译重试: ${translationId}`, { retryCount, reason });
    this.emit('translationRetried', { translationId, retryCount, reason });
  }

  /**
   * 记录队列指标
   * @param {Object} queueStats - 队列统计
   */
  recordQueueMetrics(queueStats) {
    this.metrics.queue = {
      ...this.metrics.queue,
      ...queueStats,
      maxQueueSize: Math.max(this.metrics.queue.maxQueueSize, queueStats.currentQueueSize || 0)
    };
    
    this.logger.logMetric('queue_size', queueStats.currentQueueSize || 0);
  }

  /**
   * 记录引擎性能
   * @param {string} engine - 引擎名称
   * @param {number} responseTime - 响应时间
   * @param {boolean} success - 是否成功
   */
  recordEnginePerformance(engine, responseTime, success) {
    if (!this.metrics.engines.responseTime[engine]) {
      this.metrics.engines.responseTime[engine] = [];
      this.metrics.engines.errorRate[engine] = { total: 0, errors: 0 };
    }
    
    // 记录响应时间（保留最近100次）
    this.metrics.engines.responseTime[engine].push(responseTime);
    if (this.metrics.engines.responseTime[engine].length > 100) {
      this.metrics.engines.responseTime[engine].shift();
    }
    
    // 记录错误率
    this.metrics.engines.errorRate[engine].total++;
    if (!success) {
      this.metrics.engines.errorRate[engine].errors++;
    }
    
    this.logger.logMetric(`engine_${engine}_response_time`, responseTime);
  }

  /**
   * 记录翻译错误
   * @param {string} errorMessage - 错误消息
   * @param {Object} context - 上下文
   */
  recordTranslationError(errorMessage, context = {}) {
    const error = new Error(errorMessage);
    this.recordError(error, context);
  }

  /**
   * 记录错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 上下文
   */
  recordError(error, context = {}) {
    const errorType = error.name || 'UnknownError';
    const language = context.language || 'unknown';
    const engine = context.engine || 'unknown';

    // 按类型统计
    this.metrics.errors.byType[errorType] = (this.metrics.errors.byType[errorType] || 0) + 1;

    // 按语言统计
    this.metrics.errors.byLanguage[language] = (this.metrics.errors.byLanguage[language] || 0) + 1;

    // 按引擎统计
    this.metrics.errors.byEngine[engine] = (this.metrics.errors.byEngine[engine] || 0) + 1;

    // 记录详细错误信息
    const errorRecord = {
      id: Date.now() + Math.random(), // 唯一ID
      timestamp: new Date(),
      type: errorType,
      error: error.message,
      language,
      engine,
      translationId: context.translationId || 'unknown',
      // 详细上下文信息
      productId: context.productId || 'unknown',
      carModel: context.carModel || context.title || 'unknown',
      carBrand: context.carBrand || context.brand || 'unknown',
      fieldName: context.fieldName || 'unknown',
      originalText: context.originalText ?
        (context.originalText.length > 100 ?
          context.originalText.substring(0, 100) + '...' :
          context.originalText) : 'unknown',
      stack: error.stack,
      context
    };

    this.metrics.errors.recentErrors.push(errorRecord);

    // 保留最近100个错误
    if (this.metrics.errors.recentErrors.length > 100) {
      this.metrics.errors.recentErrors.shift();
    }

    // 记录到日志
    this.logger.error(`翻译错误 [${errorType}] ${language}/${engine}: ${error.message}`, {
      productId: context.productId,
      carModel: context.carModel || context.title,
      fieldName: context.fieldName
    });
  }

  /**
   * 记录车辆翻译完成
   * @param {string} productId - 产品ID
   * @param {Object} context - 上下文信息
   */
  recordVehicleTranslationComplete(productId, context = {}) {
    this.metrics.vehicles.translatedCount++;
    console.log(`[翻译监控] 车辆翻译完成: ${productId}, 当前已翻译车辆数: ${this.metrics.vehicles.translatedCount}`);
    this.logger.info(`车辆翻译完成: ${productId}, 当前已翻译车辆数: ${this.metrics.vehicles.translatedCount}`);
  }

  /**
   * 更新性能指标
   * @param {number} duration - 翻译耗时
   */
  updatePerformanceMetrics(duration) {
    const perf = this.metrics.performance;
    
    perf.totalTranslationTime += duration;
    perf.averageTranslationTime = perf.totalTranslationTime / this.metrics.translations.successful;
    perf.slowestTranslation = Math.max(perf.slowestTranslation, duration);
    perf.fastestTranslation = Math.min(perf.fastestTranslation, duration);
    
    // 记录慢翻译
    if (duration > this.config.performance.slowTranslationThreshold) {
      this.logger.warn(`检测到慢翻译: ${duration}ms`);
      this.emit('slowTranslation', { duration });
    }
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport() {
    const now = new Date();
    const metrics = this.metrics;

    // 计算成功率
    const successRate = metrics.translations.total > 0
      ? (metrics.translations.successful / metrics.translations.total * 100).toFixed(2)
      : 0;

    // 计算引擎可用性
    const engineAvailability = {};
    Object.keys(metrics.engines.errorRate).forEach(engine => {
      const stats = metrics.engines.errorRate[engine];
      engineAvailability[engine] = stats.total > 0
        ? ((stats.total - stats.errors) / stats.total * 100).toFixed(2)
        : 100;
    });

    // 使用内存中的车辆翻译统计（添加安全检查）
    const translatedVehicleCount = this.metrics.vehicles ? this.metrics.vehicles.translatedCount : 0;

    // 频率控制：只在间隔时间后或数量变化时输出日志
    const nowTimestamp = Date.now();
    const shouldLog = (nowTimestamp - this.lastVehicleCountLogTime) > this.vehicleCountLogInterval ||
                      translatedVehicleCount !== this.lastLoggedVehicleCount;

    if (shouldLog) {
      console.log(`[翻译监控] 当前翻译车辆数量: ${translatedVehicleCount}`);
      this.lastVehicleCountLogTime = nowTimestamp;
      this.lastLoggedVehicleCount = translatedVehicleCount;
    }

    const report = {
      timestamp: now,
      summary: {
        totalTranslations: metrics.translations.total,
        successfulTranslations: metrics.translations.successful,
        failedTranslations: metrics.translations.failed,
        successRate: `${successRate}%`,
        averageResponseTime: `${metrics.performance.averageTranslationTime.toFixed(0)}ms`,
        translatedVehicleCount: translatedVehicleCount
      },
      queue: {
        currentSize: metrics.queue.currentQueueSize,
        maxSize: metrics.queue.maxQueueSize,
        totalQueued: metrics.queue.totalQueued
      },
      engines: {
        availability: engineAvailability,
        activeTranslations: this.activeTranslations.size
      },
      errors: {
        totalErrors: metrics.translations.failed,
        recentErrorTypes: Object.keys(metrics.errors.byType).slice(0, 5),
        topErrorLanguages: Object.keys(metrics.errors.byLanguage).slice(0, 3),
        errorsByEngine: metrics.errors.byEngine,
        recentErrors: metrics.errors.recentErrors.slice(-20).map(err => ({
          id: err.id,
          timestamp: err.timestamp,
          type: err.type,
          error: err.error,
          language: err.language,
          engine: err.engine,
          productId: err.productId,
          carModel: err.carModel,
          carBrand: err.carBrand,
          fieldName: err.fieldName,
          originalText: err.originalText
        })) // 最近20个错误的详细信息
      }
    };

    return report;
  }

  /**
   * 获取健康状态
   */
  getHealthStatus() {
    const metrics = this.metrics;
    const activeTranslations = this.activeTranslations.size;
    
    // 计算健康分数
    let healthScore = 100;
    
    // 成功率影响
    const successRate = metrics.translations.total > 0 
      ? metrics.translations.successful / metrics.translations.total
      : 1;
    healthScore -= (1 - successRate) * 50;
    
    // 队列大小影响
    if (metrics.queue.currentQueueSize > 100) {
      healthScore -= 20;
    }
    
    // 活跃翻译数量影响
    if (activeTranslations > 10) {
      healthScore -= 10;
    }
    
    // 确定健康状态
    let status = 'healthy';
    if (healthScore < 50) {
      status = 'unhealthy';
    } else if (healthScore < 80) {
      status = 'degraded';
    }
    
    return {
      status,
      healthScore: Math.max(0, Math.round(healthScore)),
      activeTranslations,
      queueSize: metrics.queue.currentQueueSize,
      successRate: `${(successRate * 100).toFixed(2)}%`,
      uptime: process.uptime()
    };
  }



  /**
   * 清理过期数据
   */
  cleanupExpiredData() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
    
    // 清理过期的活跃翻译
    for (const [translationId, translation] of this.activeTranslations.entries()) {
      if (translation.startTime < cutoffTime) {
        this.activeTranslations.delete(translationId);
        this.logger.warn(`清理过期的活跃翻译: ${translationId}`);
      }
    }
    
    // 清理过期的错误记录
    this.metrics.errors.recentErrors = this.metrics.errors.recentErrors.filter(
      error => error.timestamp.getTime() > cutoffTime
    );
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    console.log('[翻译监控] 重置监控指标...');

    // 重置内存指标
    this.metrics = {
      translations: { total: 0, successful: 0, failed: 0, retried: 0 },
      performance: { averageTranslationTime: 0, totalTranslationTime: 0, slowestTranslation: 0, fastestTranslation: Infinity },
      errors: { byType: {}, byLanguage: {}, byEngine: {}, recentErrors: [] },
      queue: { totalQueued: 0, currentQueueSize: 0, averageWaitTime: 0, maxQueueSize: 0 },
      engines: { availability: {}, responseTime: {}, errorRate: {} },
      vehicles: { translatedCount: 0 } // 重置车辆统计
    };

    this.activeTranslations.clear();

    const message = '翻译监控指标已重置（包含翻译车辆数量）';
    console.log(`[翻译监控] ${message}`);
    this.logger.info(message);
  }

  /**
   * 获取详细指标
   */
  getDetailedMetrics() {
    return {
      ...this.metrics,
      activeTranslations: Array.from(this.activeTranslations.entries()),
      healthStatus: this.getHealthStatus()
    };
  }
}

module.exports = TranslationMonitor;
