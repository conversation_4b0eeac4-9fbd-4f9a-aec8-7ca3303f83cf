/**
 * 商品控制器
 */
const { Product, User, Dealer, Brand, Series, ConfigCar, ModelSpec, sequelize } = require('../../../models');
const { validateInput, isValidStatus } = require('../../../utils/validators');
const { getPagination, getPagingData } = require('../../../utils/pagination');
const { TranslationService } = require('../../../services/translationService');
const exchangeRateService = require('../../../services/exchangeRateService'); // 添加汇率服务
const { Op, Sequelize } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const autoTranslationService = require('../../../services/autoTranslationService'); // 引入自动翻译服务
const { handleProductOnShelf } = require('../../../services/autoTranslationService');
const translationManager = require('../../../services/core/TranslationManager'); // 新翻译系统
const { createProductOptimized, createProductWithUrls } = require('../../../services/productCreationService');
const { getOptimizedVehicleList, getCursorBasedVehicleList } = require('../../../services/optimizedVehicleService');
const db = require('../../../models'); // <--- 添加这一行来导入整个 db 对象
const multiDealerConcurrencyService = require('../../../services/multiDealerConcurrencyService');



/**
 * 检查车商发布限制 (实时版本 - 直接从数据库读取最新数据)
 */
const checkPublishLimit = async (dealerId) => {
  try {
    // 直接从数据库获取车商信息和最新的发布限制
    const dealer = await db.Dealer.findByPk(dealerId);
    if (!dealer) {
      return {
        allowed: false,
        error: '车商不存在',
        currentCount: 0,
        limit: 0,
        remaining: 0
      };
    }

    const publishLimit = dealer.publishLimit || 50; // 默认50辆

    // 实时查询当前在售车辆数量
    const currentCount = await db.Product.count({
      where: {
        dealerId: dealerId,
        status: 'active'
      }
    });

    console.log(`[发布限制] 车商 ${dealerId} 实时检查: ${currentCount}/${publishLimit} (${dealer.companyName})`);

    const allowed = currentCount < publishLimit;
    const remaining = Math.max(0, publishLimit - currentCount);

    if (!allowed) {
      return {
        allowed: false,
        error: `已达到发布限制 (${currentCount}/${publishLimit})，请联系管理员提升限制或下架部分车辆`,
        currentCount,
        limit: publishLimit,
        remaining: 0
      };
    }

    return {
      allowed: true,
      currentCount,
      limit: publishLimit,
      remaining
    };

  } catch (error) {
    console.error('检查发布限制失败:', error);
    return {
      allowed: false,
      error: '检查发布限制失败: ' + error.message,
      currentCount: 0,
      limit: 0,
      remaining: 0
    };
  }
};

/**
 * 并发安全的发布限制检查 (带事务锁)
 * 用于多车商同时上架的场景
 */
const checkPublishLimitSafe = async (dealerId) => {
  const transaction = await db.sequelize.transaction();
  try {
    // 获取车商信息
    const dealer = await db.Dealer.findByPk(dealerId, { transaction });
    if (!dealer) {
      await transaction.rollback();
      return {
        allowed: false,
        error: '车商不存在',
        currentCount: 0,
        limit: 0
      };
    }

    const publishLimit = dealer.publishLimit || 50;

    // 使用 FOR UPDATE 锁定查询，确保并发安全
    const currentCount = await db.Product.count({
      where: {
        dealerId: dealerId,
        status: 'active'
      },
      transaction,
      lock: true  // 添加行锁
    });

    console.log(`[发布限制] 车商 ${dealerId} 并发安全检查: ${currentCount}/${publishLimit} (${dealer.companyName})`);

    await transaction.commit();

    if (currentCount >= publishLimit) {
      return {
        allowed: false,
        error: `已达到发布限制 (${currentCount}/${publishLimit})，请联系管理员提升限制或下架部分车辆`,
        currentCount,
        limit: publishLimit,
        remaining: 0
      };
    }

    return {
      allowed: true,
      currentCount,
      limit: publishLimit,
      remaining: publishLimit - currentCount
    };

  } catch (error) {
    await transaction.rollback();
    console.error('并发安全发布限制检查失败:', error);
    return {
      allowed: false,
      error: '检查发布限制失败: ' + error.message,
      currentCount: 0,
      limit: 0
    };
  }
};

/**
 * 获取车商的商品列表（新版本）
 */
const getDealerProducts = async (req, res) => {
  try {
    console.log('getDealerProducts 函数被调用', req.user);

    const dealerId = req.user?.id;
    if (!dealerId) {
      return res.status(401).json({
        success: false,
        message: '无法获取有效的车商ID'
      });
    }
    console.log('获取车商商品列表，使用 Dealer ID:', dealerId);

    const {
      page = 1,
      limit = 10,
      status = 'all', // 默认查询所有状态，与后台管理系统保持一致
      brand, // 品牌筛选
      minPrice, // 最低价格
      maxPrice, // 最高价格
      search // 搜索关键词
    } = req.query;

    console.log('getDealerProducts 接收到的查询参数:', {
      page, limit, status, brand, minPrice, maxPrice, search,
      dealerId: dealerId
    });

    const { offset, limit: limitValue } = getPagination(page, limit);

    const whereClause = { dealerId: dealerId };

    // 确保只查询明确的状态
    if (status && ['active', 'inactive', 'sold', 'pending'].includes(status)) {
      whereClause.status = status;
    }

    // 添加品牌筛选
    if (brand && brand.trim()) {
      whereClause.brand = brand.trim();
    }

    // 添加价格筛选
    if (minPrice || maxPrice) {
      whereClause.price = {};
      if (minPrice) {
        whereClause.price[Op.gte] = parseFloat(minPrice);
      }
      if (maxPrice) {
        whereClause.price[Op.lte] = parseFloat(maxPrice);
      }
    }

    // 添加搜索功能
    if (search && search.trim()) {
      const searchTerm = search.trim();
      console.log('应用搜索条件，搜索词:', searchTerm);
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${searchTerm}%` } },
        { brand: { [Op.like]: `%${searchTerm}%` } },
        { condition: { [Op.like]: `%${searchTerm}%` } } // 使用condition字段替代modelName
      ];
      console.log('搜索条件已添加到 whereClause');
    } else {
      console.log('没有搜索条件');
    }
    // 如果 status 为 'all' 或无效，则不添加状态过滤条件，查询所有状态

    console.log('最终的查询条件 whereClause:', JSON.stringify(whereClause, null, 2));

    // 查询产品列表
    const products = await Product.findAndCountAll({
      where: whereClause,
      attributes: [
        'productId', 'modelId', 'brand', 'title', 'price', 
        'mainImage', 'status', 'mileage', 'firstRegistration',
        'createdAt', 'updatedAt', 'manufactureDate', 'color'
      ],
      offset: offset,
      limit: limitValue,
      order: [['createdAt', 'DESC']]
    });

    // 计算下一页参数
    const currentPage = parseInt(page, 10);
    const totalPages = Math.ceil(products.count / limitValue);
    const next = currentPage < totalPages ? currentPage + 1 : null;

    return res.status(200).json({
      success: true,
      totalItems: products.count,
      totalPages: totalPages,
      currentPage: parseInt(page, 10),
      limit: limitValue,
      next: next,
      data: products.rows
    });
  } catch (error) {
    console.error('获取车商商品列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取车商商品列表失败: ' + error.message
    });
  }
};

/**
 * 设置缺省值
 */
const setDefaultValues = (product) => {
  // 设置默认的活动状态
  if (!product.status) {
    product.status = 'active';
  }

  // 如果有出版日期，进行转换（但不自动设置新的发布时间）
  if (product.publishedAt && typeof product.publishedAt === 'string') {
    product.publishedAt = new Date(product.publishedAt);
  }
  // 注意：不再自动设置 publishedAt，避免在修改时被误判为字段变更

  // 如果没有上传日期，设置为当前时间
  if (!product.createdAt) {
    product.createdAt = new Date();
  }

  // 注意：updatedAt 由 Sequelize 自动管理，不需要手动设置

  // 设置最低价格为0
  if (product.price < 0) {
    product.price = 0;
  }

  // 确保里程数为正数
  if (product.mileage < 0) {
    product.mileage = 0;
  }

  // 确保VIN码为大写
  if (product.vin) {
    product.vin = product.vin.toUpperCase();
  }

  return product;
};

/**
 * 搜索商品列表 - 优化版本
 */
const searchProductList = async (req, res) => {
  try {
    // 1. 提取查询参数
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'DESC',
      sortBy, // 新增：支持前端的 sortBy 参数
      sortDirection, // 新增：支持前端的 sortDirection 参数
      keyword,
      brand, // 注意：这里的 brand, series 可能是ID，需要确认它们在 Product_xx 表中是否存在或如何处理
      series,
      minPrice,
      maxPrice,
      minPriceUsd,
      maxPriceUsd,
      status = 'active', // Product_xx 表可能没有 status 字段，需要确认
      dealerId,
      locale, // <--- 新增：获取 locale 参数
      // 新增：车辆规格筛选参数
      energyType,
      bodyStructure,
      gearboxType,
      driveType,
      // 新增：性能优化参数
      useCache = true,
      useCursor = false, // 是否使用游标分页
      cursor // 游标值
    } = req.query;

    // 2. 选择查询模型
    let TargetModel = db.Product; // 默认使用中文products表（微信小程序兼容性）

    // 根据locale选择对应的产品表
    if (locale === 'en') {
      TargetModel = db.ProductEn; // 英文使用ProductEn表
      console.log(`Using ProductEn model for English locale: ${locale}`);
    } else if (locale === 'ru') {
      TargetModel = db.ProductRu; // 俄文使用ProductRu表
      console.log(`Using ProductRu model for Russian locale: ${locale}`);
    } else if (locale === 'es') {
      TargetModel = db.ProductEs; // 西班牙文使用ProductEs表
      console.log(`Using ProductEs model for Spanish locale: ${locale}`);
    } else if (locale === 'fr') {
      TargetModel = db.ProductFr; // 法文使用ProductFr表
      console.log(`Using ProductFr model for French locale: ${locale}`);
    } else if (!locale) {
      // 如果没有传递locale参数，默认使用中文products表（微信小程序使用）
      TargetModel = db.Product;
      console.log(`No locale specified, defaulting to Product (Chinese) for mini-program compatibility`);
    } else {
      console.warn(`Unsupported locale '${locale}', defaulting to Chinese Product table.`);
      TargetModel = db.Product; // 默认使用中文products表
    }

    // 3. 计算分页参数
    const { offset, limit: limitValue } = getPagination(page, limit);

    // 4. 构建查询条件
    const whereClause = {};
    const sortOrders = [];

    // 4.1 处理状态过滤
    if (TargetModel.rawAttributes.status) {
      if (status && isValidStatus(status)) {
        whereClause.status = status;
      } else if (status === 'all') {
        // 不添加状态过滤
      } else {
        // 默认只显示活跃商品
        whereClause.status = 'active';
      }
    } else if (status && status !== 'all') {
      console.warn(`Model ${TargetModel.name} does not have a status field, but a status filter ('${status}') was requested. This filter will be ignored for this model.`);
    }

    // 4.2 处理价格范围
    if (minPrice && !isNaN(minPrice)) {
      whereClause.price = { ...whereClause.price, [Op.gte]: minPrice };
    }

    if (maxPrice && !isNaN(maxPrice)) {
      whereClause.price = { ...whereClause.price, [Op.lte]: maxPrice };
    }

    // 4.2.1 处理美元价格范围
    if (minPriceUsd && !isNaN(minPriceUsd)) {
      if (TargetModel.rawAttributes.priceUsd) {
        whereClause.priceUsd = { ...whereClause.priceUsd, [Op.gte]: minPriceUsd };
      }
    }

    if (maxPriceUsd && !isNaN(maxPriceUsd)) {
      if (TargetModel.rawAttributes.priceUsd) {
        whereClause.priceUsd = { ...whereClause.priceUsd, [Op.lte]: maxPriceUsd };
      }
    }

    // 4.3 处理关键词搜索
    if (keyword) {
      const keywordConditions = [];
      if (TargetModel.rawAttributes.title) keywordConditions.push({ title: { [Op.like]: `%${keyword}%` } });
      if (TargetModel.rawAttributes.description) keywordConditions.push({ description: { [Op.like]: `%${keyword}%` } });
      if (TargetModel.rawAttributes.vin) keywordConditions.push({ vin: { [Op.like]: `%${keyword}%` } });

      if (keywordConditions.length > 0) {
        whereClause[Op.or] = keywordConditions;
      }
    }

    // 4.4 处理品牌过滤
    if (brand) {
      if (TargetModel.rawAttributes.brand) {
        whereClause.brand = brand;
      }
    }

    // 4.5 处理车商过滤
    if (dealerId && TargetModel.rawAttributes.dealerId) {
      whereClause.dealerId = dealerId;
    }

    // 4.6 处理车辆规格筛选
    if (energyType && TargetModel.rawAttributes.energyType) {
      whereClause.energyType = energyType;
    }

    if (bodyStructure && TargetModel.rawAttributes.bodyStructure) {
      whereClause.bodyStructure = bodyStructure;
    }

    if (gearboxType && TargetModel.rawAttributes.gearboxType) {
      whereClause.gearboxType = gearboxType;
    }

    if (driveType && TargetModel.rawAttributes.driveType) {
      whereClause.driveType = driveType;
    }

    // 4.7 处理排序
    const finalSortBy = sortBy || sort || 'createdAt';
    const finalSortDirection = sortDirection || order || 'DESC';

    if (finalSortBy) {
      const direction = finalSortDirection && (finalSortDirection.toUpperCase() === 'ASC' || finalSortDirection.toUpperCase() === 'DESC')
        ? finalSortDirection.toUpperCase()
        : 'DESC';

      // 字段映射：将前端字段名映射到模型字段名
      const fieldMapping = {
        'price': 'priceUsd', // 前端的 price 对应模型的 priceUsd 字段
        'year': 'manufactureDate',
        'mileage': 'mileage',
        'createdAt': 'createdAt',
        'random': 'random'
      };

      const dbField = fieldMapping[finalSortBy] || finalSortBy;

      // 特殊处理随机排序
      if (finalSortBy === 'random') {
        const { Sequelize } = require('sequelize');
        sortOrders.push([Sequelize.fn('RAND')]);
      } else if (TargetModel.rawAttributes[dbField]) {
        sortOrders.push([dbField, direction]);
      } else {
        console.warn(`Sort key '${dbField}' not found in model ${TargetModel.name}. Using default sort.`);
        if (TargetModel.rawAttributes.createdAt) {
          sortOrders.push(['createdAt', 'DESC']);
        } else if (TargetModel.rawAttributes.id) {
          sortOrders.push(['id', 'DESC']);
        }
      }
    } else {
      // 默认排序：创建时间降序
      if (TargetModel.rawAttributes.createdAt) {
        sortOrders.push(['createdAt', 'DESC']);
      } else if (TargetModel.rawAttributes.id) {
        sortOrders.push(['id', 'DESC']);
      }
    }

    // 如果排序数组为空，添加一个默认的主键排序
    if (sortOrders.length === 0 && TargetModel.primaryKeyAttribute && TargetModel.rawAttributes[TargetModel.primaryKeyAttribute]) {
      sortOrders.push([TargetModel.primaryKeyAttribute, 'DESC']);
    }

    // 5. 执行查询 - 使用优化服务
    try {
      // 构建优化服务参数
      const optimizedParams = {
        page: parseInt(page),
        limit: parseInt(limitValue),
        sortBy: sortBy || sort || 'createdAt',
        sortDirection: sortDirection || order || 'DESC',
        locale: locale || 'zh',
        keyword,
        brand,
        minPriceUsd: minPriceUsd ? parseInt(minPriceUsd) : undefined,
        maxPriceUsd: maxPriceUsd ? parseInt(maxPriceUsd) : undefined,
        energyType,
        bodyStructure,
        gearboxType,
        driveType,
        useCache: useCache !== 'false'
      };

      console.log('使用优化服务查询参数:', optimizedParams);

      const result = await getOptimizedVehicleList(TargetModel, optimizedParams);

      // 6. 返回搜索结果
      return res.status(200).json({
        success: true,
        message: '获取商品列表成功',
        vehicles: result.vehicles,
        totalItems: result.totalItems,
        totalPages: result.totalPages,
        currentPage: result.currentPage,
        itemsPerPage: result.itemsPerPage,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage
      });

    } catch (optimizedError) {
      console.error('优化服务查询失败，回退到传统查询:', optimizedError.message);
      console.error('优化服务错误详情:', optimizedError);

      // 回退到传统查询
      const products = await TargetModel.findAndCountAll({
        where: whereClause,
        limit: limitValue,
        offset: offset,
        order: sortOrders,
      });

      // 格式化响应数据
      const response = getPagingData(products, page, limitValue);

      // 返回搜索结果
      return res.status(200).json({
        success: true,
        message: '获取商品列表成功',
        ...response
      });
    }

  } catch (error) {
    console.error('搜索商品列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};





/**
 * 获取商品详情
 */
const getProductDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const locale = req.query.locale; // 不设置默认值

    console.log(`获取车辆详情: ID=${id}, 语言=${locale || '中文(products表)'}`);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '车辆ID不能为空'
      });
    }

    const db = require('../../../models');
    let ProductModel = db.Product; // 默认使用中文products表
    let ConfigCarModel = db.ConfigCar; // 默认使用中文配置表

    // 只有明确指定locale时才使用语言表，微信小程序使用中文表
    if (locale === 'en') {
      ProductModel = db.ProductEn;
      ConfigCarModel = db.ConfigCarEn;
    } else if (locale === 'ru') {
      ProductModel = db.ProductRu;
      ConfigCarModel = db.ConfigCarRu;
    } else if (locale === 'es') {
      ProductModel = db.ProductEs;
      ConfigCarModel = db.ConfigCarEs;
    } else if (locale === 'fr') {
      ProductModel = db.ProductFr;
      ConfigCarModel = db.ConfigCarFr;
    }
    // 如果没有locale参数或不支持的语言，使用中文products表
    
    console.log(`使用模型: ${ProductModel.name}, ${ConfigCarModel ? ConfigCarModel.name : '无配置模型'}`);
    
    const productInstance = await ProductModel.findOne({
      where: { productId: id }
    });

    if (!productInstance) {
      return res.status(404).json({
        success: false, 
        message: '未找到该车辆'
      });
    }

    // 过滤 productInstance 的字段
    const filteredProductData = {};
    const productDataRaw = productInstance.toJSON();
    for (const key in productDataRaw) {
      if (productDataRaw[key] !== null && productDataRaw[key] !== undefined && productDataRaw[key] !== '') {
        filteredProductData[key] = productDataRaw[key];
      }
    }
    
    const modelId = filteredProductData.modelId; // 使用过滤后的数据获取modelId
    console.log(`车辆 ${id} 的modelId: ${modelId}`);
    
    let filteredConfigCarData = null;
    if (modelId && ConfigCarModel) {
      try {
        const configCarInstance = await ConfigCarModel.findOne({
          where: { '车型_id': modelId.toString() },
          attributes: {
            exclude: ['minGuidePrice(Wan)', 'maxGuidePrice(Wan)', 'msrp(Yuan)', 'warranty']
          }
        });
        if (configCarInstance) {
            filteredConfigCarData = {};
            const configCarDataRaw = configCarInstance.get();
            const modelAttributes = ConfigCarModel.rawAttributes;
            for (const key in configCarDataRaw) {
                if (configCarDataRaw[key] !== null && configCarDataRaw[key] !== undefined && configCarDataRaw[key] !== '') {
                    // 使用 model 'field' 属性作为键名 (如果存在)，否则使用原始键名
                    const fieldName = modelAttributes[key] && modelAttributes[key].field ? modelAttributes[key].field : key;
                    filteredConfigCarData[fieldName] = configCarDataRaw[key];
                }
            }
        }
        console.log(`找到车型配置: ${filteredConfigCarData ? 'yes' : 'no'}`);
      } catch (configError) {
        console.error('获取车型配置信息失败:', configError);
      }
    }
    
    const vehicleData = { ...filteredProductData }; // 使用过滤后的产品数据作为基础
    if (filteredConfigCarData) {
      vehicleData.configCar = filteredConfigCarData;
    }

    return res.status(200).json({
      success: true,
      message: '获取车辆详情成功',
      data: vehicleData
    });
  } catch (error) {
    console.error('获取车辆详情出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取车辆详情失败: ' + error.message
    });
  }
};

/**
 * 创建商品 - 优化版本
 */
const createProduct = async (req, res) => {
  const startTime = Date.now();

  try {
    console.log('开始优化的商品创建流程...');
    console.log('请求体数据:', JSON.stringify(req.body).substring(0, 500) + '...');

    // 1. 身份验证
    const { user } = req;
    if (!user || !user.id) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        code: 'UNAUTHORIZED'
      });
    }

    // 1.5. 🚫 检查发布限制（仅对车商）
    if (user.type === 'dealer') {
      const limitCheck = await checkPublishLimit(user.id);
      if (!limitCheck.allowed) {
        console.log('发布限制检查失败:', limitCheck.error);
        return res.status(403).json({
          success: false,
          message: limitCheck.error,
          code: 'PUBLISH_LIMIT_EXCEEDED',
          data: {
            currentCount: limitCheck.currentCount,
            limit: limitCheck.limit
          }
        });
      }
      console.log(`✅ 发布限制检查通过: ${limitCheck.currentCount}/${limitCheck.limit}, 剩余${limitCheck.remaining}个名额`);
    }

    // 2. 检查是否为小程序模式（已有图片URL）
    const hasImageUrls = req.body.mainImage || req.body.images;
    const files = req.files || [];

    console.log('创建模式检测:', {
      hasImageUrls: !!hasImageUrls,
      fileCount: files.length,
      mainImage: req.body.mainImage,
      images: req.body.images
    });

    let result;
    if (hasImageUrls && files.length === 0) {
      // 小程序模式：使用已上传的图片URL
      console.log('使用小程序模式创建产品（图片URL已存在）');
      result = await createProductWithUrls(user, req.body);
    } else {
      // 文件上传模式：处理上传的文件
      console.log(`使用文件上传模式创建产品（${files.length} 个文件）`);
      result = await createProductOptimized(user, req.body, files);
    }

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error,
        code: 'CREATION_FAILED',
        creationLog: result.creationLog
      });
    }

    // 4. 记录性能指标
    const totalTime = Date.now() - startTime;
    console.log(`商品创建完成，总耗时: ${totalTime}ms`);

    // 5. 返回成功响应
    return res.status(201).json({
      success: true,
      message: '商品创建成功',
      data: {
        productId: result.product.productId,
        title: result.product.title,
        price: result.product.price,
        status: result.product.status,
        imageCount: result.imageCount,
        createdAt: result.product.createdAt
      },
      performance: {
        totalTime: `${totalTime}ms`,
        steps: result.creationLog.steps.length,
        imageProcessed: result.imageCount
      }
    });

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('创建商品出错:', error);

    // 处理不同类型的错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '数据库验证失败',
        code: 'DATABASE_VALIDATION_ERROR',
        errors: error.errors.map(e => ({ field: e.path, message: e.message })),
        performance: { totalTime: `${totalTime}ms` }
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        success: false,
        message: '商品已存在',
        code: 'DUPLICATE_PRODUCT',
        performance: { totalTime: `${totalTime}ms` }
      });
    }

    return res.status(500).json({
      success: false,
      message: '创建商品失败: ' + error.message,
      code: 'INTERNAL_ERROR',
      performance: { totalTime: `${totalTime}ms` }
    });
  }
};

/**
 * 更新商品信息
 */
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    // 获取认证用户信息
    const user = req.user;
    console.log('更新商品的用户信息:', JSON.stringify(user));
    
    // 检查用户类型
    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确:', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法更新商品'
      });
    }
    
    // 直接使用JWT中的ID作为车商ID
    const dealerId = user.id;
    console.log('使用dealerId:', dealerId);
    
    const updateData = req.body;
    console.log('更新商品数据:', JSON.stringify(updateData).substring(0, 200) + '...');

    // 查找要更新的商品
    const product = await Product.findOne({
      where: {
        productId: id,  // 修复：使用正确的字段名 productId
        dealerId: dealerId
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在或您无权访问此商品'
      });
    }
    
    // 设置默认值
    const dataToUpdate = setDefaultValues({
      ...product.toJSON(),
      ...updateData
    });
    
    // --- 防御性代码：清理 condition 字段 --- 
    if (typeof dataToUpdate.condition === 'string' && dataToUpdate.condition.startsWith('{"description":')) {
      console.warn(`[UPDATE] 检测到 condition 字段包含默认 JSON 字符串，将其设为 null。ID: ${id}, 原始值: ${dataToUpdate.condition}`);
      dataToUpdate.condition = null;
    } else if (dataToUpdate.condition === '') { // 如果是空字符串也设为 null
      dataToUpdate.condition = null;
    }
    // --------------------------------------
    
    // 计算美元价格（如果价格有变动）
    if (dataToUpdate.price && (dataToUpdate.price !== product.price)) {
      try {
        dataToUpdate.priceUsd = await exchangeRateService.calculateUSDPrice(dataToUpdate.price);
        console.log(`更新商品美元价格: CNY ${dataToUpdate.price} => USD ${dataToUpdate.priceUsd}`);
      } catch (error) {
        console.error('计算美元价格失败:', error);
        // 使用默认汇率计算
        dataToUpdate.priceUsd = parseFloat(((dataToUpdate.price * 1.2) / 7.2).toFixed(2));
      }
    }
    
    console.log('准备更新商品:', {
      id: product.id,
      title: dataToUpdate.title,
      dealerId: dataToUpdate.dealerId,
      price: dataToUpdate.price,
      priceUsd: dataToUpdate.priceUsd
    });

    // 获取原始产品数据用于比较
    const originalProduct = product.toJSON();

    // 更新商品
    await product.update(dataToUpdate);
    console.log('商品更新成功');

    // 🌍 检测字段变更并触发智能翻译
    console.log(`🌍 产品 ${product.productId} 更新完成，检测字段变更并触发智能翻译`);

    // 导入handleProductUpdate函数和需要翻译的字段列表
    const { handleProductUpdate } = require('../../../services/autoTranslationService');
    const { getProductTranslatableFields } = require('../../../services/translationService');

    // 标准化字段值比较函数
    function normalizeValue(value, fieldName) {
      if (value === null || value === undefined) return null;

      // 特殊处理图片字段
      if (fieldName === 'images') {
        let imageArray = [];
        if (typeof value === 'string') {
          try {
            // 尝试解析JSON格式
            imageArray = JSON.parse(value);
          } catch (e) {
            // 如果不是JSON，可能是逗号分隔的字符串
            if (value.includes(',')) {
              imageArray = value.split(',').map(img => img.trim());
            } else {
              imageArray = value ? [value] : [];
            }
          }
        } else if (Array.isArray(value)) {
          imageArray = value;
        }
        // 返回排序后的JSON字符串用于比较
        return JSON.stringify(imageArray.sort());
      }

      if (typeof value === 'number') return value.toString();
      if (typeof value === 'string') return value.trim();
      return value;
    }

    // 检测用户实际修改的字段（使用标准化比较）
    const changedFields = {};

    // 只检查用户实际提交的字段，使用标准化比较
    for (const field in updateData) {
      if (updateData.hasOwnProperty(field)) {
        const originalValue = normalizeValue(originalProduct[field], field);
        const newValue = normalizeValue(updateData[field], field);

        if (originalValue !== newValue) {
          changedFields[field] = updateData[field];
          console.log(`🔍 检测到用户修改的字段: ${field} (${originalValue} → ${newValue})`);
        } else {
          console.log(`✓ 字段无变更: ${field} (标准化后相等)`);
        }
      }
    }

    // 检查handleProductUpdate函数是否存在
    if (typeof handleProductUpdate === 'function') {
      if (Object.keys(changedFields).length > 0) {
        console.log(`✅ 发现 ${Object.keys(changedFields).length} 个字段变更，开始调用智能翻译处理...`);
        // 异步触发智能翻译，传递所有变更的字段
        handleProductUpdate(product.productId, changedFields, {
          useQueue: true,
          priority: 'normal'
        }).catch(error => {
          console.error(`❌ 智能翻译更新产品 ${product.productId} 失败:`, error);
        });
      } else {
        console.log(`✅ 没有字段变更，跳过处理`);
      }
    } else {
      console.error('❌ handleProductUpdate函数不存在！');
    }

    // 返回更新后的商品
    return res.status(200).json({
      success: true,
      message: '商品更新成功',
      data: product
    });
  } catch (error) {
    console.error('更新商品出错:', error);
    
    // 处理验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '商品数据验证失败',
        errors: error.errors.map(e => ({ field: e.path, message: e.message }))
      });
    }
    
    return res.status(500).json({
      success: false,
      message: '更新商品失败: ' + error.message
    });
  }
};

/**
 * 删除所有语言版本的商品
 */
const deleteProductTranslations = async (productId) => {
  try {
    const languages = ['en', 'ru', 'fr', 'es'];
    
    for (const lang of languages) {
      const tableName = `products_${lang}`;
      // 删除语言版本的商品记录
      await sequelize.query(
        `DELETE FROM ${tableName} WHERE productId = ?`,
        {
          replacements: [productId],
          type: sequelize.QueryTypes.DELETE
        }
      );
      console.log(`已删除 ${tableName} 表中的商品记录：${productId}`);
    }
  } catch (error) {
    console.error(`删除商品翻译版本失败 (${productId}):`, error);
    // 不抛出错误，让主流程继续执行
  }
};

/**
 * 使用软删除方法移除商品（标记为删除）
 */
// 注意：这个函数似乎与 deleteProduct 功能重叠，且被意外调用。
// 我们将修复它以使用正确的 productId，但理想情况下应该整合或删除其中一个。
const removeProduct = async (req, res) => {
  try {
    // 在函数开头打印完整的 req.params 对象
    console.log('[CRITICAL_DEBUG] Inside removeProduct - req.params:', JSON.stringify(req.params)); 

    // *** 修改这里：直接使用 req.params.id 获取值 ***
    const routeProductId = req.params.id; 
    console.log('[DEBUG] removeProduct called with routeProductId (from req.params.id):', routeProductId);

    if (!routeProductId) {
      console.log('[WARN] routeProductId (from req.params.id) is missing or undefined!'); // 添加更明确的日志
      return res.status(400).json({
        success: false,
        message: '缺少商品ID (来自路径参数id)' // 区分错误来源
      });
    }

    // 获取认证用户信息
    const user = req.user;
    console.log('删除商品的用户信息 (removeProduct):', JSON.stringify(user));

    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确 (removeProduct):', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法删除商品'
      });
    }

    const dealerId = user.id; // 假设JWT中的id是dealerId
    console.log('使用dealerId (removeProduct):', dealerId);

    // 构建查询条件 - 使用正确的 productId
    const whereCondition = {
        productId: routeProductId, // *** 查询数据库时仍然使用正确的 productId 字段名 ***
        dealerId: dealerId
    };
    console.log('[DEBUG] removeProduct - Product.findOne where condition:', JSON.stringify(whereCondition));

    // 查找要删除的商品
    const product = await Product.findOne({
      where: whereCondition
    });

    if (!product) {
       console.log(`[WARN] Product ${routeProductId} not found or does not belong to dealer ${dealerId} (removeProduct)`);
      return res.status(404).json({
        success: false,
        message: '商品不存在或您无权访问此商品'
      });
    }

    console.log('准备软删除商品 (removeProduct):', {
      productId: product.productId, // 使用正确字段名
      title: product.title,
      dealerId: product.dealerId
    });

    // *** 改为物理删除以匹配 deleteProduct 的行为 ***
    // 先删除所有语言版本
    await deleteProductTranslations(product.productId);
    
    // 再删除主表记录
    await product.destroy();
    console.log('商品及其所有语言版本删除成功 (removeProduct as destroy)');

    // 返回结果 - 204 No Content
    return res.status(204).send();

  } catch (error) {
    console.error('删除商品出错 (removeProduct):', error);
    const errorMessage = error.original ? error.original.sqlMessage : error.message;
    return res.status(500).json({
      success: false,
      message: `删除商品失败: ${errorMessage}`
    });
  }
};

/**
 * 修改商品状态
 */
const changeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // 验证输入
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    if (!status || !isValidStatus(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }
    
    // 获取认证用户信息
    const user = req.user;
    console.log('更改商品状态的用户信息:', JSON.stringify(user));
    
    // 检查用户类型
    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确:', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法更改商品状态'
      });
    }
    
    // 直接使用JWT中的ID作为车商ID
    const dealerId = user.id;
    console.log('使用dealerId:', dealerId);

    // 查找要更新的商品
    const product = await Product.findOne({
      where: {
        productId: id,  // 修复：使用正确的字段名 productId
        dealerId: dealerId
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在或您无权访问此商品'
      });
    }
    
    console.log('准备更新商品状态:', {
      id: product.id,
      title: product.title,
      currentStatus: product.status,
      newStatus: status
    });

    // 🚫 如果要将状态改为active，检查发布限制
    if (status === 'active' && product.status !== 'active') {
      const limitCheck = await checkPublishLimit(dealerId);
      if (!limitCheck.allowed) {
        console.log('上架时发布限制检查失败:', limitCheck.error);
        return res.status(403).json({
          success: false,
          message: limitCheck.error,
          data: {
            currentCount: limitCheck.currentCount,
            limit: limitCheck.limit
          }
        });
      }
      console.log(`✅ 上架发布限制检查通过: ${limitCheck.currentCount}/${limitCheck.limit}, 剩余${limitCheck.remaining}个名额`);
    }

    // 更新状态
    await product.update({ 
      status,
      updatedAt: new Date(),
      
      // 如果状态为sold，设置soldAt字段
      ...(status === 'sold' ? { soldAt: new Date() } : {}),
      
      // 如果状态为expired，设置expiredAt字段
      ...(status === 'expired' ? { expiredAt: new Date() } : {})
    });
    
    console.log('商品状态更新成功');

    // 如果状态从非active变为active，触发自动翻译
    if (status === 'active' && product.status !== 'active') {
      console.log(`产品 ${product.productId} 状态变为 active，触发自动翻译`);

      // 异步触发翻译，不阻塞响应
      handleProductOnShelf(product.productId).catch(error => {
        console.error(`自动翻译产品 ${product.productId} 失败:`, error);
      });
    }

    // 返回结果
    return res.status(200).json({
      success: true,
      message: '商品状态更新成功',
      data: {
        id: product.id,
        status: product.status
      }
    });
  } catch (error) {
    console.error('更新商品状态出错:', error);
    return res.status(500).json({
      success: false,
      message: '更新商品状态失败: ' + error.message
    });
  }
};

/**
 * 上架商品
 * 与createProduct类似，但有不同的逻辑和响应格式
 */
const listProduct = async (req, res) => {
  try {
    // 获取认证用户信息
    const user = req.user;
    console.log('认证用户信息:', JSON.stringify(user));
    
    // 检查用户类型
    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确:', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法上架商品'
      });
    }
    
    // 直接使用JWT中的ID作为车商ID
    const dealerId = user.id;
    console.log('使用dealerId:', dealerId);

    // 🚫 检查发布限制
    const limitCheck = await checkPublishLimit(dealerId);
    if (!limitCheck.allowed) {
      console.log('发布限制检查失败:', limitCheck.error);
      return res.status(403).json({
        success: false,
        message: limitCheck.error,
        data: {
          currentCount: limitCheck.currentCount,
          limit: limitCheck.limit
        }
      });
    }

    console.log(`✅ 发布限制检查通过: ${limitCheck.currentCount}/${limitCheck.limit}, 剩余${limitCheck.remaining}个名额`);

    // 从请求体获取商品数据
    let productData = { ...req.body };
    console.log('请求数据:', JSON.stringify(productData).substring(0, 500) + '...');

    // 设置经销商ID
    productData.dealerId = dealerId;

    // 验证必要字段
    if (!productData.title) {
      return res.status(400).json({
        success: false,
        message: '商品标题不能为空'
      });
    }

    // 设置上架状态
    productData.status = 'active';
    productData.publishedAt = new Date();

    // 设置默认值，包括翻译必填字段
    productData = setDefaultValues(productData);

    // **** 后端生成 productId ****
    productData.productId = uuidv4();
    console.log('后端生成商品编号(上架):', productData.productId);

    // 转换相对路径为完整URL的函数
    const convertToFullUrl = (url, baseUrl = 'http://localhost:3010') => {
      if (!url) return null;

      // 如果已经是完整URL，直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 如果是相对路径，转换为完整URL
      if (url.startsWith('/uploads/')) {
        return `${baseUrl}${url}`;
      }

      // 如果是其他格式，保持原样
      return url;
    };

    // 获取基础URL（从环境变量或默认值）
    const baseUrl = process.env.BASE_URL || 'http://localhost:3010';

    // 处理图片信息 - 优先使用小程序传递的图片URL
    console.log('处理图片信息:', {
      mainImage: productData.mainImage,
      images: productData.images,
      imagesType: typeof productData.images
    });

    if (productData.mainImage && productData.mainImage !== '/default-car-image.jpg') {
      // 转换主图URL为完整URL
      productData.mainImage = convertToFullUrl(productData.mainImage, baseUrl);
      console.log('使用小程序传递的主图（转换为完整URL）:', productData.mainImage);
    } else {
      productData.mainImage = `${baseUrl}/default-car-image.jpg`;
      console.log('设置默认主图（完整URL）:', productData.mainImage);
    }

    if (productData.images && productData.images !== '[]' && productData.images !== '["/default-car-image.jpg"]') {
      let imageArray = [];

      // 处理images数据
      if (typeof productData.images === 'string') {
        try {
          imageArray = JSON.parse(productData.images);
        } catch (e) {
          console.warn('解析图片列表JSON失败，使用原始字符串:', productData.images);
          imageArray = [productData.images];
        }
      } else if (Array.isArray(productData.images)) {
        imageArray = productData.images;
      }

      // 转换所有图片URL为完整URL
      const convertedImages = imageArray.map(url => convertToFullUrl(url, baseUrl)).filter(url => url);

      productData.images = JSON.stringify(convertedImages);
      console.log('图片列表转换为完整URL并格式化为JSON:', productData.images);
    } else {
      productData.images = JSON.stringify([`${baseUrl}/default-car-image.jpg`]);
      console.log('设置默认图片列表（完整URL）:', productData.images);
    }

    // 确保翻译必填字段有默认值（在数据库保存前设置）
    if (!productData.color) {
      productData.color = '未知';
      console.log('设置默认颜色: 未知');
    }

    if (!productData.interiorColor) {
      productData.interiorColor = '未知';
      console.log('设置默认内饰颜色: 未知');
    }
    
    // 确保必须的 modelId 存在
    if (!productData.modelId) {
      console.error('缺失必需的 modelId');
      return res.status(400).json({
        success: false,
        message: '车型ID (modelId) 不能为空'
      });
    }

    // 如果有modelId，从config_car表获取车辆规格信息
    if (productData.modelId) {
      try {
        const { ConfigCar } = require('../../../models');
        const carConfig = await ConfigCar.findOne({
          where: { modelId: productData.modelId.toString() },
          attributes: ['energyType', 'bodyStructure', 'gearboxType', 'driveType', 'brandName'],
          raw: true
        });

        if (carConfig) {
          console.log('从config_car表获取到车辆规格信息:', carConfig);

          // 设置车辆规格字段
          productData.energyType = carConfig.energyType;
          productData.bodyStructure = carConfig.bodyStructure;
          productData.gearboxType = carConfig.gearboxType;
          productData.driveType = carConfig.driveType;

          // 如果没有设置品牌，从config_car表获取
          if (!productData.brand && carConfig.brandName) {
            productData.brand = carConfig.brandName;
          }
        } else {
          console.warn(`未找到modelId ${productData.modelId} 对应的车型配置信息`);
        }
      } catch (configError) {
        console.error('获取车型配置信息失败:', configError);
        // 不阻止商品创建，只是记录错误
      }
    }

    // 确保 condition 字段是字符串格式
    if (productData.condition && typeof productData.condition === 'object') {
      productData.condition = JSON.stringify(productData.condition);
    }

    // 确保 images 字段是字符串格式
    if (productData.images && typeof productData.images === 'object') {
      productData.images = JSON.stringify(productData.images);
    }
    
    // --- 防御性代码：清理 condition 字段 (上架) ---
    if (typeof productData.condition === 'string' && productData.condition.startsWith('{"description":')) {
      console.warn(`[LIST] 检测到 condition 字段包含默认 JSON 字符串，将其设为 null。原始值: ${productData.condition}`);
      productData.condition = null;
    } else if (productData.condition === '') { // 如果是空字符串也设为 null
      productData.condition = null;
    }
    // --------------------------------------
    
    console.log('[DEBUG] 即将保存到数据库的 productData (上架):', JSON.stringify(productData, null, 2)); // 添加详细日志

    // 创建商品记录
    let product;
    try {
      product = await Product.create(productData);
      console.log('商品上架成功(创建)，数据库ID:', product.id, '商品编号:', product.productId);
    } catch (dbError) {
      console.error('数据库创建商品记录失败(上架):', dbError);
      return res.status(500).json({
        success: false,
        message: '数据库保存商品失败(上架): ' + dbError.message,
        errorDetails: dbError
      });
    }
    
    console.log('商品创建成功，ID:', product.productId);

    // 如果产品状态为active，触发翻译
    if (product.status === 'active') {
      console.log(`新产品 ${product.productId} 状态为 active，触发自动翻译`);

      // 统一使用旧翻译系统（内部已经整合了新翻译引擎的降级机制）
      handleProductOnShelf(product.productId).catch(error => {
        console.error(`自动翻译新产品 ${product.productId} 失败:`, error);
      });
    }

    // 返回上架的商品
    return res.status(201).json({
      success: true,
      message: '商品上架成功',
      data: {
        id: product.productId,
        title: product.title,
        price: product.price,
        status: product.status,
        publishedAt: product.publishedAt
      }
    });
  } catch (error) {
    console.error('上架商品出错:', error);

    // 处理验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: '商品数据验证失败: ' + error.errors.map(e => `${e.path}: ${e.message}`).join(', '),
        code: 'VALIDATION_ERROR',
        errors: error.errors.map(e => ({ field: e.path, message: e.message }))
      });
    }

    // 处理数据库约束错误
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        success: false,
        message: '商品已存在或违反唯一性约束',
        code: 'DUPLICATE_ERROR'
      });
    }

    // 处理外键约束错误
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      return res.status(400).json({
        success: false,
        message: '关联数据不存在，请检查车型配置',
        code: 'FOREIGN_KEY_ERROR'
      });
    }

    // 处理其他数据库错误
    if (error.name && error.name.startsWith('Sequelize')) {
      return res.status(500).json({
        success: false,
        message: '数据库操作失败: ' + error.message,
        code: 'DATABASE_ERROR'
      });
    }

    // 处理一般错误
    return res.status(500).json({
      success: false,
      message: '上架商品失败: ' + (error.message || '未知错误'),
      code: 'INTERNAL_ERROR'
    });
    
    return res.status(500).json({
      success: false,
      message: '上架商品失败: ' + error.message
    });
  }
};

/**
 * 翻译商品信息
 */
const translateProduct = async (req, res) => {
  try {
    const { productId } = req.params;
    
    // 验证productId
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    // 获取认证用户信息
    const user = req.user;
    console.log('翻译请求的用户信息:', JSON.stringify(user));
    
    // 检查用户类型
    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确:', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无权限翻译商品'
      });
    }
    
    // 直接使用JWT中的ID作为车商ID
    const dealerId = user.id;
    console.log('使用dealerId:', dealerId);
    
    // 查找商品
    const product = await Product.findOne({
      where: {
        id: productId,
        dealerId: dealerId
      }
    });
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在或您无权访问此商品'
      });
    }
    
    // 获取商品标题和描述
    const { title, description } = product;
    
    // 调用翻译服务
    // 注: 实际项目中需要接入真实的翻译API
    const translationResults = {
      title: {
        en: `[EN] ${title}`,
        ja: `[JA] ${title}`,
        ko: `[KO] ${title}`
      },
      description: description ? {
        en: `[EN] ${description}`,
        ja: `[JA] ${description}`,
        ko: `[KO] ${description}`
      } : null
    };
    
    // 更新商品的国际化字段
    await product.update({
      i18n: JSON.stringify(translationResults)
    });
    
    return res.status(200).json({
      success: true,
      message: '商品信息翻译成功',
      data: translationResults
    });
  } catch (error) {
    console.error('翻译商品信息出错:', error);
    return res.status(500).json({
      success: false,
      message: '翻译商品信息失败: ' + error.message
    });
  }
};

/**
 * 根据商品编号获取商品详情
 */
const getProductByProductId = async (req, res) => {
  try {
    const { productId } = req.params;
    const locale = req.query.locale; // 不设置默认值，微信小程序使用中文表

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品编号不能为空'
      });
    }

    console.log(`收到获取车辆详情请求，车辆ID: ${productId}, 语言: ${locale || '中文(products表)'}`);

    const db = require('../../../models');
    let ProductModel = db.Product; // 默认使用中文products表
    let ConfigCarModel = db.ConfigCar; // 默认使用中文配置表

    // 只有明确指定locale时才使用语言表，微信小程序使用中文表
    if (locale === 'en') {
      ProductModel = db.ProductEn || db.Product;
      ConfigCarModel = db.ConfigCarEn || db.ConfigCar;
    } else if (locale === 'ru') {
      ProductModel = db.ProductRu || db.Product;
      ConfigCarModel = db.ConfigCarRu || db.ConfigCar;
    } else if (locale === 'es') {
      ProductModel = db.ProductEs || db.Product;
      ConfigCarModel = db.ConfigCarEs || db.ConfigCar;
    } else if (locale === 'fr') {
      ProductModel = db.ProductFr || db.Product;
      ConfigCarModel = db.ConfigCarFr || db.ConfigCar;
    }
    // 如果没有locale参数或不支持的语言，使用中文products表
    
    console.log(`使用模型: ${ProductModel.name}, ${ConfigCarModel ? ConfigCarModel.name : '无配置模型'}`);
    
    const productInstance = await ProductModel.findOne({
        where: { productId: productId }
      });

    if (!productInstance) {
      return res.status(404).json({
        success: false,
        message: '未找到该车辆'
      });
    }
    
    // 过滤 productInstance 的字段
    const filteredProductData = {};
    const productDataRaw = productInstance.toJSON();
    for (const key in productDataRaw) {
      if (productDataRaw[key] !== null && productDataRaw[key] !== undefined && productDataRaw[key] !== '') {
        filteredProductData[key] = productDataRaw[key];
      }
    }

    const modelId = filteredProductData.modelId; // 使用过滤后的数据获取modelId
    console.log(`车辆 ${productId} 的modelId: ${modelId}`);
    
    let filteredConfigCarData = null;
    if (modelId && ConfigCarModel) {
      try {
        const configCarInstance = await ConfigCarModel.findOne({
          where: { '车型_id': modelId.toString() },
          attributes: {
            exclude: ['minGuidePrice(Wan)', 'maxGuidePrice(Wan)', 'msrp(Yuan)', 'warranty']
          }
        });
        if (configCarInstance) {
            filteredConfigCarData = {};
            const configCarDataRaw = configCarInstance.get();
            const modelAttributes = ConfigCarModel.rawAttributes;
            for (const key in configCarDataRaw) {
                if (configCarDataRaw[key] !== null && configCarDataRaw[key] !== undefined && configCarDataRaw[key] !== '') {
                    // 使用 model 'field' 属性作为键名 (如果存在)，否则使用原始键名
                    const fieldName = modelAttributes[key] && modelAttributes[key].field ? modelAttributes[key].field : key;
                    filteredConfigCarData[fieldName] = configCarDataRaw[key];
                }
            }
        }
        console.log(`找到车型配置: ${filteredConfigCarData ? 'yes' : 'no'}`);
      } catch (configError) {
        console.error('获取车型配置信息失败:', configError);
      }
    }
    
    const vehicleData = { ...filteredProductData }; // 使用过滤后的产品数据作为基础
    if (filteredConfigCarData) {
      vehicleData.configCar = filteredConfigCarData;
    }

    return res.status(200).json({
      success: true,
      message: '获取车辆详情成功',
      data: vehicleData
    });
  } catch (error) {
    console.error('获取车辆详情出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取车辆详情失败: ' + error.message
    });
  }
};

// 删除产品
const deleteProduct = async (req, res) => {
  const { productId: routeProductId } = req.params; // 重命名以免与模型字段混淆
  const dealerId = req.user ? req.user.dealerId : null; // 从认证中间件获取经销商ID, 增加空检查

  // 增加日志记录传入的参数和用户信息
  console.log(`[INFO] deleteProduct called with productId: ${routeProductId}`);
  console.log(`[INFO] User info from token:`, req.user);

  if (!routeProductId) {
    console.log('[WARN] Missing productId in request params');
    return res.status(400).json({ success: false, message: '缺少产品ID' });
  }

  if (!dealerId) {
    console.log('[WARN] Missing dealerId in authenticated user');
    return res.status(403).json({ success: false, message: '未经授权的操作，无法识别经销商身份' });
  }

  console.log(`[DEBUG] Attempting to delete product ${routeProductId} by dealer ${dealerId}`);

  try {
    // 构建查询条件
    const whereCondition = {
      productId: routeProductId, // 使用从路由参数获取的 productId
      dealerId: dealerId       // 确保经销商只能删除自己的产品
    };

    // 在执行查询前打印实际使用的查询条件
    console.log('[CRITICAL_DEBUG] Product.findOne where condition:', JSON.stringify(whereCondition));

    // 使用正确的字段名 'productId' 进行查询
    const product = await Product.findOne({
      where: whereCondition // 使用上面构建并打印的条件
    });

    if (!product) {
      console.log(`[WARN] Product ${routeProductId} not found or does not belong to dealer ${dealerId}`);
      return res.status(404).json({ success: false, message: '未找到要删除的产品或您无权删除该产品' });
    }

    // 执行物理删除
    // 先删除所有语言版本
    await deleteProductTranslations(product.productId);
    
    // 再删除主表记录
    await product.destroy();
    console.log('商品及其所有语言版本删除成功 (deleteProduct)');

    // 返回成功响应
    res.status(200).json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    console.error(`[ERROR] Failed to delete product ${routeProductId}:`, error);
    // 记录详细错误信息
    const errorMessage = error.original ? error.original.sqlMessage : error.message;
    return res.status(500).json({ 
      success: false, 
      message: `删除商品失败: ${errorMessage}`,
      // 开发环境下可以考虑返回更详细的错误堆栈
      // error: process.env.NODE_ENV !== 'production' ? error : undefined 
    });
  }
};

/**
 * 手动触发翻译（带权限检查）
 * TODO: 增加权限控制，例如仅管理员或指定角色可以触发
 */
const manualTranslateProductWithAuth = async (req, res) => {
  try {
    const { productId } = req.params;
    const user = req.user; // 假设有用户信息

    // TODO: 添加更严格的权限检查
    if (!user || user.type !== 'admin') {
      // return res.status(403).json({ success: false, message: '无权执行此操作' });
    }

    if (!productId) {
      return res.status(400).json({ success: false, message: '缺少产品ID' });
    }

    const product = await Product.findOne({ where: { productId: productId } });
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到产品' });
    }

    console.log(`[手动翻译触发] 用户 ${user?.id || '未知'} 触发产品 ${productId} 的翻译`);

    // --- 异步处理翻译 --- 
    // 移除 await，让翻译在后台执行
    // 使用 IIFE (立即调用函数表达式) 启动后台任务，并添加错误处理
    (async () => {
      try {
        console.log(`[后台翻译] 开始处理产品 ${productId}...`);
        if (product.status === 'active') {
          console.log(`产品 ${productId} 状态为 active，调用 handleProductOnShelf`);
          await autoTranslationService.handleProductOnShelf(productId); 
          console.log(`[后台翻译] handleProductOnShelf 处理产品 ${productId} 完成`);
        } else {
          console.log(`产品 ${productId} 状态为 ${product.status}，将直接调用核心翻译函数`);
          const productTranslation = await autoTranslationService.translateProductSelective(productId);
          console.log(`[后台翻译] translateProductSelective 处理产品 ${productId} 完成，结果: ${productTranslation.success}`);
          // 如果产品翻译成功且有关联的车型ID，则继续翻译配置
          if (productTranslation.success && product.modelId) {
            console.log(`[后台翻译] 开始处理车型 ${product.modelId} 的配置翻译...`);
            await autoTranslationService.translateConfigCarSelective(product.modelId);
            console.log(`[后台翻译] translateConfigCarSelective 处理车型 ${product.modelId} 完成`);
          }
        }
        console.log(`[后台翻译] 产品 ${productId} 的后台翻译任务完成。`);
      } catch (backgroundError) {
        console.error(`[后台翻译] 处理产品 ${productId} 时发生错误:`, backgroundError);
        // 在这里可以添加错误记录逻辑，例如更新产品状态为失败
        try {
          await Product.update(
            { translationError: backgroundError.message || '后台翻译任务失败', is_translating: false }, 
            { where: { productId: productId } }
          );
        } catch (updateError) {
          console.error(`[后台翻译] 更新产品 ${productId} 错误状态失败:`, updateError);
        }
      }
    })();

    // --- 立即返回响应 --- 
    // 不再等待翻译结果，直接返回 202 Accepted
    return res.status(202).json({
      success: true,
      message: '产品翻译请求已接受，正在后台处理中。',
      data: {
        status: 'processing',
        productId: productId
      }
    });

  } catch (error) {
    // 这里的 catch 主要捕获查找产品或权限检查时的错误
    console.error('手动触发翻译失败 (控制器层面):', error);
    return res.status(500).json({
      success: false,
      message: '触发翻译失败: ' + error.message
    });
  }
};

/**
 * 获取产品翻译状态
 */
const getProductTranslationStatus = async (req, res) => {
  try {
    // 获取产品ID
    const { productId } = req.params;
    
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }
    
    // 查找产品
    const product = await Product.findOne({ where: { productId: productId } });
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的产品'
      });
    }
    
    // 检查用户权限（只有管理员或产品所有者可以查看）
    const user = req.user;
    const isAdmin = user && user.type === 'admin';
    const isDealerOwner = user && user.type === 'dealer' && user.id === product.dealerId;
    
    if (!isAdmin && !isDealerOwner) {
      return res.status(403).json({
        success: false,
        message: '无权查看该产品的翻译状态'
      });
    }
    
    // 确定翻译状态
    let translationStatus = 'unknown';

    // 基于 test_all 字段判断翻译状态
    if (product.test_all === true) {
      translationStatus = 'completed';
    } else if (product.translationError) {
      translationStatus = 'failed';
    } else if (product.test_all === false) {
      translationStatus = 'pending';
    }
    
    // 获取详细的翻译状态（如果存在）
    let detailedStatus = null;
    
    if (product.translationStatus) {
      try {
        if (typeof product.translationStatus === 'string') {
          detailedStatus = JSON.parse(product.translationStatus);
        } else {
          detailedStatus = product.translationStatus;
        }
      } catch (error) {
        console.error('解析翻译状态详情失败:', error);
      }
    }
    
    // 获取各语言的翻译状态
    const languageStatus = {
      en: product.test_en === true,
      ru: product.test_ru === true,
      fr: product.test_fr === true,
      es: product.test_es === true
    };
    
    // 计算估计的完成时间
    let estimatedCompletionTime = null;
    
    if (translationStatus === 'processing' && product.lastTranslatedAt) {
      estimatedCompletionTime = new Date(product.lastTranslatedAt);
      estimatedCompletionTime.setSeconds(estimatedCompletionTime.getSeconds() + 30); // 假设翻译需要30秒
    }
    
    // 返回翻译状态信息
    return res.status(200).json({
      success: true,
      data: {
        productId: product.productId,
        status: translationStatus,
        languageStatus,
        startedAt: product.lastTranslatedAt,
        estimatedCompletionTime,
        error: product.translationError || null,
        detailedStatus
      }
    });
    
  } catch (error) {
    console.error('获取产品翻译状态失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取产品翻译状态失败: ' + error.message
    });
  }
};

// --- 修改 getProductCategories 函数 - 基于车身结构 ---
const getProductCategories = async (req, res) => {
  try {
    const { locale = 'zh' } = req.query;

    console.log(`[getProductCategories] Called with locale: ${locale}`);

    // 选择对应语言的模型
    const db = require('../../../models');
    let TargetModel = db.Product;

    if (locale && locale !== 'zh' && db.PRODUCT_MODELS && db.PRODUCT_MODELS[locale]) {
      TargetModel = db.PRODUCT_MODELS[locale];
    }

    // 获取所有不同的车身结构及其数量
    const bodyStructureStats = await TargetModel.findAll({
      attributes: [
        'bodyStructure',
        [db.Sequelize.fn('COUNT', db.Sequelize.col('bodyStructure')), 'count']
      ],
      where: {
        bodyStructure: { [db.Sequelize.Op.ne]: null },
        status: 'active' // 只统计已上架的车辆
      },
      group: ['bodyStructure'],
      raw: true
    });

    // 车身结构到图片的映射
    const bodyStructureImageMap = {
      '轿车': '/images/category-placeholders/sedan.svg',
      '三厢车': '/images/category-placeholders/sedan.svg',
      'sedan': '/images/category-placeholders/sedan.svg',
      'SUV': '/images/category-placeholders/suv.svg',
      'suv': '/images/category-placeholders/suv.svg',
      '掀背车': '/images/category-placeholders/hatchback.svg',
      '两厢车': '/images/category-placeholders/hatchback.svg',
      'hatchback': '/images/category-placeholders/hatchback.svg',
      '跑车': '/images/category-placeholders/coupe.svg',
      'coupe': '/images/category-placeholders/coupe.svg',
      '旅行车': '/images/category-placeholders/wagon.svg',
      'wagon': '/images/category-placeholders/wagon.svg',
      '皮卡': '/images/category-placeholders/pickup.svg',
      'pickup': '/images/category-placeholders/pickup.svg',
      '敞篷车': '/images/category-placeholders/convertible.svg',
      'convertible': '/images/category-placeholders/convertible.svg',
      'MPV': '/images/category-placeholders/mpv.svg',
      'mpv': '/images/category-placeholders/mpv.svg'
    };

    // 格式化数据
    const categories = bodyStructureStats
      .filter(item => item.bodyStructure && item.bodyStructure.trim() !== '')
      .map((item, index) => {
        const bodyStructure = item.bodyStructure.trim();
        const slug = bodyStructure.toLowerCase().replace(/\s+/g, '-');

        return {
          id: `${slug}-${index + 1}`,
          name: bodyStructure,
          slug: slug,
          count: parseInt(item.count) || 0,
          image: bodyStructureImageMap[bodyStructure] || bodyStructureImageMap[bodyStructure.toLowerCase()] || '/images/category-placeholders/default.svg'
        };
      })
      .sort((a, b) => b.count - a.count); // 按数量降序排列

    console.log(`[getProductCategories] Found ${categories.length} body structure categories`);

    res.status(200).json({
      success: true,
      message: 'Categories fetched successfully',
      data: categories
    });

  } catch (error) {
    console.error('Error in getProductCategories:', error);

    // 如果数据库查询失败，返回基本的备用分类
    const fallbackCategories = [
      {
        id: 'sedan-1',
        name: '轿车',
        slug: 'sedan',
        count: 0,
        image: '/images/category-placeholders/sedan.svg'
      },
      {
        id: 'suv-2',
        name: 'SUV',
        slug: 'suv',
        count: 0,
        image: '/images/category-placeholders/suv.svg'
      },
      {
        id: 'hatchback-3',
        name: '掀背车',
        slug: 'hatchback',
        count: 0,
        image: '/images/category-placeholders/hatchback.svg'
      }
    ];

    res.status(200).json({
      success: true,
      message: 'Categories fetched successfully (fallback)',
      data: fallbackCategories
    });
  }
};

const getFeaturedProducts = async (req, res) => {
  try {
    const { locale, limit = 6 } = req.query;
    // TODO: 实现获取推荐商品的逻辑，考虑 locale 和 limit
    // 暂时返回空数组
    console.log(`[getFeaturedProducts] Called with locale: ${locale}, limit: ${limit}`);
    res.status(200).json({ success: true, message: 'Featured products fetched (placeholder)', data: [] });
  } catch (error) {
    console.error('Error in getFeaturedProducts:', error);
    res.status(500).json({ success: false, message: 'Failed to get featured products: ' + error.message });
  }
};

const getLatestProducts = async (req, res) => {
  try {
    const { locale, limit = 6 } = req.query;
    // TODO: 实现获取最新商品的逻辑，考虑 locale 和 limit
    // 暂时返回空数组
    console.log(`[getLatestProducts] Called with locale: ${locale}, limit: ${limit}`);
    res.status(200).json({ success: true, message: 'Latest products fetched (placeholder)', data: [] });
  } catch (error) {
    console.error('Error in getLatestProducts:', error);
    res.status(500).json({ success: false, message: 'Failed to get latest products: ' + error.message });
  }
};

/**
 * 获取车辆规格筛选选项
 */
const getVehicleSpecOptions = async (req, res) => {
  try {
    const { locale = 'zh' } = req.query;

    // 选择对应语言的模型
    const db = require('../../../models');
    let TargetModel = db.Product;

    if (locale && locale !== 'zh' && db.PRODUCT_MODELS && db.PRODUCT_MODELS[locale]) {
      TargetModel = db.PRODUCT_MODELS[locale];
    }

    // 构建基础查询条件，不包含status字段（因为可能不存在）
    const baseWhere = {};

    // 获取各个字段的不重复值
    const [energyTypes, bodyStructures, gearboxTypes, driveTypes] = await Promise.all([
      TargetModel.findAll({
        attributes: [[db.Sequelize.fn('DISTINCT', db.Sequelize.col('energyType')), 'value']],
        where: {
          energyType: { [db.Sequelize.Op.ne]: null }
        },
        raw: true
      }),
      TargetModel.findAll({
        attributes: [[db.Sequelize.fn('DISTINCT', db.Sequelize.col('bodyStructure')), 'value']],
        where: {
          bodyStructure: { [db.Sequelize.Op.ne]: null }
        },
        raw: true
      }),
      TargetModel.findAll({
        attributes: [[db.Sequelize.fn('DISTINCT', db.Sequelize.col('gearboxType')), 'value']],
        where: {
          gearboxType: { [db.Sequelize.Op.ne]: null }
        },
        raw: true
      }),
      TargetModel.findAll({
        attributes: [[db.Sequelize.fn('DISTINCT', db.Sequelize.col('driveType')), 'value']],
        where: {
          driveType: { [db.Sequelize.Op.ne]: null }
        },
        raw: true
      })
    ]);

    // 格式化数据
    const formatOptions = (options) => {
      return options
        .map(item => item.value)
        .filter(value => value && value.trim() !== '')
        .sort()
        .map((value, index) => ({
          id: index + 1,
          name: value,
          slug: value.toLowerCase().replace(/\s+/g, '-')
        }));
    };

    const result = {
      energyTypes: formatOptions(energyTypes),
      bodyStructures: formatOptions(bodyStructures),
      gearboxTypes: formatOptions(gearboxTypes),
      driveTypes: formatOptions(driveTypes)
    };

    return res.status(200).json({
      success: true,
      message: '获取车辆规格选项成功',
      data: result
    });
  } catch (error) {
    console.error('获取车辆规格选项出错:', error);
    return res.status(500).json({
      success: false,
      message: '获取车辆规格选项失败: ' + error.message
    });
  }
};
// --- 结束新增 ---

/**
 * 获取车商发布限制信息
 */
const getPublishLimitInfo = async (req, res) => {
  try {
    const { user } = req;
    if (!user || !user.id) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      });
    }

    const dealerId = user.id;

    // 获取发布限制检查结果
    const limitInfo = await checkPublishLimit(dealerId);

    return res.json({
      success: true,
      data: {
        dealerId: dealerId,
        currentCount: limitInfo.currentCount,
        limit: limitInfo.limit,
        remaining: limitInfo.remaining || 0,
        allowed: limitInfo.allowed,
        message: limitInfo.allowed
          ? `还可以发布 ${limitInfo.remaining} 辆车`
          : limitInfo.error
      }
    });

  } catch (error) {
    console.error('获取发布限制信息失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取发布限制信息失败: ' + error.message
    });
  }
};

/**
 * 仅改变商品状态，不触发翻译过程
 * 专门用于上/下架操作，不影响其他翻译流程
 */
const statusChangeOnly = async (req, res) => {
  try {
    const { productId } = req.params;
    const { status } = req.body;
    
    // 验证输入
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    if (!status || !isValidStatus(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }
    
    // 获取认证用户信息
    const user = req.user;
    console.log('仅状态变更 - 用户信息:', JSON.stringify(user));
    
    // 检查用户类型
    if (!user || user.type !== 'dealer') {
      console.log('用户类型不正确:', user?.type);
      return res.status(403).json({
        success: false,
        message: '用户不是车商，无法更改商品状态'
      });
    }
    
    // 直接使用JWT中的ID作为车商ID
    const dealerId = user.id;
    console.log('仅状态变更 - 使用dealerId:', dealerId);

    // 查找要更新的商品
    const product = await Product.findOne({
      where: {
        productId: productId,
        dealerId: dealerId
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在或您无权访问此商品'
      });
    }
    
    console.log('仅状态变更 - 准备更新商品状态:', {
      id: product.id,
      productId: product.productId,
      title: product.title,
      currentStatus: product.status,
      newStatus: status
    });

    // 🚫 如果要将状态改为active，检查发布限制
    if (status === 'active' && product.status !== 'active') {
      const limitCheck = await checkPublishLimit(dealerId);
      if (!limitCheck.allowed) {
        console.log('仅状态变更 - 上架时发布限制检查失败:', limitCheck.error);
        return res.status(403).json({
          success: false,
          message: limitCheck.error,
          data: {
            currentCount: limitCheck.currentCount,
            limit: limitCheck.limit
          }
        });
      }
      console.log(`仅状态变更 - ✅ 发布限制检查通过: ${limitCheck.currentCount}/${limitCheck.limit}, 剩余${limitCheck.remaining}个名额`);
    }

    // 更新状态
    await product.update({ 
      status,
      updatedAt: new Date(),
      
      // 如果状态为sold，设置soldAt字段
      ...(status === 'sold' ? { soldAt: new Date() } : {}),
      
      // 如果状态为expired，设置expiredAt字段
      ...(status === 'expired' ? { expiredAt: new Date() } : {})
    });
    
    console.log('仅状态变更 - 商品状态更新成功，不触发翻译流程');

    // 同步更新所有语言表中的状态
    try {
      console.log(`同步更新所有语言表中商品 ${productId} 的状态为 ${status}`);
      const languages = ['en', 'ru', 'es', 'fr'];
      
      // 确保db对象可用
      const db = require('../../../models');
      
      for (const lang of languages) {
        // 获取对应语言的模型
        const modelName = `Product${lang.charAt(0).toUpperCase() + lang.slice(1)}`;
        const LangModel = db[modelName];
        
        if (!LangModel) {
          console.warn(`语言模型 ${modelName} 不存在，跳过更新`);
          continue;
        }
        
        // 更新对应语言表中的商品状态
        const [updatedRows] = await LangModel.update(
          { 
            status,
            updatedAt: new Date()
          },
          { 
            where: { productId: productId }
          }
        );
        
        console.log(`${modelName} 表中更新了 ${updatedRows} 条记录`);
      }
    } catch (translationError) {
      // 如果更新翻译表出错，记录错误但不中断流程
      console.error('同步更新语言表状态时出错:', translationError);
    }

    // 注意这里跳过了翻译触发步骤

    // 返回结果
    return res.status(200).json({
      success: true,
      message: '商品状态更新成功',
      data: {
        id: product.id,
        productId: product.productId,
        status: product.status
      }
    });
  } catch (error) {
    console.error('仅状态变更 - 更新失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新商品状态失败: ' + error.message
    });
  }
};

// 统一导出控制器函数
module.exports = {
  getDealerProducts,
  setDefaultValues,
  searchProductList,
  getProductDetail,
  createProduct,
  updateProduct,
  removeProduct,
  changeStatus,
  listProduct,
  translateProduct,
  getProductByProductId,
  deleteProduct,
  manualTranslateProductWithAuth,
  getProductTranslationStatus,
  getProductCategories,
  getFeaturedProducts,
  getLatestProducts,
  getVehicleSpecOptions,
  getPublishLimitInfo,
  statusChangeOnly
};