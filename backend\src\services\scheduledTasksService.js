/**
 * 定时任务服务
 * 集中管理系统定时执行的任务
 */

const cron = require('node-cron');
const db = require('../models');
const { Product } = db;
const autoTranslationService = require('./autoTranslationService');
const newScheduledTasksService = require('./newScheduledTasksService');

const logger = require('../utils/logger');

// 日志记录函数
const logTask = (taskName, message, isError = false) => {
  if (isError) {
    logger.simpleError(`定时任务 ${taskName}: ${message}`);
  } else {
    // 简化模式：只显示任务开始和完成的重要节点
    if (message.includes('开始执行') || message.includes('执行完成') || message.includes('执行出错')) {
      logger.milestone(`定时任务 ${taskName}: ${message}`);
    } else {
      logger.debug(`定时任务 ${taskName}: ${message}`);
    }
  }
};

/**
 * 清理已删除产品记录
 * 删除各语言表中对应于主表中已删除的记录
 */
const cleanupDeletedProductsTask = async () => {
  const taskName = '清理已删除产品记录';
  try {
    logTask(taskName, '开始执行');
    
    const result = await Product.cleanupDeletedRecords();
    
    if (result.success) {
      logTask(taskName, `执行完成，共处理 ${result.productCount} 个产品，删除了 ${result.count} 条语言记录`);
    } else {
      logTask(taskName, `执行失败: ${result.error}`, true);
    }
    
    return result;
  } catch (error) {
    logTask(taskName, `执行出错: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 自动翻译未翻译的产品
 */
const autoTranslateProductsTask = async () => {
  const taskName = '自动翻译未翻译产品';

  try {
    logTask(taskName, '开始执行');

    // 统一使用新翻译系统
    const result = await newScheduledTasksService.autoTranslateProductsTask();

    // 转换新系统结果格式以兼容旧系统
    const formattedResult = {
      total: result.processed || 0,
      success: result.successful || 0,
      failure: result.failed || 0,
      configsTriggered: 0, // 新系统暂不支持配置翻译触发统计
      originalResult: result
    };

    logTask(taskName, `执行完成，共处理 ${formattedResult.total} 个产品，成功 ${formattedResult.success} 个，失败 ${formattedResult.failure} 个`);
    return formattedResult;

  } catch (error) {
    logTask(taskName, `执行出错: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

/**
 * 检查产品表与语言表的同步状态
 */
const checkProductSyncTask = async () => {
  const taskName = '检查产品同步状态';
  try {
    logTask(taskName, '开始执行');
    
    // 1. 检查主表中存在但语言表中不存在的记录
    const languages = autoTranslationService.SUPPORTED_LANGUAGES;
    const results = {
      missingLanguageRecords: 0,
      translationNeeded: 0
    };
    
    for (const lang of languages) {
      const tableName = `products_${lang}`;
      const [missingRecords] = await db.sequelize.query(
        `SELECT products.productId FROM products 
         LEFT JOIN ${tableName} ON products.productId = ${tableName}.productId
         WHERE products.status != 'deleted'
         AND ${tableName}.productId IS NULL`,
        { type: db.sequelize.QueryTypes.SELECT }
      );
      
      if (missingRecords && missingRecords.length > 0) {
        logTask(taskName, `发现 ${missingRecords.length} 条产品在 ${tableName} 中缺失`);
        results.missingLanguageRecords += missingRecords.length;
        
        // 标记这些产品需要翻译
        for (const record of missingRecords) {
          await Product.update(
            { test_all: false },
            { where: { productId: record.productId } }
          );
          results.translationNeeded++;
        }
      }
    }
    
    logTask(taskName, `执行完成，发现 ${results.missingLanguageRecords} 条缺失记录，标记 ${results.translationNeeded} 条产品需要翻译`);
    
    return { success: true, ...results };
  } catch (error) {
    logTask(taskName, `执行出错: ${error.message}`, true);
    return { success: false, error: error.message };
  }
};

// 初始化定时任务
const initScheduledTasks = () => {
  try {
    // 每天凌晨2点执行删除记录清理
    cron.schedule('0 2 * * *', async () => {
      await cleanupDeletedProductsTask();
    });
    
    // 每天凌晨3点执行产品同步检查
    cron.schedule('0 3 * * *', async () => {
      await checkProductSyncTask();
    });
    
    // 每2小时执行一次自动翻译
    cron.schedule('0 */2 * * *', async () => {
      await autoTranslateProductsTask();
    });
    
    logger.milestone('所有定时任务已初始化');
  } catch (error) {
    logger.simpleError('初始化定时任务失败', error);
  }
};

// 导出模块
module.exports = {
  initScheduledTasks,
  cleanupDeletedProductsTask,
  autoTranslateProductsTask,
  checkProductSyncTask
}; 