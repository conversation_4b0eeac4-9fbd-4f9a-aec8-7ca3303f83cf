globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/vehicles/[brand]/[title-productId]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/BrandNavigationClient.tsx":{"*":{"id":"(ssr)/./src/components/layout/BrandNavigationClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/HeaderClient.tsx":{"*":{"id":"(ssr)/./src/components/layout/HeaderClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/MobileNavigation.tsx":{"*":{"id":"(ssr)/./src/components/layout/MobileNavigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/SingleRenderVehicleList.tsx":{"*":{"id":"(ssr)/./src/components/vehicles/SingleRenderVehicleList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/VehicleFilters.tsx":{"*":{"id":"(ssr)/./src/components/vehicles/VehicleFilters.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/OptimizedPagination.tsx":{"*":{"id":"(ssr)/./src/components/OptimizedPagination.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/MobileFilterBar.tsx":{"*":{"id":"(ssr)/./src/components/vehicles/MobileFilterBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/ConfigModal.tsx":{"*":{"id":"(ssr)/./src/components/vehicles/ConfigModal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/GalleryWrapper.tsx":{"*":{"id":"(ssr)/./src/components/vehicles/GalleryWrapper.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\layout\\BrandNavigationClient.tsx":{"id":"(app-pages-browser)/./src/components/layout/BrandNavigationClient.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\layout\\HeaderClient.tsx":{"id":"(app-pages-browser)/./src/components/layout/HeaderClient.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\layout\\MobileNavigation.tsx":{"id":"(app-pages-browser)/./src/components/layout/MobileNavigation.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\home\\HotVehiclesSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\vehicles\\SingleRenderVehicleList.tsx":{"id":"(app-pages-browser)/./src/components/vehicles/SingleRenderVehicleList.tsx","name":"*","chunks":["app/[locale]/vehicles/page","static/chunks/app/%5Blocale%5D/vehicles/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\vehicles\\VehicleFilters.tsx":{"id":"(app-pages-browser)/./src/components/vehicles/VehicleFilters.tsx","name":"*","chunks":["app/[locale]/vehicles/[brand]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\OptimizedPagination.tsx":{"id":"(app-pages-browser)/./src/components/OptimizedPagination.tsx","name":"*","chunks":["app/[locale]/vehicles/[brand]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\vehicles\\MobileFilterBar.tsx":{"id":"(app-pages-browser)/./src/components/vehicles/MobileFilterBar.tsx","name":"*","chunks":["app/[locale]/vehicles/[brand]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\vehicles\\ConfigModal.tsx":{"id":"(app-pages-browser)/./src/components/vehicles/ConfigModal.tsx","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\components\\vehicles\\GalleryWrapper.tsx":{"id":"(app-pages-browser)/./src/components/vehicles/GalleryWrapper.tsx","name":"*","chunks":["app/[locale]/vehicles/[brand]/[title-productId]/page","static/chunks/app/%5Blocale%5D/vehicles/%5Bbrand%5D/%5Btitle-productId%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\[locale]\\layout":[{"inlined":false,"path":"static/css/app/[locale]/layout.css"}],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\[locale]\\page":[],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\[locale]\\vehicles\\page":[],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\[locale]\\vehicles\\[brand]\\page":[],"C:\\Users\\<USER>\\Desktop\\sgc\\sgc-frontend\\src\\app\\[locale]\\vehicles\\[brand]\\[title-productId]\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/BrandNavigationClient.tsx":{"*":{"id":"(rsc)/./src/components/layout/BrandNavigationClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/HeaderClient.tsx":{"*":{"id":"(rsc)/./src/components/layout/HeaderClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/MobileNavigation.tsx":{"*":{"id":"(rsc)/./src/components/layout/MobileNavigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/SingleRenderVehicleList.tsx":{"*":{"id":"(rsc)/./src/components/vehicles/SingleRenderVehicleList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/VehicleFilters.tsx":{"*":{"id":"(rsc)/./src/components/vehicles/VehicleFilters.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/OptimizedPagination.tsx":{"*":{"id":"(rsc)/./src/components/OptimizedPagination.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/MobileFilterBar.tsx":{"*":{"id":"(rsc)/./src/components/vehicles/MobileFilterBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/ConfigModal.tsx":{"*":{"id":"(rsc)/./src/components/vehicles/ConfigModal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/vehicles/GalleryWrapper.tsx":{"*":{"id":"(rsc)/./src/components/vehicles/GalleryWrapper.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}