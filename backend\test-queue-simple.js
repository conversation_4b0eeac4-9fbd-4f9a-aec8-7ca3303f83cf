/**
 * 简单测试队列大小
 */

require('dotenv').config();

async function testQueueSimple() {
    console.log('🧪 简单测试队列大小...\n');

    try {
        // 1. 直接测试翻译队列服务
        console.log('1. 测试翻译队列服务的统计方法...');
        const translationQueueService = require('./src/services/translationQueueService');
        
        const queueStats = translationQueueService.getStats();
        console.log('当前队列统计:', JSON.stringify(queueStats, null, 2));
        
        // 2. 检查关键字段
        console.log('\n2. 检查关键字段...');
        const hasCurrentQueueSize = queueStats.hasOwnProperty('currentQueueSize');
        const currentQueueSize = queueStats.currentQueueSize;
        
        console.log(`currentQueueSize字段存在: ${hasCurrentQueueSize}`);
        console.log(`currentQueueSize值: ${currentQueueSize}`);
        
        // 3. 模拟添加任务
        console.log('\n3. 模拟添加任务到队列...');
        
        try {
            const taskId = translationQueueService.addTask({
                entityType: 'product',
                entityId: 'test-product-' + Date.now(),
                languages: ['en'],
                operation: 'translateProduct'
            }, 'normal');
            
            console.log(`任务已添加: ${taskId}`);
            
            // 再次获取统计
            const newQueueStats = translationQueueService.getStats();
            console.log('添加任务后的队列统计:', JSON.stringify(newQueueStats, null, 2));
            
            const newCurrentQueueSize = newQueueStats.currentQueueSize;
            console.log(`新的队列大小: ${newCurrentQueueSize}`);
            
            // 4. 验证队列大小变化
            console.log('\n4. 验证队列大小变化...');
            if (newCurrentQueueSize > currentQueueSize) {
                console.log('✅ 队列大小正确增加');
                console.log(`从 ${currentQueueSize} 增加到 ${newCurrentQueueSize}`);
            } else {
                console.log('❌ 队列大小没有正确增加');
            }
            
        } catch (error) {
            console.log('添加任务失败:', error.message);
        }
        
        // 5. 等待任务处理
        console.log('\n5. 等待任务处理...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const finalQueueStats = translationQueueService.getStats();
        console.log('任务处理后的队列统计:', JSON.stringify(finalQueueStats, null, 2));
        
        const finalQueueSize = finalQueueStats.currentQueueSize;
        console.log(`最终队列大小: ${finalQueueSize}`);
        
        // 6. 最终验证
        console.log('\n🎯 最终验证结果:');
        console.log('==================');
        
        if (hasCurrentQueueSize) {
            console.log('✅ currentQueueSize字段存在');
            console.log('✅ 队列统计功能正常');
            console.log('✅ API应该能返回正确的队列大小');
            
            console.log('\n📊 队列大小变化过程:');
            console.log(`初始: ${currentQueueSize}`);
            console.log(`添加任务后: ${newQueueStats?.currentQueueSize || '未知'}`);
            console.log(`处理后: ${finalQueueSize}`);
            
            console.log('\n🎊 队列大小功能修复成功！');
            console.log('翻译监控页面现在应该能正确显示队列大小了。');
            
        } else {
            console.log('❌ currentQueueSize字段不存在');
            console.log('需要进一步检查队列服务实现');
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
testQueueSimple().catch(console.error);
