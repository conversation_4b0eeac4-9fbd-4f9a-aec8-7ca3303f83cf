/**
 * 测试手动触发翻译和上架翻译的流程是否一致
 */

require('dotenv').config();

async function testTranslationFlows() {
    console.log('🧪 测试翻译流程一致性...\n');

    try {
        // 1. 测试上架翻译流程
        console.log('1. 测试上架翻译流程...');
        const { handleProductOnShelf } = require('./src/services/autoTranslationService');
        
        // 模拟产品ID
        const testProductId = 'test-product-' + Date.now();
        
        console.log(`调用上架翻译: handleProductOnShelf('${testProductId}')`);
        console.log('参数: 无额外参数 (使用默认值)');
        
        // 这里只是测试函数调用，不实际执行翻译
        console.log('上架翻译函数可用:', typeof handleProductOnShelf === 'function');
        
        // 2. 测试手动触发翻译流程
        console.log('\n2. 测试手动触发翻译流程...');
        const newScheduledTasksService = require('./src/services/newScheduledTasksService');
        
        console.log('手动触发翻译函数可用:', typeof newScheduledTasksService.autoTranslateProductsTask === 'function');
        
        // 3. 检查代码一致性
        console.log('\n3. 检查代码一致性...');
        const fs = require('fs');
        const path = require('path');
        
        // 检查手动触发翻译的代码
        const newTasksPath = path.join(__dirname, 'src', 'services', 'newScheduledTasksService.js');
        const newTasksContent = fs.readFileSync(newTasksPath, 'utf8');
        
        // 检查上架翻译的代码
        const autoTransPath = path.join(__dirname, 'src', 'services', 'autoTranslationService.js');
        const autoTransContent = fs.readFileSync(autoTransPath, 'utf8');
        
        // 分析手动触发翻译的调用方式
        const handlesProductOnShelf = newTasksContent.includes('handleProductOnShelf');
        const usesDefaultParams = newTasksContent.includes('handleProductOnShelf(productId)');
        const hasExtraParams = newTasksContent.includes('handleProductOnShelf(productId, {');
        
        console.log('手动触发翻译代码分析:');
        console.log(`  - 使用 handleProductOnShelf: ${handlesProductOnShelf}`);
        console.log(`  - 使用默认参数调用: ${usesDefaultParams}`);
        console.log(`  - 传递额外参数: ${hasExtraParams}`);
        
        // 分析上架翻译的默认参数
        const defaultPriorityMatch = autoTransContent.match(/priority = '(\w+)'/);
        const defaultUseQueueMatch = autoTransContent.match(/useQueue = (\w+)/);
        const skipIfTranslatedMatch = autoTransContent.match(/skipIfTranslated: (\w+)/);
        
        console.log('\n上架翻译默认参数:');
        console.log(`  - 默认优先级: ${defaultPriorityMatch ? defaultPriorityMatch[1] : '未找到'}`);
        console.log(`  - 默认使用队列: ${defaultUseQueueMatch ? defaultUseQueueMatch[1] : '未找到'}`);
        console.log(`  - 跳过已翻译: ${skipIfTranslatedMatch ? skipIfTranslatedMatch[1] : '未找到'}`);
        
        // 4. 提取关键代码片段
        console.log('\n4. 关键代码片段对比:');
        
        // 手动触发翻译的调用
        const manualCallMatch = newTasksContent.match(/handleProductOnShelf\([^)]+\)[^;]*;/);
        if (manualCallMatch) {
            console.log('\n手动触发翻译的调用:');
            console.log('```javascript');
            console.log(manualCallMatch[0]);
            console.log('```');
        }
        
        // 上架翻译的默认参数设置
        const defaultParamsMatch = autoTransContent.match(/const \{ [^}]+ \} = options;[^}]+默认[^}]+/);
        if (defaultParamsMatch) {
            console.log('\n上架翻译的默认参数:');
            console.log('```javascript');
            console.log(defaultParamsMatch[0]);
            console.log('```');
        }
        
        // 5. 一致性验证
        console.log('\n🎯 一致性验证结果:');
        console.log('========================');
        
        if (handlesProductOnShelf && usesDefaultParams && !hasExtraParams) {
            console.log('✅ 手动触发翻译使用相同的函数调用');
            console.log('✅ 使用默认参数，不传递额外参数');
            console.log('✅ 应该和上架翻译行为完全一致');
            
            console.log('\n🔄 预期的统一流程:');
            console.log('1. 调用 handleProductOnShelf(productId)');
            console.log('2. 使用默认参数: priority="high", useQueue=true');
            console.log('3. 决策选项: skipIfTranslated=true');
            console.log('4. 智能调度器添加到翻译队列');
            console.log('5. 队列处理器执行翻译任务');
            console.log('6. 一辆车一个任务，并行翻译多种语言');
            
        } else {
            console.log('❌ 手动触发翻译和上架翻译不一致');
            
            if (!handlesProductOnShelf) {
                console.log('  - 未使用 handleProductOnShelf 函数');
            }
            if (!usesDefaultParams) {
                console.log('  - 未使用默认参数调用');
            }
            if (hasExtraParams) {
                console.log('  - 传递了额外参数，可能导致行为不一致');
            }
        }
        
        // 6. 实际测试建议
        console.log('\n💡 实际测试建议:');
        console.log('1. 在翻译监控页面点击"手动触发自动翻译"');
        console.log('2. 观察日志输出，应该看到:');
        console.log('   - [产品上架] 产品 xxx 上架，开始翻译处理');
        console.log('   - [智能调度] 产品 xxx 已添加到翻译队列 (high): task-xxx');
        console.log('   - [自动翻译] 产品 xxx 已加入翻译队列: task-xxx (优先级: high)');
        console.log('3. 确认每辆车只产生一个翻译任务');
        console.log('4. 确认使用高优先级队列处理');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
testTranslationFlows().catch(console.error);
