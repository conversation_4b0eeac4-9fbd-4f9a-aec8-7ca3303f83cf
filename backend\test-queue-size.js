/**
 * 测试队列大小API
 */

require('dotenv').config();

async function testQueueSizeAPI() {
    console.log('🧪 测试队列大小API...\n');

    try {
        // 1. 测试翻译队列服务的统计方法
        console.log('1. 测试翻译队列服务的统计方法...');
        const translationQueueService = require('./src/services/translationQueueService');
        
        const queueStats = translationQueueService.getStats();
        console.log('队列统计数据:', JSON.stringify(queueStats, null, 2));
        
        // 检查关键字段
        const hasCurrentQueueSize = queueStats.hasOwnProperty('currentQueueSize');
        const hasTotalQueueSize = queueStats.hasOwnProperty('totalQueueSize');
        const hasSize = queueStats.hasOwnProperty('size');
        
        console.log('\n字段检查:');
        console.log(`  - currentQueueSize: ${hasCurrentQueueSize} (值: ${queueStats.currentQueueSize})`);
        console.log(`  - totalQueueSize: ${hasTotalQueueSize} (值: ${queueStats.totalQueueSize})`);
        console.log(`  - size: ${hasSize} (值: ${queueStats.size})`);
        
        // 2. 模拟添加任务到队列
        console.log('\n2. 模拟添加任务到队列...');
        
        try {
            const taskId = translationQueueService.addTask({
                entityType: 'product',
                entityId: 'test-product-' + Date.now(),
                languages: ['en', 'ru'],
                operation: 'translateProduct'
            }, 'normal');
            
            console.log(`任务已添加: ${taskId}`);
            
            // 再次获取统计
            const newQueueStats = translationQueueService.getStats();
            console.log('添加任务后的队列统计:', JSON.stringify(newQueueStats, null, 2));
            
        } catch (error) {
            console.log('添加任务失败 (这是正常的，因为没有完整的数据库连接):', error.message);
        }
        
        // 3. 测试API响应格式
        console.log('\n3. 测试API响应格式...');
        
        // 模拟API响应
        const mockApiResponse = {
            success: true,
            data: {
                isRunning: true,
                queueStats: queueStats,
                healthStatus: null,
                uptime: process.uptime(),
                timestamp: new Date().toISOString()
            }
        };
        
        console.log('模拟API响应:', JSON.stringify(mockApiResponse, null, 2));
        
        // 4. 模拟前端处理逻辑
        console.log('\n4. 模拟前端处理逻辑...');
        
        function getQueueSizeFromResponse(responseData) {
            let queueSize = '0';
            if (responseData && responseData.queueStats) {
                // 尝试多种可能的字段名
                if (responseData.queueStats.currentQueueSize !== undefined) {
                    queueSize = responseData.queueStats.currentQueueSize.toString();
                } else if (responseData.queueStats.size !== undefined) {
                    queueSize = responseData.queueStats.size.toString();
                } else if (responseData.queueStats.totalQueueSize !== undefined) {
                    queueSize = responseData.queueStats.totalQueueSize.toString();
                }
            }
            return queueSize;
        }
        
        const frontendQueueSize = getQueueSizeFromResponse(mockApiResponse.data);
        console.log(`前端获取的队列大小: ${frontendQueueSize}`);
        
        // 5. 验证结果
        console.log('\n🎯 验证结果:');
        console.log('=============');
        
        if (hasCurrentQueueSize && frontendQueueSize !== '0') {
            console.log('✅ 队列大小API修复成功');
            console.log('✅ 前端应该能正确显示队列大小');
        } else if (hasCurrentQueueSize && frontendQueueSize === '0') {
            console.log('✅ 队列大小字段已添加');
            console.log('ℹ️ 当前队列为空，显示0是正确的');
        } else {
            console.log('❌ 队列大小API仍有问题');
            console.log('❌ 缺少必要的字段');
        }
        
        console.log('\n💡 测试建议:');
        console.log('1. 刷新翻译监控页面');
        console.log('2. 检查队列大小是否正确显示');
        console.log('3. 手动触发翻译，观察队列大小变化');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
testQueueSizeAPI().catch(console.error);
