{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5smByO1KjBO+7X4ZEgK5vV5x/7Zs7JO5jgHJv+Dx4P0="}}}, "functions": {}, "sortedMiddleware": ["/"]}