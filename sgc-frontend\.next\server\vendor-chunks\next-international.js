"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-international";
exports.ids = ["vendor-chunks/next-international"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-international/dist/app/server/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/next-international/dist/app/server/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/app/server/index.ts\nvar server_exports = {};\n__export(server_exports, {\n  createI18nServer: () => createI18nServer,\n  setStaticParamsLocale: () => setStaticParamsLocale\n});\nmodule.exports = __toCommonJS(server_exports);\nvar import_server_only = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n\n// src/app/server/get-locale-cache.tsx\nvar import_headers = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\nvar import_react = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n// src/common/constants.ts\nvar LOCALE_HEADER = \"X-Next-Locale\";\nvar LOCALE_COOKIE = \"Next-Locale\";\nvar DEFAULT_SEGMENT_NAME = \"locale\";\n\n// src/app/server/get-locale-cache.tsx\nvar import_navigation = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// src/helpers/log.ts\nfunction log(type, message) {\n  if (true) {\n    console[type](`[next-international] ${message}`);\n  }\n  return null;\n}\nvar error = (message) => log(\"error\", message);\n\n// src/app/server/get-locale-cache.tsx\nvar getLocale = (0, import_react.cache)(() => ({ current: void 0 }));\nvar getStaticParamsLocale = () => getLocale().current;\nvar setStaticParamsLocale = (value) => {\n  getLocale().current = value;\n};\nvar getLocaleCache = (0, import_react.cache)(() => __async(void 0, null, function* () {\n  var _a;\n  let locale;\n  locale = getStaticParamsLocale();\n  if (!locale) {\n    try {\n      locale = (yield (0, import_headers.headers)()).get(LOCALE_HEADER);\n      if (!locale) {\n        locale = (_a = (yield (0, import_headers.cookies)()).get(LOCALE_COOKIE)) == null ? void 0 : _a.value;\n      }\n    } catch (e) {\n      throw new Error(\n        \"Could not find locale while pre-rendering page, make sure you called `setStaticParamsLocale` at the top of your pages\"\n      );\n    }\n  }\n  if (!locale) {\n    error(`Locale not found in headers or cookies, returning \"notFound()\"`);\n    (0, import_navigation.notFound)();\n  }\n  return locale;\n}));\n\n// src/app/server/create-get-current-locale.ts\nfunction createGetCurrentLocale() {\n  return function getCurrentLocale() {\n    return getLocaleCache();\n  };\n}\n\n// src/common/create-t.ts\nvar import_react2 = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\nfunction createT(context, scope) {\n  const { localeContent, fallbackLocale } = context;\n  const content = fallbackLocale && typeof localeContent === \"string\" ? fallbackLocale : Object.assign(fallbackLocale != null ? fallbackLocale : {}, localeContent);\n  const pluralKeys = new Set(\n    Object.keys(content).filter((key) => key.includes(\"#\")).map((key) => key.split(\"#\", 1)[0])\n  );\n  const pluralRules = new Intl.PluralRules(context.locale);\n  function getPluralKey(count) {\n    if (count === 0)\n      return \"zero\";\n    return pluralRules.select(count);\n  }\n  function t(key, ...params) {\n    var _a, _b;\n    const paramObject = params[0];\n    let isPlural = false;\n    if (paramObject && \"count\" in paramObject) {\n      const isPluralKey = scope ? pluralKeys.has(`${scope}.${key}`) : pluralKeys.has(key);\n      if (isPluralKey) {\n        key = `${key}#${getPluralKey(paramObject.count)}`;\n        isPlural = true;\n      }\n    }\n    let value = scope ? content[`${scope}.${key}`] : content[key];\n    if (!value && isPlural) {\n      const baseKey = key.split(\"#\", 1)[0];\n      value = (_a = content[`${baseKey}#other`] || key) == null ? void 0 : _a.toString();\n    } else {\n      value = (_b = value || key) == null ? void 0 : _b.toString();\n    }\n    if (!paramObject) {\n      return value;\n    }\n    let isString = true;\n    const result = value == null ? void 0 : value.split(/({[^}]*})/).map((part, index) => {\n      const match = part.match(/{(.*)}/);\n      if (match) {\n        const param = match[1];\n        const paramValue = paramObject[param];\n        if ((0, import_react2.isValidElement)(paramValue)) {\n          isString = false;\n          return (0, import_react2.cloneElement)(paramValue, { key: `${String(param)}-${index}` });\n        }\n        return paramValue;\n      }\n      return part;\n    });\n    return isString ? result == null ? void 0 : result.join(\"\") : result;\n  }\n  return t;\n}\n\n// src/common/flatten-locale.ts\nvar flattenLocale = (locale, prefix = \"\") => Object.entries(locale).reduce(\n  (prev, [name, value]) => __spreadValues(__spreadValues({}, prev), typeof value === \"string\" ? { [prefix + name]: value } : flattenLocale(value, `${prefix}${name}.`)),\n  {}\n);\n\n// src/app/server/create-get-i18n.ts\nfunction createGetI18n(locales, config) {\n  const localeCache = /* @__PURE__ */ new Map();\n  return function getI18n() {\n    return __async(this, null, function* () {\n      const locale = yield getLocaleCache();\n      const cached = localeCache.get(locale);\n      if (cached) {\n        return yield cached;\n      }\n      const localeFnPromise = (() => __async(this, null, function* () {\n        const localeModule = yield locales[locale]();\n        return createT(\n          {\n            localeContent: flattenLocale(localeModule.default),\n            fallbackLocale: config.fallbackLocale ? flattenLocale(config.fallbackLocale) : void 0,\n            locale\n          },\n          void 0\n        );\n      }))();\n      localeCache.set(locale, localeFnPromise);\n      return yield localeFnPromise;\n    });\n  };\n}\n\n// src/app/server/create-get-scoped-i18n.ts\nfunction createGetScopedI18n(locales, config) {\n  const localeCache = /* @__PURE__ */ new Map();\n  return function getScopedI18n(scope) {\n    return __async(this, null, function* () {\n      const locale = yield getLocaleCache();\n      const cacheKey = `${locale}-${scope}`;\n      const cached = localeCache.get(cacheKey);\n      if (cached) {\n        return yield cached;\n      }\n      const localeFnPromise = (() => __async(this, null, function* () {\n        const localeModule = yield locales[locale]();\n        return createT(\n          {\n            localeContent: flattenLocale(localeModule.default),\n            fallbackLocale: config.fallbackLocale ? flattenLocale(config.fallbackLocale) : void 0,\n            locale\n          },\n          scope\n        );\n      }))();\n      localeCache.set(cacheKey, localeFnPromise);\n      return yield localeFnPromise;\n    });\n  };\n}\n\n// src/app/server/create-get-static-params.ts\nfunction createGetStaticParams(locales, config) {\n  return function getStaticParams() {\n    return Object.keys(locales).map((locale) => {\n      var _a;\n      return {\n        [(_a = config.segmentName) != null ? _a : DEFAULT_SEGMENT_NAME]: locale\n      };\n    });\n  };\n}\n\n// src/app/server/index.ts\nfunction createI18nServer(locales, config = {}) {\n  const getI18n = createGetI18n(locales, config);\n  const getScopedI18n = createGetScopedI18n(locales, config);\n  const getCurrentLocale = createGetCurrentLocale();\n  const getStaticParams = createGetStaticParams(locales, config);\n  return {\n    getI18n,\n    getScopedI18n,\n    getCurrentLocale,\n    getStaticParams\n  };\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-international/dist/app/server/index.js\n");

/***/ })

};
;