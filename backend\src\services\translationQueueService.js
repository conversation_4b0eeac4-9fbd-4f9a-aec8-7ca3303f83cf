const EventEmitter = require('events');
const TranslationStatusService = require('./translationStatusService');
const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILE_PATH = path.join(__dirname, '../../config/translation-queue-config.json');

/**
 * 读取翻译队列配置
 */
function loadQueueConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = fs.readFileSync(CONFIG_FILE_PATH, 'utf8');
      return JSON.parse(configData);
    }
  } catch (error) {
    console.error('读取翻译队列配置文件失败:', error);
  }

  // 返回默认配置
  return {
    maxConcurrency: 5,
    maxRetries: 3,
    retryDelay: 5000,
    batchSize: 10,
    processingInterval: 1000,
    cleanupInterval: 300000
  };
}

/**
 * 保存翻译队列配置
 */
function saveQueueConfig(config) {
  try {
    const configToSave = {
      ...config,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(CONFIG_FILE_PATH, JSON.stringify(configToSave, null, 2), 'utf8');
    console.log('[翻译队列] 配置已保存到文件');
  } catch (error) {
    console.error('保存翻译队列配置文件失败:', error);
  }
}

/**
 * 翻译队列管理服务
 * 支持优先级、批量处理、失败重试和并发控制
 */
class TranslationQueueService extends EventEmitter {
  constructor() {
    super();

    // 队列存储
    this.queues = {
      high: [],     // 高优先级队列
      normal: [],   // 普通优先级队列
      low: []       // 低优先级队列
    };

    // 处理中的任务
    this.processing = new Map();

    // 失败重试队列
    this.retryQueue = [];

    // 处理器状态
    this.paused = false;
    this.processorInterval = null;

    // 从配置文件加载配置
    const savedConfig = loadQueueConfig();

    // 配置
    this.config = {
      maxConcurrency: process.env.TRANSLATION_MAX_CONCURRENCY !== undefined
        ? parseInt(process.env.TRANSLATION_MAX_CONCURRENCY)
        : (savedConfig.maxConcurrency !== undefined ? savedConfig.maxConcurrency : 5),
      maxRetries: savedConfig.maxRetries || 3,
      retryDelay: savedConfig.retryDelay || 5000,
      batchSize: savedConfig.batchSize || 10,
      processingInterval: savedConfig.processingInterval || 1000,
      cleanupInterval: savedConfig.cleanupInterval || 300000
    };

    // 统计信息
    this.stats = {
      totalQueued: 0,
      totalProcessed: 0,
      totalSucceeded: 0,
      totalFailed: 0,
      currentProcessing: 0
    };

    console.log(`[翻译队列] 初始化翻译队列服务，最大并发: ${this.config.maxConcurrency}`);

    // 启动处理器
    this.startProcessor();
    this.startCleanup();
  }

  /**
   * 添加翻译任务到队列
   * @param {Object} task - 翻译任务
   * @param {string} priority - 优先级 (high/normal/low)
   * @returns {string} - 任务ID
   */
  addTask(task, priority = 'normal') {
    const taskId = this.generateTaskId();
    const queueTask = {
      id: taskId,
      ...task,
      priority,
      addedAt: new Date(),
      retryCount: 0,
      status: 'queued'
    };
    
    // 验证任务
    if (!this.validateTask(queueTask)) {
      throw new Error('无效的翻译任务');
    }
    
    // 添加到对应优先级队列
    this.queues[priority].push(queueTask);
    this.stats.totalQueued++;
    
    console.log(`[翻译队列] 添加任务: ${taskId} (优先级: ${priority})`);
    this.emit('taskAdded', queueTask);
    
    return taskId;
  }

  /**
   * 批量添加翻译任务
   * @param {Array} tasks - 任务列表
   * @param {string} priority - 优先级
   * @returns {Array} - 任务ID列表
   */
  addBatchTasks(tasks, priority = 'normal') {
    const taskIds = [];
    
    for (const task of tasks) {
      try {
        const taskId = this.addTask(task, priority);
        taskIds.push(taskId);
      } catch (error) {
        console.error(`[翻译队列] 批量添加任务失败:`, error);
      }
    }
    
    console.log(`[翻译队列] 批量添加 ${taskIds.length} 个任务`);
    return taskIds;
  }

  /**
   * 获取下一个要处理的任务
   * @returns {Object|null} - 任务对象或null
   */
  getNextTask() {
    // 按优先级顺序获取任务
    for (const priority of ['high', 'normal', 'low']) {
      if (this.queues[priority].length > 0) {
        return this.queues[priority].shift();
      }
    }
    
    // 检查重试队列
    const now = Date.now();
    const retryTask = this.retryQueue.find(task => 
      task.retryAt && task.retryAt <= now
    );
    
    if (retryTask) {
      const index = this.retryQueue.indexOf(retryTask);
      this.retryQueue.splice(index, 1);
      return retryTask;
    }
    
    return null;
  }

  /**
   * 处理翻译任务
   * @param {Object} task - 任务对象
   * @returns {Promise<Object>} - 处理结果
   */
  async processTask(task) {
    const { id, entityType, entityId, languages, operation } = task;
    
    console.log(`[翻译队列] 开始处理任务: ${id}`);
    
    try {
      // 更新任务状态
      task.status = 'processing';
      task.startedAt = new Date();
      this.processing.set(id, task);
      this.stats.currentProcessing++;
      
      // 初始化翻译状态
      await TranslationStatusService.initializeTranslationStatus(entityType, entityId, languages);
      
      // 执行翻译操作
      let result;
      switch (operation) {
        case 'translateProduct':
          // 传递变更字段信息和决策选项
          result = await this.executeProductTranslation(
            entityId,
            languages,
            task.changedFields,
            task.decision || {}
          );
          break;
        case 'translateConfigCar':
          result = await this.executeConfigCarTranslation(entityId, languages);
          break;
        case 'retranslate':
          result = await this.executeRetranslation(entityType, entityId, languages);
          break;
        default:
          throw new Error(`不支持的操作: ${operation}`);
      }
      
      // 更新任务状态
      task.status = 'completed';
      task.completedAt = new Date();
      task.result = result;
      
      this.stats.totalSucceeded++;
      console.log(`[翻译队列] 任务完成: ${id}`);
      this.emit('taskCompleted', task);
      
      return { success: true, result };
      
    } catch (error) {
      console.error(`[翻译队列] 任务失败: ${id}`, error);
      
      // 更新任务状态
      task.status = 'failed';
      task.error = error.message;
      task.failedAt = new Date();
      
      // 检查是否需要重试
      if (task.retryCount < this.config.maxRetries) {
        await this.scheduleRetry(task);
      } else {
        this.stats.totalFailed++;
        this.emit('taskFailed', task);
      }
      
      return { success: false, error: error.message };
      
    } finally {
      // 清理处理中的任务
      this.processing.delete(id);
      this.stats.currentProcessing--;
      this.stats.totalProcessed++;
    }
  }

  /**
   * 安排任务重试
   * @param {Object} task - 任务对象
   */
  async scheduleRetry(task) {
    task.retryCount++;
    task.retryAt = Date.now() + (this.config.retryDelay * Math.pow(2, task.retryCount - 1));
    task.status = 'retry_scheduled';
    
    this.retryQueue.push(task);
    
    console.log(`[翻译队列] 安排重试: ${task.id} (第${task.retryCount}次重试)`);
    this.emit('taskRetryScheduled', task);
  }

  /**
   * 执行产品翻译
   * @param {string} productId - 产品ID
   * @param {Array} languages - 语言列表
   * @param {Object} changedFields - 变更的字段（可选）
   * @param {Object} decision - 决策选项（可选）
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeProductTranslation(productId, languages, changedFields = null, decision = {}) {
    // 直接使用旧翻译系统的产品翻译函数，保持统一的翻译逻辑
    const { translateProductSelective } = require('./autoTranslationService');

    // 构建翻译选项，包含决策选项
    const translationOptions = {
      enableLanguageParallel: true,
      ...decision  // 传递所有决策选项，包括forceTranslation等
    };

    // 如果有变更字段信息，传递给翻译函数
    if (changedFields && Object.keys(changedFields).length > 0) {
      translationOptions.changedFields = changedFields;
      console.log(`[翻译队列] 产品 ${productId} 将只翻译变更的字段: ${Object.keys(changedFields).join(', ')}`);
    }

    const result = await translateProductSelective(productId, translationOptions);

    console.log(`[翻译队列] 产品翻译结果: success=${result.success}, testAllStatus=${result.testAllStatus} (类型: ${typeof result.testAllStatus})`);

    // 检查是否为产品修改翻译
    const isProductUpdate = changedFields && Object.keys(changedFields).length > 0;

    // 只有产品上架翻译且成功且test_all为true时，才触发配置翻译
    if (!isProductUpdate && result.success && result.testAllStatus === true) {
      try {
        const { Product } = require('../models');
        const product = await Product.findOne({
          where: { productId },
          attributes: ['productId', 'modelId', 'test_all']
        });

        if (product && product.modelId && (product.test_all === true || product.test_all === 1)) {
          console.log(`[翻译队列] 产品 ${productId} test_all=true，自动触发配置翻译 modelId=${product.modelId}`);

          // 添加配置翻译任务到队列
          const configTaskId = this.addTask({
            entityType: 'configCar',
            entityId: product.modelId,
            languages: ['en', 'ru', 'es', 'fr'],
            operation: 'translateConfigCar'
          }, 'normal');

          console.log(`[翻译队列] 配置翻译任务已添加: ${configTaskId}`);

          // 在结果中添加配置翻译信息
          result.configTranslationScheduled = true;
          result.configTaskId = configTaskId;
        }
      } catch (error) {
        console.error(`[翻译队列] 检查配置翻译时出错:`, error);
        // 不影响主要的产品翻译结果
      }
    } else if (isProductUpdate) {
      console.log(`[翻译队列] 产品修改翻译完成，跳过配置翻译触发`);
    }

    return result;
  }

  /**
   * 执行配置车翻译
   * @param {number} modelId - 车型ID
   * @param {Array} languages - 语言列表
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeConfigCarTranslation(modelId, languages) {
    const { translateConfigCarSelective } = require('./autoTranslationService');
    return await translateConfigCarSelective(modelId);
  }

  /**
   * 执行重新翻译
   * @param {string} entityType - 实体类型
   * @param {string|number} entityId - 实体ID
   * @param {Array} languages - 语言列表
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeRetranslation(entityType, entityId, languages) {
    // 重置翻译状态
    await TranslationStatusService.resetTranslationStatus(entityType, entityId, languages);
    
    if (entityType === 'product') {
      return await this.executeProductTranslation(entityId, languages);
    } else if (entityType === 'configCar') {
      return await this.executeConfigCarTranslation(entityId, languages);
    } else {
      throw new Error(`不支持的实体类型: ${entityType}`);
    }
  }

  /**
   * 启动任务处理器
   */
  startProcessor() {
    console.log(`[翻译队列] 启动任务处理器，处理间隔: ${this.config.processingInterval}ms`);

    this.processorInterval = setInterval(async () => {
      try {
        // 检查是否暂停
        if (this.paused) {
          return;
        }

        // 添加调试日志
        const totalTasks = this.queues.high.length + this.queues.normal.length + this.queues.low.length;
        if (totalTasks > 0) {
          console.log(`[翻译队列] 检查队列: 高优先级=${this.queues.high.length}, 普通=${this.queues.normal.length}, 低优先级=${this.queues.low.length}, 正在处理=${this.stats.currentProcessing}/${this.config.maxConcurrency}`);
        }

        // 检查是否达到最大并发数
        if (this.stats.currentProcessing >= this.config.maxConcurrency) {
          if (totalTasks > 0) {
            console.log(`[翻译队列] 已达到最大并发数 ${this.config.maxConcurrency}，等待处理完成`);
          }
          return;
        }

        // 获取下一个任务
        const task = this.getNextTask();
        if (!task) {
          return;
        }

        console.log(`[翻译队列] 获取到任务: ${task.id}, 类型: ${task.operation}, 实体: ${task.entityId}`);

        // 异步处理任务
        this.processTask(task).catch(error => {
          console.error('[翻译队列] 处理任务时出错:', error);
        });

      } catch (error) {
        console.error('[翻译队列] 处理器运行时出错:', error);
      }

    }, this.config.processingInterval);
  }

  /**
   * 启动清理器
   */
  startCleanup() {
    setInterval(() => {
      this.cleanupCompletedTasks();
      this.cleanupExpiredRetries();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompletedTasks() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
    
    for (const [taskId, task] of this.processing.entries()) {
      if (task.completedAt && task.completedAt.getTime() < cutoffTime) {
        this.processing.delete(taskId);
      }
    }
  }

  /**
   * 清理过期的重试任务
   */
  cleanupExpiredRetries() {
    const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
    
    this.retryQueue = this.retryQueue.filter(task => 
      !task.failedAt || task.failedAt.getTime() > cutoffTime
    );
  }

  /**
   * 验证任务
   * @param {Object} task - 任务对象
   * @returns {boolean} - 是否有效
   */
  validateTask(task) {
    const required = ['entityType', 'entityId', 'languages', 'operation'];
    return required.every(field => task[field] !== undefined);
  }

  /**
   * 生成任务ID
   * @returns {string} - 任务ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取队列状态
   * @returns {Object} - 队列状态
   */
  getQueueStatus() {
    return {
      queues: {
        high: this.queues.high.length,
        normal: this.queues.normal.length,
        low: this.queues.low.length
      },
      retryQueue: this.retryQueue.length,
      processing: this.stats.currentProcessing,
      stats: { ...this.stats }
    };
  }

  /**
   * 暂停队列处理
   */
  pause() {
    this.paused = true;
    console.log('[翻译队列] 队列已暂停');
  }

  /**
   * 恢复队列处理
   */
  resume() {
    this.paused = false;
    console.log('[翻译队列] 队列已恢复');
  }



  /**
   * 停止队列处理器
   */
  stopProcessor() {
    if (this.processorInterval) {
      clearInterval(this.processorInterval);
      this.processorInterval = null;
      console.log('[翻译队列] 队列处理器已停止');
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} - 统计信息
   */
  getStats() {
    const totalQueueSize = this.queues.high.length + this.queues.normal.length + this.queues.low.length;

    return {
      ...this.stats,
      queues: {
        high: this.queues.high.length,
        normal: this.queues.normal.length,
        low: this.queues.low.length
      },
      retryQueue: this.retryQueue.length,
      processing: this.processing.size,
      // 添加前端期望的字段
      currentQueueSize: totalQueueSize,
      totalQueueSize: totalQueueSize,
      size: totalQueueSize
    };
  }

  /**
   * 清空队列
   * @param {string} priority - 优先级，如果不指定则清空所有队列
   */
  clearQueue(priority = null) {
    if (priority) {
      this.queues[priority] = [];
      console.log(`[翻译队列] 清空${priority}优先级队列`);
    } else {
      this.queues.high = [];
      this.queues.normal = [];
      this.queues.low = [];
      this.retryQueue = [];
      console.log('[翻译队列] 清空所有队列');
    }
  }

  /**
   * 获取当前最大并发数
   * @returns {number} - 最大并发数
   */
  getMaxConcurrency() {
    return this.config.maxConcurrency;
  }

  /**
   * 设置最大并发数
   * @param {number} concurrency - 新的最大并发数
   */
  setMaxConcurrency(concurrency) {
    const oldConcurrency = this.config.maxConcurrency;
    this.config.maxConcurrency = parseInt(concurrency);

    console.log(`[翻译队列] 最大并发数从 ${oldConcurrency} 更新为 ${this.config.maxConcurrency}`);

    // 保存配置到文件
    saveQueueConfig(this.config);

    // 如果设置为0，暂停队列处理；否则恢复队列处理
    if (this.config.maxConcurrency === 0) {
      this.pause();
    } else if (this.paused && this.config.maxConcurrency > 0) {
      this.resume();
    }

    this.emit('concurrencyChanged', {
      oldConcurrency,
      newConcurrency: this.config.maxConcurrency
    });
  }
}

// 创建单例实例
const translationQueueService = new TranslationQueueService();

module.exports = translationQueueService;
