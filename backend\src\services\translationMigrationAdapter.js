/**
 * 翻译系统迁移适配器
 * 提供旧翻译系统API的兼容层，内部调用新翻译系统
 */

const translationManager = require('./core/TranslationManager');
const { getSupportedLanguages } = require('../config/translationConfig');

// 从旧系统导入常量和工具函数
const oldTranslationService = require('./translationService');

/**
 * 兼容性适配器类
 */
class TranslationMigrationAdapter {
  constructor() {
    // 移除环境变量选择，统一使用旧翻译系统
  }

  /**
   * 翻译单个文本 - 统一使用旧翻译系统
   */
  async translate(text, from = 'zh', to = 'en') {
    // 直接使用旧翻译系统，它已经有完整的降级机制
    try {
      const result = await oldTranslationService.translate(text, from, to);

      // 返回统一格式
      return {
        success: result.success,
        translatedText: result.translatedText,
        originalText: text,
        to,
        engine: result.engine
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        translatedText: `[${to.toUpperCase()}] ${text}`
      };
    }
  }

  /**
   * 多语言翻译 - 统一使用旧翻译系统
   */
  async translateToMultipleLanguages(text, targetLanguages = null, fromLanguage = 'zh') {
    // 直接使用旧翻译系统的多语言翻译
    try {
      return await oldTranslationService.translateToMultipleLanguages(
        text,
        targetLanguages,
        fromLanguage
      );
    } catch (error) {
      return {
        success: false,
        error: error.message,
        translations: {}
      };
    }
  }

  /**
   * 批量处理产品翻译 - 统一使用旧翻译系统
   */
  async batchProcessProductTranslations(productIds, options = {}) {
    // 直接使用旧翻译系统的批量处理
    try {
      return await oldTranslationService.batchProcessProductTranslations(productIds, options);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        processed: 0,
        successful: 0,
        failed: productIds.length
      };
    }
  }

  /**
   * 批量处理配置车翻译 - 统一使用旧翻译系统
   */
  async batchProcessConfigCarTranslations(modelIds, options = {}) {
    // 直接使用旧翻译系统的批量处理
    try {
      return await oldTranslationService.batchProcessConfigCarTranslations(modelIds, options);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        processed: 0,
        successful: 0,
        failed: modelIds.length
      };
    }
  }

  /**
   * 获取翻译状态 - 使用新系统的状态查询
   */
  async getTranslationStatus(entityType, entityId) {
    try {
      return await translationManager.getTranslationStatus(entityType, entityId);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        statusData: {
          status: 'unknown',
          message: '状态查询失败'
        }
      };
    }
  }

  /**
   * 检查语言是否支持 - 统一使用旧系统
   */
  isSupportedLanguage(language) {
    return oldTranslationService.isSupportedLanguage(language);
  }

  /**
   * 获取支持的语言列表 - 统一使用旧系统
   */
  getSupportedLanguages() {
    return Object.keys(oldTranslationService.LANGUAGES);
  }

  /**
   * 切换翻译服务 - 统一使用旧系统
   */
  switchTranslationService(service) {
    return oldTranslationService.switchTranslationService(service);
  }

  /**
   * 获取当前翻译服务 - 统一使用旧系统
   */
  getCurrentService() {
    return oldTranslationService.getCurrentService();
  }

  /**
   * 获取系统统计信息 - 使用新系统的统计功能
   */
  getSystemStats() {
    try {
      return translationManager.getStats();
    } catch (error) {
      return {
        message: '统计信息获取失败',
        error: error.message,
        currentService: this.getCurrentService()
      };
    }
  }

  /**
   * 获取健康状态 - 使用新系统的健康检查
   */
  getHealthStatus() {
    try {
      return translationManager.getHealthStatus();
    } catch (error) {
      return {
        status: 'error',
        message: '健康检查失败',
        error: error.message
      };
    }
  }

  // 代理其他旧系统方法
  async saveTranslation(...args) {
    return await oldTranslationService.saveTranslation(...args);
  }

  async getCarTranslations(...args) {
    return await oldTranslationService.getCarTranslations(...args);
  }

  async getCarMultiLanguageTranslations(...args) {
    return await oldTranslationService.getCarMultiLanguageTranslations(...args);
  }

  async updateCarTranslations(...args) {
    return await oldTranslationService.updateCarTranslations(...args);
  }

  async updateCarMultiLanguageTranslations(...args) {
    return await oldTranslationService.updateCarMultiLanguageTranslations(...args);
  }

  getProductTranslatableFields(...args) {
    return oldTranslationService.getProductTranslatableFields(...args);
  }

  getConfigCarTranslatableFields(...args) {
    return oldTranslationService.getConfigCarTranslatableFields(...args);
  }

  async upsertProductTranslation(...args) {
    return await oldTranslationService.upsertProductTranslation(...args);
  }

  async upsertConfigCarTranslation(...args) {
    return await oldTranslationService.upsertConfigCarTranslation(...args);
  }

  checkAllFieldsNotEmpty(...args) {
    return oldTranslationService.checkAllFieldsNotEmpty(...args);
  }

  containsChinese(...args) {
    return oldTranslationService.containsChinese(...args);
  }
}

// 创建单例实例
const adapter = new TranslationMigrationAdapter();

// 导出兼容的API
module.exports = {
  // 基础翻译功能
  LANGUAGES: oldTranslationService.LANGUAGES,
  isSupportedLanguage: adapter.isSupportedLanguage.bind(adapter),
  translate: adapter.translate.bind(adapter),
  translateToMultipleLanguages: adapter.translateToMultipleLanguages.bind(adapter),
  switchTranslationService: adapter.switchTranslationService.bind(adapter),
  getCurrentService: adapter.getCurrentService.bind(adapter),
  
  // 批量处理功能
  batchProcessProductTranslations: adapter.batchProcessProductTranslations.bind(adapter),
  batchProcessConfigCarTranslations: adapter.batchProcessConfigCarTranslations.bind(adapter),
  
  // 新系统功能
  getTranslationStatus: adapter.getTranslationStatus.bind(adapter),
  getSystemStats: adapter.getSystemStats.bind(adapter),
  getHealthStatus: adapter.getHealthStatus.bind(adapter),
  
  // 代理的旧系统方法
  saveTranslation: adapter.saveTranslation.bind(adapter),
  getCarTranslations: adapter.getCarTranslations.bind(adapter),
  getCarMultiLanguageTranslations: adapter.getCarMultiLanguageTranslations.bind(adapter),
  updateCarTranslations: adapter.updateCarTranslations.bind(adapter),
  updateCarMultiLanguageTranslations: adapter.updateCarMultiLanguageTranslations.bind(adapter),
  getProductTranslatableFields: adapter.getProductTranslatableFields.bind(adapter),
  getConfigCarTranslatableFields: adapter.getConfigCarTranslatableFields.bind(adapter),
  upsertProductTranslation: adapter.upsertProductTranslation.bind(adapter),
  upsertConfigCarTranslation: adapter.upsertConfigCarTranslation.bind(adapter),
  checkAllFieldsNotEmpty: adapter.checkAllFieldsNotEmpty.bind(adapter),
  containsChinese: adapter.containsChinese.bind(adapter),
  
  // 适配器实例
  adapter
};
