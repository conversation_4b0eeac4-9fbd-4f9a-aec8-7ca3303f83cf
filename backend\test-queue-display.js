/**
 * 测试队列显示是否正常
 */

require('dotenv').config();
const axios = require('axios');

async function testQueueDisplay() {
    console.log('🧪 测试队列显示是否正常...\n');

    const baseURL = 'http://localhost:3010';
    
    try {
        // 1. 先登录获取token
        console.log('1. 登录获取认证token...');
        const loginResponse = await axios.post(`${baseURL}/api/v1/admin/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        const token = loginResponse.data.token;
        console.log('登录成功，获取到token');
        
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 测试队列状态API
        console.log('\n2. 测试队列状态API...');
        const statusResponse = await axios.get(`${baseURL}/api/v1/translation-monitor/status`, { headers });
        
        console.log('API响应状态:', statusResponse.status);
        console.log('API响应数据:', JSON.stringify(statusResponse.data, null, 2));
        
        const queueStats = statusResponse.data.data.queueStats;
        console.log('\n队列统计详情:', JSON.stringify(queueStats, null, 2));
        
        // 3. 验证队列大小字段
        console.log('\n3. 验证队列大小字段...');
        const hasCurrentQueueSize = queueStats && queueStats.hasOwnProperty('currentQueueSize');
        const hasTotalQueueSize = queueStats && queueStats.hasOwnProperty('totalQueueSize');
        const hasSize = queueStats && queueStats.hasOwnProperty('size');
        
        console.log('字段检查:');
        console.log(`  - currentQueueSize: ${hasCurrentQueueSize} (值: ${queueStats?.currentQueueSize})`);
        console.log(`  - totalQueueSize: ${hasTotalQueueSize} (值: ${queueStats?.totalQueueSize})`);
        console.log(`  - size: ${hasSize} (值: ${queueStats?.size})`);
        
        // 4. 模拟前端获取队列大小的逻辑
        console.log('\n4. 模拟前端获取队列大小...');
        
        function getQueueSizeFromAPI(statusData) {
            let queueSize = '0';
            if (statusData && statusData.queueStats) {
                // 尝试多种可能的字段名
                if (statusData.queueStats.currentQueueSize !== undefined) {
                    queueSize = statusData.queueStats.currentQueueSize.toString();
                } else if (statusData.queueStats.size !== undefined) {
                    queueSize = statusData.queueStats.size.toString();
                } else if (statusData.queueStats.totalQueueSize !== undefined) {
                    queueSize = statusData.queueStats.totalQueueSize.toString();
                }
            }
            return queueSize;
        }
        
        const frontendQueueSize = getQueueSizeFromAPI(statusResponse.data.data);
        console.log(`前端显示的队列大小: ${frontendQueueSize}`);
        
        // 5. 手动触发翻译，观察队列变化
        console.log('\n5. 手动触发翻译，观察队列变化...');
        
        console.log('触发自动翻译...');
        const triggerResponse = await axios.post(`${baseURL}/api/v1/translation-monitor/trigger-auto-translation`, {}, { headers });
        
        console.log('触发翻译响应:', triggerResponse.data);
        
        // 等待一下，然后再次检查队列状态
        console.log('\n等待2秒后检查队列状态...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const statusAfterTrigger = await axios.get(`${baseURL}/api/v1/translation-monitor/status`, { headers });
        const queueStatsAfter = statusAfterTrigger.data.data.queueStats;
        
        console.log('触发翻译后的队列统计:', JSON.stringify(queueStatsAfter, null, 2));
        
        const queueSizeAfter = getQueueSizeFromAPI(statusAfterTrigger.data.data);
        console.log(`触发翻译后的队列大小: ${queueSizeAfter}`);
        
        // 6. 最终验证
        console.log('\n🎯 最终验证结果:');
        console.log('==================');
        
        if (hasCurrentQueueSize && hasSize) {
            console.log('✅ 队列大小字段修复成功');
            console.log('✅ API返回正确的队列统计数据');
            console.log('✅ 前端应该能正确显示队列大小');
            
            if (frontendQueueSize !== '0' || queueSizeAfter !== '0') {
                console.log('✅ 队列大小变化正常');
            } else {
                console.log('ℹ️ 当前没有待翻译的产品，队列为空是正常的');
            }
            
            console.log('\n🎊 队列显示修复完成！');
            console.log('现在翻译监控页面应该能正确显示队列大小了。');
            
        } else {
            console.log('❌ 队列大小字段仍有问题');
            console.log('需要进一步检查API实现');
        }
        
        // 7. 提供使用建议
        console.log('\n💡 使用建议:');
        console.log('1. 刷新翻译监控页面');
        console.log('2. 队列大小应该显示实际数值而不是0');
        console.log('3. 手动触发翻译时，队列大小应该会临时增加');
        console.log('4. 翻译完成后，队列大小应该回到0');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        
        if (error.response) {
            console.error('API错误响应:', error.response.status, error.response.data);
        }
    }
}

// 运行测试
testQueueDisplay().catch(console.error);
