/**
 * 测试修复后的翻译系统
 */

require('dotenv').config();

async function testFixedTranslation() {
    console.log('🧪 测试修复后的翻译系统...\n');

    try {
        // 1. 测试旧翻译系统
        console.log('1. 测试旧翻译系统...');
        const translationService = require('./src/services/translationService');
        const oldResult = await translationService.translate('测试文本', 'zh', 'en', { productId: 'test' });
        console.log('旧系统结果:', {
            success: oldResult.success,
            engine: oldResult.engine,
            translatedText: oldResult.translatedText
        });

        // 2. 测试核心翻译引擎
        console.log('\n2. 测试核心翻译引擎...');
        const TranslationEngines = require('./src/services/core/TranslationEngines');
        const logger = { warn: console.warn, error: console.error, info: console.info, debug: () => {} };
        const engines = new TranslationEngines(logger);
        
        const coreResult = await engines.executeTranslation('baidu', '测试文本', 'zh', 'en');
        console.log('核心引擎结果:', {
            success: coreResult.success,
            engine: coreResult.engine,
            translatedText: coreResult.translatedText
        });

        // 3. 测试核心翻译引擎的translate方法
        console.log('\n3. 测试核心翻译引擎的translate方法...');
        const coreTranslateResult = await engines.translate('测试文本', 'zh', 'en', { productId: 'debug-test' });
        console.log('核心translate方法结果:', {
            success: coreTranslateResult.success,
            engine: coreTranslateResult.engine,
            translatedText: coreTranslateResult.translatedText
        });

        // 4. 测试新翻译系统的批量处理
        console.log('\n4. 测试新翻译系统的批量处理...');
        const translationManager = require('./src/services/core/TranslationManager');
        
        // 确保翻译管理器已启动
        if (!translationManager.isRunning) {
            await translationManager.start();
        }

        // 模拟一个产品ID进行测试
        const testProductId = 'test-product-123';
        const batchResult = await translationManager.handleProductListing(testProductId, {
            useQueue: false,  // 直接执行，不使用队列
            forceTranslation: true
        });
        
        console.log('批量处理结果:', {
            success: batchResult.success,
            action: batchResult.action,
            error: batchResult.error
        });

        // 5. 总结
        console.log('\n📊 修复验证结果:');
        console.log('==================');
        
        if (oldResult.success && coreResult.success && coreTranslateResult.success) {
            console.log('🎉 修复成功！');
            console.log('✅ 旧翻译系统正常');
            console.log('✅ 核心翻译引擎正常');
            console.log('✅ 新旧系统已完全合并');
            console.log(`✅ 统一使用引擎: ${oldResult.engine}`);
            
            console.log('\n🔧 修复的问题:');
            console.log('1. executeTranslation方法现在正确调用旧翻译系统');
            console.log('2. 返回格式已统一');
            console.log('3. 新翻译系统的队列处理现在使用旧系统的引擎逻辑');
            
        } else {
            console.log('⚠️ 仍有问题需要解决');
            
            if (!oldResult.success) {
                console.log(`❌ 旧翻译系统失败: ${oldResult.error}`);
            }
            if (!coreResult.success) {
                console.log(`❌ 核心翻译引擎失败: ${coreResult.error}`);
            }
            if (!coreTranslateResult.success) {
                console.log(`❌ 核心translate方法失败: ${coreTranslateResult.error}`);
            }
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 运行测试
testFixedTranslation().catch(console.error);
