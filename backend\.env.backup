# 服务器配置
PORT=3010
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=usedcar_export
DB_USER=root
DB_PASSWORD=123456
SYNC_DATABASE=true
FORCE_SYNC=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=development_secret_key
JWT_EXPIRES_IN=24h

# 跨域配置
CORS_ORIGIN=*

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# DeepSeek翻译API配置
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions
DEEPSEEK_API_KEY=sdsadsadsadsa

# 百度翻译API配置 (故意设置错误用于测试)
BAIDU_API_URL=https://fanyi-api.baidu.com/api/trans/vip/translate
BAIDU_ASYNC_API_URL=https://fanyi-api.baidu.com/api/trans/async/v2
BAIDU_ASYNC_RESULT_URL=https://fanyi-api.baidu.com/api/trans/async/v2/pull
BAIDU_APP_ID=12312321
BAIDU_SECRET_KEY=321321321

# 有道翻译API配置
YOUDAO_API_URL=https://openapi.youdao.com/api
YOUDAO_APP_KEY=21c114dbfaab7f40
YOUDAO_SECRET_KEY=zUE77vxL7qpuuxz9I6pnDuzlLaJ838i3

# 翻译服务通用配置
TRANSLATION_ENABLED=true
TRANSLATION_SERVICE=baidu
TRANSLATION_DEFAULT_SOURCE=zh
TRANSLATION_DEFAULT_TARGET=en
TRANSLATION_DEBUG=false
TRANSLATION_THROTTLE_INTERVAL=1500  # 增加请求间隔毫秒数到1500毫秒，避免频率限制
TRANSLATION_RETRY_INTERVAL=2000     # 错误重试间隔毫秒数
TRANSLATION_MAX_RETRIES=3           # 最大重试次数

# 新翻译系统配置
USE_NEW_TRANSLATION_SYSTEM=true
AUTO_TRANSLATION_ENABLED=true
TRANSLATION_CACHE_ENABLED=true
TRANSLATION_LOG_LEVEL=info
TRANSLATION_TIMEOUT=30000
TRANSLATION_FALLBACK_TO_OLD=true

# 豆包翻译API配置
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_API_KEY=your_doubao_api_key
