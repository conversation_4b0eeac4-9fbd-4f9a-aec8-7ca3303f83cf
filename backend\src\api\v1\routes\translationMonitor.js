/**
 * 翻译监控管理路由
 * 提供翻译系统监控、配置和管理功能
 */

const express = require('express');
const router = express.Router();
const { checkAuth, isAdmin } = require('../../../middlewares/authMiddleware');
const translationManager = require('../../../services/core/TranslationManager');
const translationMonitoringService = require('../../../services/translationMonitoringService');
const configPersistenceService = require('../../../services/configPersistenceService');

/**
 * 获取翻译系统当前状态（不生成新报告）- 无需认证的公开监控接口
 * GET /api/v1/translation-monitor/performance-report
 */
router.get('/performance-report', async (req, res) => {
  try {
    const monitor = translationManager.monitor;
    
    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    // 获取当前指标数据和完整的性能报告
    const healthStatus = monitor.getHealthStatus();
    const detailedMetrics = monitor.getDetailedMetrics();

    // 生成完整的性能报告（包含车辆数量统计）
    const currentReport = await monitor.generatePerformanceReport();

    // 创建包含时间的报告摘要用于日志输出
    const summaryWithTime = {
      ...currentReport.summary,
      timestamp: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    };

    console.log('[翻译监控API] 生成的报告摘要:', JSON.stringify(summaryWithTime, null, 2));

    // 获取翻译引擎状态
    const engineStats = translationManager.engines ? translationManager.engines.getStats() : null;


    // 获取引擎状态
    let engineStatus = {};
    try {
      engineStatus = translationMonitoringService.getEngineStatus();
    } catch (error) {
      console.error('获取引擎状态失败:', error);
      engineStatus = {
        baidu: { name: '百度翻译', status: 'unknown', successCount: 0, failureCount: 0 },
        deepseek: { name: 'DeepSeek翻译', status: 'unknown', successCount: 0, failureCount: 0 }
      };
    }

    res.json({
      success: true,
      data: {
        performanceReport: currentReport,
        healthStatus,
        detailedMetrics,
        engineStatus,
        systemStatus: {
          isStopped: global.translationSystemStopped || false,
          stoppedAt: global.translationSystemStoppedAt || null,
          reason: global.translationSystemStopped ? '备用翻译引擎失败' : null
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取性能报告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取性能报告失败',
      error: error.message
    });
  }
});

/**
 * 公开监控状态接口（无需认证）
 * GET /api/v1/translation-monitor/public-status
 */
router.get('/public-status', async (req, res) => {
  try {
    const monitor = translationManager.monitor;

    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控服务未初始化'
      });
    }

    // 获取基础性能数据
    const currentReport = await monitor.generatePerformanceReport();
    const healthStatus = monitor.getHealthStatus();
    const detailedMetrics = monitor.getDetailedMetrics();

    // 获取引擎状态
    let engineStatus = {};
    try {
      engineStatus = translationMonitoringService.getEngineStatus();
    } catch (error) {
      console.error('获取引擎状态失败:', error);
      engineStatus = {
        baidu: { name: '百度翻译', status: 'unknown', successCount: 0, failureCount: 0 },
        deepseek: { name: 'DeepSeek翻译', status: 'unknown', successCount: 0, failureCount: 0 }
      };
    }

    res.json({
      success: true,
      data: {
        performanceReport: currentReport,
        healthStatus,
        detailedMetrics,
        engineStatus,
        systemStatus: {
          isStopped: global.translationSystemStopped || false,
          stoppedAt: global.translationSystemStoppedAt || null,
          reason: global.translationSystemStopped ? '备用翻译引擎失败' : null
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('获取公开监控状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取监控状态失败',
      error: error.message
    });
  }
});

/**
 * 获取翻译监控配置
 * GET /api/v1/translation-monitor/config
 */
router.get('/config', checkAuth, isAdmin, async (req, res) => {
  try {
    const monitor = translationManager.monitor;
    
    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    const config = monitor.config;

    res.json({
      success: true,
      data: {
        config: {
          performance: config.performance,
          healthCheck: config.healthCheck,
          logging: config.logging
        }
      }
    });
  } catch (error) {
    console.error('获取监控配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取监控配置失败',
      error: error.message
    });
  }
});

/**
 * 更新翻译监控配置
 * PUT /api/v1/translation-monitor/config
 */
router.put('/config', checkAuth, isAdmin, async (req, res) => {
  try {
    const { performance, healthCheck, logging } = req.body;
    const monitor = translationManager.monitor;
    
    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    // 更新配置
    if (performance) {
      Object.assign(monitor.config.performance, performance);
    }
    
    if (healthCheck) {
      Object.assign(monitor.config.healthCheck, healthCheck);
    }
    
    if (logging) {
      Object.assign(monitor.config.logging, logging);
    }



    res.json({
      success: true,
      message: '监控配置更新成功',
      data: {
        config: {
          performance: monitor.config.performance,
          healthCheck: monitor.config.healthCheck,
          logging: monitor.config.logging
        }
      }
    });
  } catch (error) {
    console.error('更新监控配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新监控配置失败',
      error: error.message
    });
  }
});

/**
 * 手动生成性能报告
 * POST /api/v1/translation-monitor/generate-report
 */
router.post('/generate-report', checkAuth, isAdmin, async (req, res) => {
  try {
    const monitor = translationManager.monitor;
    
    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    const report = await monitor.generatePerformanceReport();
    
    // 触发性能报告事件
    monitor.emit('performanceReport', report);

    res.json({
      success: true,
      message: '性能报告生成成功',
      data: report
    });
  } catch (error) {
    console.error('生成性能报告失败:', error);
    res.status(500).json({
      success: false,
      message: '生成性能报告失败',
      error: error.message
    });
  }
});

/**
 * 重置翻译监控指标
 * POST /api/v1/translation-monitor/reset-metrics
 */
router.post('/reset-metrics', checkAuth, isAdmin, async (req, res) => {
  try {
    const monitor = translationManager.monitor;

    console.log('[重置API] 收到重置请求');

    if (!monitor) {
      console.log('[重置API] 翻译监控系统未初始化');
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    console.log('[重置API] 开始调用 monitor.resetMetrics...');
    monitor.resetMetrics();
    console.log('[重置API] resetMetrics 调用完成');

    const message = '监控指标重置成功（包含翻译车辆数量统计）';
    console.log('[重置API] 重置成功:', message);

    res.json({
      success: true,
      message: message
    });
  } catch (error) {
    console.error('[重置API] 重置监控指标失败:', error);
    res.status(500).json({
      success: false,
      message: '重置监控指标失败',
      error: error.message
    });
  }
});

/**
 * 清理过期监控数据
 * POST /api/v1/translation-monitor/cleanup
 */
router.post('/cleanup', checkAuth, isAdmin, async (req, res) => {
  try {
    const monitor = translationManager.monitor;
    
    if (!monitor) {
      return res.status(500).json({
        success: false,
        message: '翻译监控系统未初始化'
      });
    }

    monitor.cleanupExpiredData();

    res.json({
      success: true,
      message: '过期数据清理完成'
    });
  } catch (error) {
    console.error('清理过期数据失败:', error);
    res.status(500).json({
      success: false,
      message: '清理过期数据失败',
      error: error.message
    });
  }
});

/**
 * 获取翻译系统状态
 * GET /api/v1/translation-monitor/status
 */
router.get('/status', checkAuth, isAdmin, async (req, res) => {
  try {
    const isRunning = translationManager.isRunning;

    // 获取实际使用的翻译队列统计（旧翻译队列服务）
    const translationQueueService = require('../../../services/translationQueueService');
    const queueStats = translationQueueService.getStats();

    // 添加调试日志
    console.log('[翻译监控API] 队列统计数据:', queueStats);

    const monitor = translationManager.monitor;

    const status = {
      isRunning,
      queueStats,
      healthStatus: monitor ? monitor.getHealthStatus() : null,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统状态失败',
      error: error.message
    });
  }
});

/**
 * 获取翻译引擎配置
 * GET /api/v1/translation-monitor/engine-config
 */
router.get('/engine-config', checkAuth, isAdmin, async (req, res) => {
  try {
    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');

    // 获取当前环境变量中的配置
    const currentConfig = {
      engines: {},
      currentEngine: process.env.TRANSLATION_SERVICE || 'baidu',
      settings: {
        timeout: process.env.TRANSLATION_TIMEOUT || '30000',
        enableCache: process.env.TRANSLATION_CACHE_ENABLED === 'true',
        logLevel: process.env.TRANSLATION_LOG_LEVEL || 'info'
      }
    };

    // 为每个引擎添加配置信息（隐藏敏感信息）
    const enginePromises = Object.keys(TRANSLATION_ENGINES).map(async engineName => {
      const engine = TRANSLATION_ENGINES[engineName];
      const healthStatus = await testEngineHealth(engineName);

      currentConfig.engines[engineName] = {
        name: engine.name,
        apiUrl: engine.apiUrl,
        timeout: engine.timeout,
        maxTextLength: engine.maxTextLength,
        rateLimit: engine.rateLimit,
        // 添加当前的API密钥状态（不显示实际值）
        credentials: getEngineCredentialsStatus(engineName),
        // 添加健康状态
        health: healthStatus
      };
    });

    // 等待所有引擎健康检查完成
    await Promise.all(enginePromises);

    res.json({
      success: true,
      data: currentConfig
    });
  } catch (error) {
    console.error('获取翻译引擎配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取翻译引擎配置失败',
      error: error.message
    });
  }
});

/**
 * 更新翻译引擎配置
 * PUT /api/v1/translation-monitor/engine-config
 */
router.put('/engine-config', checkAuth, isAdmin, async (req, res) => {
  try {
    const { engineName, config } = req.body;

    if (!engineName || !config) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: engineName 和 config'
      });
    }

    // 验证引擎名称
    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');
    if (!TRANSLATION_ENGINES[engineName]) {
      return res.status(400).json({
        success: false,
        message: `不支持的翻译引擎: ${engineName}`
      });
    }

    // 更新环境变量（运行时和持久化）
    const updateResult = await configPersistenceService.updateTranslationEngineConfig(engineName, config);

    if (!updateResult.success) {
      return res.status(400).json(updateResult);
    }

    // 重新初始化翻译引擎（如果需要）
    try {
      if (translationManager && translationManager.engines) {
        await translationManager.engines.reinitialize();
      }
    } catch (reinitError) {
      console.warn('重新初始化翻译引擎时出现警告:', reinitError.message);
    }

    res.json({
      success: true,
      message: `翻译引擎 ${engineName} 配置更新成功`,
      data: {
        engineName,
        updatedConfig: config
      }
    });
  } catch (error) {
    console.error('更新翻译引擎配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新翻译引擎配置失败',
      error: error.message
    });
  }
});

/**
 * 切换主要翻译引擎
 * POST /api/v1/translation-monitor/switch-engine
 */
router.post('/switch-engine', checkAuth, isAdmin, async (req, res) => {
  try {
    const { engineName } = req.body;

    if (!engineName) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: engineName'
      });
    }

    // 验证引擎名称
    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');
    if (!TRANSLATION_ENGINES[engineName]) {
      return res.status(400).json({
        success: false,
        message: `不支持的翻译引擎: ${engineName}`
      });
    }

    // 检查引擎是否可用
    const credentialsStatus = getEngineCredentialsStatus(engineName);
    if (!credentialsStatus.hasCredentials) {
      return res.status(400).json({
        success: false,
        message: `翻译引擎 ${engineName} 缺少必要的API密钥配置`
      });
    }

    // 更新主要翻译引擎（持久化保存）
    const switchResult = await configPersistenceService.updateEnvVars({
      TRANSLATION_SERVICE: engineName
    });

    if (!switchResult.success) {
      return res.status(500).json({
        success: false,
        message: `切换引擎失败: ${switchResult.message}`
      });
    }

    // 重新初始化翻译引擎
    try {
      if (translationManager && translationManager.engines) {
        await translationManager.engines.reinitialize();
      }
    } catch (reinitError) {
      console.warn('重新初始化翻译引擎时出现警告:', reinitError.message);
    }

    res.json({
      success: true,
      message: `已切换到翻译引擎: ${TRANSLATION_ENGINES[engineName].name}`,
      data: {
        currentEngine: engineName,
        engineName: TRANSLATION_ENGINES[engineName].name
      }
    });
  } catch (error) {
    console.error('切换翻译引擎失败:', error);
    res.status(500).json({
      success: false,
      message: '切换翻译引擎失败',
      error: error.message
    });
  }
});

/**
 * 测试翻译引擎连接
 * POST /api/v1/translation-monitor/test-engine
 */
router.post('/test-engine', checkAuth, isAdmin, async (req, res) => {
  try {
    const { engineName, testText = '测试' } = req.body;

    if (!engineName) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: engineName'
      });
    }

    // 验证引擎名称
    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');
    if (!TRANSLATION_ENGINES[engineName]) {
      return res.status(400).json({
        success: false,
        message: `不支持的翻译引擎: ${engineName}`
      });
    }

    // 执行测试翻译
    const testResult = await testEngineConnection(engineName, testText);

    res.json({
      success: true,
      message: '翻译引擎测试完成',
      data: testResult
    });
  } catch (error) {
    console.error('测试翻译引擎失败:', error);
    res.status(500).json({
      success: false,
      message: '测试翻译引擎失败',
      error: error.message
    });
  }
});

/**
 * 引擎健康状态缓存
 */
const engineHealthCache = new Map();
const HEALTH_CACHE_TTL = 30000; // 30秒缓存

/**
 * 测试引擎健康状态
 */
async function testEngineHealth(engineName) {
  const cacheKey = `health_${engineName}`;
  const cached = engineHealthCache.get(cacheKey);

  // 如果有缓存且未过期，返回缓存结果
  if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_TTL) {
    return cached.status;
  }

  let healthStatus = {
    healthy: false,
    lastTest: new Date().toISOString(),
    error: null,
    responseTime: null
  };

  try {
    const startTime = Date.now();

    // 根据引擎类型进行健康检查
    switch (engineName) {
      case 'baidu':
        const { translateWithBaidu } = require('../../../services/baiduTranslationService');
        const baiduResult = await translateWithBaidu('测试', 'zh', 'en');
        healthStatus.healthy = baiduResult.success;
        healthStatus.error = baiduResult.success ? null : baiduResult.error;
        break;

      case 'deepseek':
        const { translateWithDeepseek } = require('../../../services/deepseekTranslationService');
        const deepseekResult = await translateWithDeepseek('测试', 'zh', 'en');
        healthStatus.healthy = deepseekResult.success;
        healthStatus.error = deepseekResult.success ? null : deepseekResult.error;
        break;

      default:
        healthStatus.error = `不支持的引擎: ${engineName}`;
    }

    healthStatus.responseTime = Date.now() - startTime;

  } catch (error) {
    healthStatus.healthy = false;
    healthStatus.error = error.message;
    healthStatus.responseTime = Date.now() - startTime;
  }

  // 缓存结果
  engineHealthCache.set(cacheKey, {
    status: healthStatus,
    timestamp: Date.now()
  });

  return healthStatus;
}

// 辅助函数：获取引擎凭据状态
function getEngineCredentialsStatus(engineName) {
  const status = {
    hasCredentials: false,
    missingCredentials: [],
    configuredCredentials: []
  };

  switch (engineName) {
    case 'baidu':
      const baiduAppId = process.env.BAIDU_APP_ID;
      const baiduSecretKey = process.env.BAIDU_SECRET_KEY;

      if (baiduAppId) status.configuredCredentials.push('BAIDU_APP_ID');
      else status.missingCredentials.push('BAIDU_APP_ID');

      if (baiduSecretKey) status.configuredCredentials.push('BAIDU_SECRET_KEY');
      else status.missingCredentials.push('BAIDU_SECRET_KEY');

      status.hasCredentials = baiduAppId && baiduSecretKey;
      break;

    case 'deepseek':
      const deepseekApiKey = process.env.DEEPSEEK_API_KEY;

      if (deepseekApiKey) status.configuredCredentials.push('DEEPSEEK_API_KEY');
      else status.missingCredentials.push('DEEPSEEK_API_KEY');

      status.hasCredentials = !!deepseekApiKey;
      break;


  }

  return status;
}



// 辅助函数：验证引擎配置
function validateEngineConfig(engineName, config) {
  const errors = [];

  switch (engineName) {
    case 'baidu':
      if (config.appId && typeof config.appId !== 'string') {
        errors.push('百度APP ID必须是字符串');
      }
      if (config.secretKey && typeof config.secretKey !== 'string') {
        errors.push('百度密钥必须是字符串');
      }
      if (config.apiUrl && !isValidUrl(config.apiUrl)) {
        errors.push('API URL格式无效');
      }
      break;

    case 'deepseek':
      if (config.apiKey && typeof config.apiKey !== 'string') {
        errors.push('DeepSeek API密钥必须是字符串');
      }
      if (config.apiUrl && !isValidUrl(config.apiUrl)) {
        errors.push('API URL格式无效');
      }
      break;


  }

  // 通用验证
  if (config.timeout && (isNaN(config.timeout) || config.timeout < 1000)) {
    errors.push('超时时间必须是大于1000的数字');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



// 辅助函数：验证URL格式
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// 辅助函数：测试引擎连接
async function testEngineConnection(engineName, testText) {
  try {
    const startTime = Date.now();

    // 根据引擎类型执行测试翻译
    let result;
    switch (engineName) {
      case 'baidu':
        const { translateWithBaidu } = require('../../../services/baiduTranslationService');
        result = await translateWithBaidu(testText, 'zh', 'en');
        break;
      case 'deepseek':
        const { translateWithDeepseek } = require('../../../services/deepseekTranslationService');
        result = await translateWithDeepseek(testText, 'zh', 'en');
        break;
      default:
        throw new Error(`暂不支持测试引擎: ${engineName}`);
    }

    const duration = Date.now() - startTime;

    return {
      engineName,
      success: result.success,
      duration: `${duration}ms`,
      testText,
      translatedText: result.success ? result.translatedText : null,
      error: result.success ? null : result.error,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      engineName,
      success: false,
      duration: '0ms',
      testText,
      translatedText: null,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 获取配置文件状态
 * GET /api/v1/translation-monitor/config-status
 */
router.get('/config-status', checkAuth, isAdmin, async (req, res) => {
  try {
    // 验证配置文件
    const validation = await configPersistenceService.validateEnvFile();

    // 获取翻译引擎相关配置
    const translationVars = await configPersistenceService.getTranslationEngineVars();

    res.json({
      success: true,
      data: {
        validation,
        translationVars,
        lastModified: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取配置状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置状态失败',
      error: error.message
    });
  }
});

/**
 * 重新加载配置
 * POST /api/v1/translation-monitor/reload-config
 */
router.post('/reload-config', checkAuth, isAdmin, async (req, res) => {
  try {
    const reloadResult = await configPersistenceService.reloadEnvVars();

    if (!reloadResult.success) {
      return res.status(500).json(reloadResult);
    }

    // 重新初始化翻译引擎
    try {
      if (translationManager && translationManager.engines) {
        await translationManager.engines.reinitialize();
      }
    } catch (reinitError) {
      console.warn('重新初始化翻译引擎时出现警告:', reinitError.message);
    }

    res.json({
      success: true,
      message: '配置重新加载成功，翻译引擎已重新初始化'
    });
  } catch (error) {
    console.error('重新加载配置失败:', error);
    res.status(500).json({
      success: false,
      message: '重新加载配置失败',
      error: error.message
    });
  }
});

/**
 * 获取引擎健康状态
 * GET /api/v1/translation-monitor/engine-health
 */
router.get('/engine-health', checkAuth, isAdmin, async (req, res) => {
  try {
    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');
    const healthStatus = {};

    // 并行检查所有引擎的健康状态
    const healthPromises = Object.keys(TRANSLATION_ENGINES).map(async engineName => {
      const health = await testEngineHealth(engineName);
      healthStatus[engineName] = {
        name: TRANSLATION_ENGINES[engineName].name,
        ...health
      };
    });

    await Promise.all(healthPromises);

    // 计算整体系统状态
    const allEngines = Object.values(healthStatus);
    const healthyEngines = allEngines.filter(engine => engine.healthy);
    const systemStatus = {
      totalEngines: allEngines.length,
      healthyEngines: healthyEngines.length,
      unhealthyEngines: allEngines.length - healthyEngines.length,
      overallHealth: healthyEngines.length > 0 ? 'partial' : 'critical',
      lastCheck: new Date().toISOString()
    };

    if (healthyEngines.length === allEngines.length) {
      systemStatus.overallHealth = 'healthy';
    }

    res.json({
      success: true,
      data: {
        engines: healthStatus,
        system: systemStatus
      }
    });
  } catch (error) {
    console.error('获取引擎健康状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取引擎健康状态失败',
      error: error.message
    });
  }
});

/**
 * 强制刷新引擎健康状态
 * POST /api/v1/translation-monitor/refresh-engine-health
 */
router.post('/refresh-engine-health', checkAuth, isAdmin, async (req, res) => {
  try {
    // 清除健康状态缓存
    engineHealthCache.clear();

    const { TRANSLATION_ENGINES } = require('../../../config/translationConfig');
    const healthStatus = {};

    // 重新检查所有引擎的健康状态
    const healthPromises = Object.keys(TRANSLATION_ENGINES).map(async engineName => {
      const health = await testEngineHealth(engineName);
      healthStatus[engineName] = {
        name: TRANSLATION_ENGINES[engineName].name,
        ...health
      };
    });

    await Promise.all(healthPromises);

    res.json({
      success: true,
      message: '引擎健康状态已刷新',
      data: healthStatus
    });
  } catch (error) {
    console.error('刷新引擎健康状态失败:', error);
    res.status(500).json({
      success: false,
      message: '刷新引擎健康状态失败',
      error: error.message
    });
  }
});

/**
 * 创建配置备份
 * POST /api/v1/translation-monitor/backup-config
 */
router.post('/backup-config', checkAuth, isAdmin, async (req, res) => {
  try {
    const backupResult = await configPersistenceService.createBackup();

    if (!backupResult) {
      return res.status(500).json({
        success: false,
        message: '创建配置备份失败'
      });
    }

    res.json({
      success: true,
      message: '配置备份创建成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('创建配置备份失败:', error);
    res.status(500).json({
      success: false,
      message: '创建配置备份失败',
      error: error.message
    });
  }
});

/**
 * 恢复配置备份
 * POST /api/v1/translation-monitor/restore-config
 */
router.post('/restore-config', checkAuth, isAdmin, async (req, res) => {
  try {
    const restoreResult = await configPersistenceService.restoreBackup();

    if (!restoreResult) {
      return res.status(500).json({
        success: false,
        message: '恢复配置备份失败'
      });
    }

    // 重新加载环境变量
    await configPersistenceService.reloadEnvVars();

    // 重新初始化翻译引擎
    try {
      if (translationManager && translationManager.engines) {
        await translationManager.engines.reinitialize();
      }
    } catch (reinitError) {
      console.warn('重新初始化翻译引擎时出现警告:', reinitError.message);
    }

    res.json({
      success: true,
      message: '配置备份恢复成功，系统已重新初始化',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('恢复配置备份失败:', error);
    res.status(500).json({
      success: false,
      message: '恢复配置备份失败',
      error: error.message
    });
  }
});

/**
 * 手动扫描需要翻译的车辆数量
 * POST /api/v1/translation-monitor/scan-pending-vehicles
 */
router.post('/scan-pending-vehicles', checkAuth, isAdmin, async (req, res) => {
  try {
    const db = require('../../../models');

    console.log('[手动扫描] 开始扫描需要翻译的车辆...');

    // 查询所有产品，检查test_xx字段
    const products = await db.Product.findAll({
      attributes: ['productId', 'title', 'brand', 'test_en', 'test_ru', 'test_fr', 'test_es', 'test_all'],
      where: {
        status: 'active' // 只统计已上架的车辆
      }
    });

    let needTranslationCount = 0;
    const pendingVehicles = [];

    // 检查每个产品的翻译状态
    for (const product of products) {
      const testFields = ['test_en', 'test_ru', 'test_fr', 'test_es'];
      let hasIncompleteTranslation = false;
      const incompleteLanguages = [];

      // 检查是否有任何test_xx字段不是true
      for (const field of testFields) {
        if (!product[field]) {
          hasIncompleteTranslation = true;
          incompleteLanguages.push(field.replace('test_', ''));
        }
      }

      if (hasIncompleteTranslation) {
        needTranslationCount++;
        pendingVehicles.push({
          productId: product.productId,
          title: product.title || '无标题',
          brand: product.brand || '未知品牌',
          incompleteLanguages: incompleteLanguages,
          test_all: product.test_all
        });
      }
    }

    const scanResult = {
      totalVehicles: products.length,
      needTranslationCount: needTranslationCount,
      completedCount: products.length - needTranslationCount,
      scanTime: new Date().toISOString(),
      pendingVehicles: pendingVehicles.slice(0, 10) // 只返回前10个作为示例
    };

    console.log(`[手动扫描] 扫描完成: 总车辆数=${products.length}, 需要翻译=${needTranslationCount}, 已完成=${products.length - needTranslationCount}`);

    res.json({
      success: true,
      message: `扫描完成，发现 ${needTranslationCount} 辆车需要翻译`,
      data: scanResult
    });
  } catch (error) {
    console.error('[手动扫描] 扫描需要翻译的车辆失败:', error);
    res.status(500).json({
      success: false,
      message: '扫描需要翻译的车辆失败',
      error: error.message
    });
  }
});

/**
 * 手动触发自动翻译产品任务
 * POST /api/v1/translation-monitor/trigger-auto-translation
 */
router.post('/trigger-auto-translation', checkAuth, isAdmin, async (req, res) => {
  try {
    console.log('[翻译监控] 收到手动触发自动翻译请求');

    // 导入自动翻译任务函数
    const { autoTranslateProductsTask } = require('../../../services/newScheduledTasksService');

    // 执行自动翻译任务
    const result = await autoTranslateProductsTask();

    console.log('[翻译监控] 手动触发自动翻译完成:', result);

    res.json({
      success: true,
      message: '自动翻译任务已手动触发',
      data: result
    });

  } catch (error) {
    console.error('[翻译监控] 手动触发自动翻译失败:', error);
    res.status(500).json({
      success: false,
      message: '手动触发自动翻译失败',
      error: error.message
    });
  }
});

module.exports = router;
