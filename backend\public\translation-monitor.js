/**
 * 翻译监控管理系统 JavaScript
 */

// API 基础URL
const API_BASE = '/api/v1/translation-monitor';

// 认证token (从localStorage获取，如果没有则需要登录)
let authToken = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

// 调试信息
console.log('当前token:', authToken);
console.log('localStorage keys:', Object.keys(localStorage));

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('翻译监控管理系统初始化');

    // 直接加载公开监控数据，无需认证
    refreshPublicStatus();

    // 如果有认证token，则加载配置管理功能
    if (authToken) {
        loadConfig();
        loadEngineConfig(); // 加载引擎配置
        checkConfigStatus(); // 检查配置状态
        // 删除自动加载引擎健康状态，改为手动刷新
        showMessage('已登录管理系统，可使用完整功能', 'success');
    } else {
        showMessage('当前为只读监控模式，如需管理功能请先登录', 'info');
        // 隐藏需要认证的控制面板
        const controlPanel = document.querySelector('.control-panel');
        const configSections = document.querySelectorAll('.config-section');
        if (controlPanel) controlPanel.style.display = 'none';
        configSections.forEach(section => {
            if (section) section.style.display = 'none';
        });
    }

    // 定时刷新监控数据
    setInterval(refreshPublicStatus, 30000); // 每30秒刷新一次
});

/**
 * 发送API请求
 */
async function apiRequest(endpoint, options = {}) {
    const url = API_BASE + endpoint;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
            ...options.headers
        },
        ...options
    };
    
    try {
        const response = await fetch(url, config);
        const data = await response.json();
        
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                showMessage('认证失败，请重新登录管理系统', 'error');
                authToken = null;
                localStorage.removeItem('adminToken');
                localStorage.removeItem('authToken');
            }
            throw new Error(data.message || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        console.error('请求URL:', url);
        console.error('请求选项:', options);
        showMessage(`API请求失败: ${error.message}`, 'error');
        throw error;
    }
}

/**
 * 格式化时间显示
 * @param {string|number} timeStr - 时间字符串或数字，如 "1500ms" 或 1500
 * @returns {string} - 格式化后的时间
 */
function formatTime(timeStr) {
    if (!timeStr || timeStr === 'N/A' || timeStr === null || timeStr === undefined) return 'N/A';

    // 如果是数字，直接处理
    if (typeof timeStr === 'number') {
        const ms = timeStr;
        if (ms < 1000) {
            return `${ms}ms`;
        } else if (ms < 60000) {
            return `${(ms / 1000).toFixed(1)}s`;
        } else {
            return `${(ms / 60000).toFixed(1)}min`;
        }
    }

    // 如果不是字符串，转换为字符串
    if (typeof timeStr !== 'string') {
        timeStr = String(timeStr);
    }

    // 提取数字部分
    const match = timeStr.match(/(\d+)/);
    if (!match) return timeStr;

    const ms = parseInt(match[1]);

    if (ms < 1000) {
        return `${ms}ms`;
    } else if (ms < 60000) {
        return `${(ms / 1000).toFixed(1)}s`;
    } else {
        return `${(ms / 60000).toFixed(1)}min`;
    }
}

/**
 * 更新系统状态显示
 * @param {string} status - 状态类型 (running, stopped, warning)
 * @param {string} text - 状态文本
 */
function updateSystemStatus(status, text) {
    const statusIndicator = document.getElementById('systemStatus');
    const statusText = document.getElementById('systemStatusText');

    if (statusIndicator && statusText) {
        statusIndicator.className = `status-indicator status-${status}`;
        statusText.textContent = text;
    }
}



/**
 * 显示错误详情
 * @param {Object} errors - 错误数据
 */
function displayErrorDetails(errors) {
    const errorDetails = document.getElementById('errorDetails');
    const errorSummary = document.getElementById('errorSummary');
    const errorList = document.getElementById('errorList');

    if (!errors || !errors.recentErrors || errors.recentErrors.length === 0) {
        errorDetails.style.display = 'none';
        return;
    }

    errorDetails.style.display = 'block';

    // 更新错误摘要
    if (errorSummary) {
        errorSummary.innerHTML = `
            <div class="summary-item">
                <span class="summary-label">错误数量:</span> ${errors.recentErrors.length} 个
            </div>
        `;
    }

    // 清空错误列表并添加滚动提示
    errorList.innerHTML = `
        <div class="scroll-hint" style="text-align: center; color: #718096; font-size: 0.9rem; padding: 10px; border-bottom: 1px solid #e2e8f0; margin-bottom: 10px;">
            📜 错误列表可上下滚动查看 (共 ${errors.recentErrors.length} 个错误)
        </div>
    `;

    errors.recentErrors.forEach(error => {
        // 检查错误对象是否有效
        if (!error || typeof error !== 'object') {
            console.warn('无效的错误对象:', error);
            return;
        }

        const errorItem = document.createElement('div');
        errorItem.className = 'error-item';

        // 解析错误ID获取详细信息
        const errorInfo = parseErrorId(error.translationId);

        errorItem.innerHTML = `
            <div class="error-title">
                ${errorInfo.type === 'product' ? '🚗 产品翻译错误' : '⚙️ 配置翻译错误'}
            </div>
            <div class="error-details-text">
                <strong>实体:</strong> ${errorInfo.type === 'product' ? '产品' : '配置'} ${errorInfo.entityId}<br>
                <strong>语言:</strong> ${errorInfo.language}<br>
                <strong>错误:</strong> ${error.error}<br>
                <strong>时间:</strong> ${new Date(error.timestamp).toLocaleString()}
            </div>
        `;

        errorList.appendChild(errorItem);
    });

    // 如果错误数量较多，自动滚动到顶部
    if (errors.recentErrors.length > 5) {
        errorList.scrollTop = 0;
    }
}

/**
 * 解析错误ID获取详细信息
 * @param {string} translationId - 翻译ID，格式如 "product_123_en_1234567890" 或 "config_456_fr_1234567890"
 * @returns {Object} - 解析后的信息
 */
function parseErrorId(translationId) {
    // 检查输入是否有效
    if (!translationId || typeof translationId !== 'string') {
        return {
            type: 'unknown',
            entityId: 'unknown',
            language: 'unknown'
        };
    }

    const parts = translationId.split('_');
    if (parts.length >= 3) {
        return {
            type: parts[0], // product 或 config
            entityId: parts[1], // 产品ID或配置ID
            language: parts[2] // 语言代码
        };
    }

    return {
        type: 'unknown',
        entityId: 'unknown',
        language: 'unknown'
    };
}

/**
 * 显示消息
 */
function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('controlMessage');
    messageEl.textContent = message;
    messageEl.className = `alert alert-${type}`;
    messageEl.classList.remove('hidden');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        messageEl.classList.add('hidden');
    }, 3000);
}

/**
 * 刷新公开监控状态（无需认证）
 */
async function refreshPublicStatus() {
    try {
        console.log('开始刷新公开监控状态...');

        // 使用公开API获取监控数据
        const response = await fetch(`${API_BASE}/public-status`);
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || '获取监控数据失败');
        }

        const performanceData = result.data;
        console.log('公开监控数据:', performanceData);

        // 更新状态显示
        updateStatusDisplay(performanceData, performanceData);
        updateReportDisplay(performanceData);

        // 更新系统状态指示器
        updateSystemStatus('running', '运行正常');

    } catch (error) {
        console.error('刷新公开监控状态失败:', error);
        updateSystemStatus('error', '连接失败');
        showMessage(`获取监控数据失败: ${error.message}`, 'error');

        // 显示错误信息
        const reportContent = document.getElementById('reportContent');
        if (reportContent) {
            reportContent.textContent = `连接失败: ${error.message}\n\n请检查服务器状态或刷新页面重试。`;
        }
    }
}

/**
 * 刷新系统状态（需要认证）
 */
async function refreshStatus() {
    try {
        console.log('开始刷新状态...');

        // 获取系统状态
        console.log('请求系统状态...');
        const statusData = await apiRequest('/status');
        console.log('系统状态响应:', statusData);

        console.log('请求性能报告...');
        const performanceData = await apiRequest('/performance-report');
        console.log('性能报告响应:', performanceData);

        updateStatusDisplay(statusData.data, performanceData.data);
        updateReportDisplay(performanceData.data);

        console.log('状态刷新完成');

    } catch (error) {
        console.error('刷新状态失败:', error);
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            tokenExists: authToken ? '存在' : '不存在',
            apiBase: API_BASE
        });

        updateStatusDisplay(null, null);

        // 如果是认证错误，提示用户重新登录
        if (error.message.includes('401') || error.message.includes('403') || error.message.includes('认证')) {
            showMessage('认证失败，请重新登录管理系统后刷新页面', 'error');
        } else if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            showMessage('网络连接失败，请检查后端服务是否正常运行', 'error');
        } else {
            showMessage('刷新状态失败: ' + error.message, 'error');
        }
    }
}

/**
 * 更新状态显示
 */
function updateStatusDisplay(statusData, performanceData) {
    console.log('更新状态显示 - statusData:', statusData);
    console.log('更新状态显示 - performanceData:', performanceData);

    // 调试队列数据
    if (statusData && statusData.queueStats) {
        console.log('队列统计数据:', statusData.queueStats);
    } else {
        console.log('队列统计数据为空或未定义');
    }

    if (!statusData || !performanceData) {
        console.warn('状态数据或性能数据为空');
        // 安全地显示错误状态
        const successRateElement = document.getElementById('successRate');
        if (successRateElement) successRateElement.textContent = 'N/A';

        const avgResponseTimeElement = document.getElementById('avgResponseTime');
        if (avgResponseTimeElement) avgResponseTimeElement.textContent = 'N/A';

        const totalTranslationsElement = document.getElementById('totalTranslations');
        if (totalTranslationsElement) totalTranslationsElement.textContent = 'N/A';

        const queueSizeElement = document.getElementById('queueSize');
        if (queueSizeElement) queueSizeElement.textContent = '0';

        updateSystemStatus('stopped', '连接失败');
        return;
    }
    
    // 更新统计数据
    const report = performanceData.performanceReport;
    console.log('性能报告数据:', report);
    console.log('翻译车辆数量:', report?.summary?.translatedVehicleCount);

    // 安全地访问报告数据，避免undefined错误
    const summary = report?.summary || {};

    // 安全地更新DOM元素
    const successRateElement = document.getElementById('successRate');
    if (successRateElement) {
        successRateElement.textContent = summary.successRate || 'N/A';
    }

    const avgResponseTimeElement = document.getElementById('avgResponseTime');
    if (avgResponseTimeElement) {
        avgResponseTimeElement.textContent = formatTime(summary.averageResponseTime);
    }

    const totalTranslationsElement = document.getElementById('totalTranslations');
    if (totalTranslationsElement) {
        totalTranslationsElement.textContent = summary.totalTranslations || '0';
    }

    const translatedVehicleCountElement = document.getElementById('translatedVehicleCount');
    if (translatedVehicleCountElement) {
        translatedVehicleCountElement.textContent = summary.translatedVehicleCount || '0';
    }

    // 显示错误信息
    updateErrorDisplay(report.errors);

    // 队列大小处理，添加更好的错误处理
    let queueSize = '0';
    if (statusData && statusData.queueStats) {
        // 尝试多种可能的字段名
        if (statusData.queueStats.currentQueueSize !== undefined) {
            queueSize = statusData.queueStats.currentQueueSize.toString();
        } else if (statusData.queueStats.size !== undefined) {
            queueSize = statusData.queueStats.size.toString();
        } else if (statusData.queueStats.totalQueueSize !== undefined) {
            queueSize = statusData.queueStats.totalQueueSize.toString();
        }
    }
    const queueSizeElement = document.getElementById('queueSize');
    if (queueSizeElement) {
        queueSizeElement.textContent = queueSize;
    }

    // 检查翻译系统停止状态
    const systemStatus = performanceData.systemStatus;
    const isSystemStopped = systemStatus && systemStatus.isStopped;

    // 更新系统状态
    const isHealthy = statusData.isRunning && report.summary.successRate !== '0%';
    const hasErrors = report.errors && report.errors.totalErrors > 0;

    if (isSystemStopped) {
        updateSystemStatus('error', `翻译系统已停止 - ${systemStatus.reason || '未知原因'}`);
        // 显示停止时间
        if (systemStatus.stoppedAt) {
            const stoppedTime = new Date(systemStatus.stoppedAt).toLocaleString();
            const systemStatusTextElement = document.getElementById('systemStatusText');
            if (systemStatusTextElement) {
                systemStatusTextElement.textContent = `已停止 (${stoppedTime})`;
            }
        }
    } else if (!statusData.isRunning) {
        updateSystemStatus('stopped', '系统停止');
    } else if (hasErrors) {
        updateSystemStatus('warning', `运行中 (${report.errors.totalErrors} 个错误)`);
    } else {
        updateSystemStatus('running', '运行正常');
    }



    // 更新系统状态指示器
    const statusIndicator = document.getElementById('systemStatus');
    const statusText = document.getElementById('systemStatusText');

    if (statusData.isRunning) {
        statusIndicator.className = 'status-indicator status-running';
        statusText.textContent = '运行中';
    } else {
        statusIndicator.className = 'status-indicator status-stopped';
        statusText.textContent = '已停止';
    }


}

/**
 * 更新报告显示
 */
function updateReportDisplay(performanceData) {
    const reportContent = document.getElementById('reportContent');
    
    if (!performanceData) {
        reportContent.textContent = '无法获取性能数据';
        return;
    }
    
    const report = performanceData.performanceReport;
    const healthStatus = performanceData.healthStatus;
    
    const reportText = `
=== 翻译系统性能报告 ===
生成时间: ${report.timestamp || 'N/A'}

📊 翻译统计:
  总翻译数: ${report.summary?.totalTranslations || '0'}
  成功翻译: ${report.summary?.successfulTranslations || '0'}
  失败翻译: ${report.summary?.failedTranslations || '0'}
  成功率: ${report.summary?.successRate || 'N/A'}
  翻译车辆数量: ${report.summary?.translatedVehicleCount || '0'}

⏱️ 性能指标:
  平均响应时间: ${report.summary?.averageResponseTime || 'N/A'}
  活跃翻译: ${report.activeTranslations || '0'}

🔄 队列状态:
  当前队列大小: ${report.queue?.currentSize || '0'}
  最大队列大小: ${report.queue?.maxSize || '0'}
  总排队数: ${report.queue?.totalQueued || '0'}

🔧 引擎状态:
  活跃翻译: ${report.engines?.activeTranslations || '0'}
  引擎可用性: ${report.engines?.availability ? JSON.stringify(report.engines.availability, null, 2) : '无数据'}

❌ 错误统计:
  总错误数: ${report.errors?.totalErrors || '0'}
  主要错误类型: ${report.errors?.recentErrorTypes?.join(', ') || '无'}
  错误语言: ${report.errors?.topErrorLanguages?.join(', ') || '无'}

🏥 健康状态:
  状态: ${healthStatus?.status || 'N/A'}
  健康分数: ${healthStatus?.healthScore || 'N/A'}/100
  活跃翻译: ${healthStatus?.activeTranslations || '0'}
  运行时间: ${healthStatus?.uptime ? Math.floor(healthStatus.uptime / 3600) + '小时' + Math.floor((healthStatus.uptime % 3600) / 60) + '分钟' : 'N/A'}
`;
    
    reportContent.textContent = reportText;
}

/**
 * 生成性能报告
 */
async function generateReport() {
    try {
        showMessage('正在生成性能报告...', 'info');
        
        const data = await apiRequest('/generate-report', {
            method: 'POST'
        });
        
        showMessage('性能报告生成成功', 'success');
        
        // 刷新显示
        await refreshStatus();
        
    } catch (error) {
        console.error('生成报告失败:', error);
    }
}

/**
 * 测试错误显示滚动功能
 */
function testErrorDisplay() {
    // 创建测试错误数据
    const testErrors = {
        totalErrors: 15,
        recentErrorTypes: ['TranslationError', 'NetworkError', 'TimeoutError'],
        topErrorLanguages: ['en', 'ru', 'es'],
        recentErrors: []
    };

    // 生成15个测试错误
    for (let i = 1; i <= 15; i++) {
        testErrors.recentErrors.push({
            id: `error_${i}`,
            timestamp: new Date(Date.now() - i * 60000).toISOString(),
            type: ['TranslationError', 'NetworkError', 'TimeoutError'][i % 3],
            language: ['en', 'ru', 'fr', 'es'][i % 4],
            engine: ['baidu', 'youdao', 'doubao'][i % 3],
            error: `测试错误信息 ${i} - 这是一个用于测试滚动功能的长错误消息，包含了详细的错误描述信息`,
            productId: `product_${i}`,
            carBrand: ['宝马', '奔驰', '奥迪', '丰田'][i % 4],
            carModel: `测试车型 ${i}`,
            fieldName: ['title', 'description', 'specs'][i % 3],
            originalText: `原始文本 ${i}`
        });
    }

    // 显示测试错误
    updateErrorDisplay(testErrors);

    // 滚动到错误区域
    const errorDetails = document.getElementById('errorDetails');
    if (errorDetails) {
        errorDetails.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * 扫描需要翻译的车辆数量
 */
async function scanPendingVehicles() {
    try {
        showMessage('正在扫描需要翻译的车辆...', 'info');

        const data = await apiRequest('/scan-pending-vehicles', {
            method: 'POST'
        });

        if (data.success) {
            const result = data.data;

            // 显示扫描结果区域
            const scanResults = document.getElementById('scanResults');
            scanResults.style.display = 'block';

            // 更新统计数据
            document.getElementById('totalVehicles').textContent = result.totalVehicles;
            document.getElementById('needTranslationCount').textContent = result.needTranslationCount;
            document.getElementById('completedCount').textContent = result.completedCount;
            document.getElementById('scanTime').textContent = new Date(result.scanTime).toLocaleString('zh-CN');

            // 显示待翻译车辆列表
            const pendingVehiclesContainer = document.getElementById('pendingVehicles');
            if (result.pendingVehicles && result.pendingVehicles.length > 0) {
                let vehiclesHtml = '<h3>待翻译车辆示例（前10个）:</h3>';
                result.pendingVehicles.forEach(vehicle => {
                    vehiclesHtml += `
                        <div class="vehicle-item">
                            <div class="vehicle-title">${vehicle.title}</div>
                            <div class="vehicle-info">品牌: ${vehicle.brand} | ID: ${vehicle.productId}</div>
                            <div class="incomplete-languages">
                                <span>缺失语言: </span>
                                ${vehicle.incompleteLanguages.map(lang =>
                                    `<span class="language-tag">${lang.toUpperCase()}</span>`
                                ).join('')}
                            </div>
                        </div>
                    `;
                });
                pendingVehiclesContainer.innerHTML = vehiclesHtml;
            } else {
                pendingVehiclesContainer.innerHTML = '<p style="text-align: center; color: #38a169; font-weight: bold;">🎉 所有车辆翻译已完成！</p>';
            }

            showMessage(data.message, 'success');

            // 滚动到结果区域
            scanResults.scrollIntoView({ behavior: 'smooth' });

        } else {
            showMessage(`扫描失败: ${data.message}`, 'error');
        }

    } catch (error) {
        console.error('扫描车辆失败:', error);
        showMessage('扫描车辆失败，请检查网络连接', 'error');
    }
}

/**
 * 手动触发自动翻译任务
 */
async function triggerAutoTranslation() {
    if (!confirm('确定要手动触发自动翻译任务吗？\n\n这将：\n• 查找需要翻译的产品\n• 批量处理产品翻译\n• 可能需要较长时间完成\n\n建议在系统空闲时执行。')) {
        return;
    }

    try {
        showMessage('正在触发自动翻译任务...', 'info');

        const data = await apiRequest('/trigger-auto-translation', {
            method: 'POST'
        });

        if (data.success) {
            const result = data.data;
            let message = `自动翻译任务执行完成！\n\n`;

            if (result.processed !== undefined) {
                message += `• 处理产品数量: ${result.processed}\n`;
            }
            if (result.successful !== undefined) {
                message += `• 成功处理: ${result.successful}\n`;
            }
            if (result.failed !== undefined) {
                message += `• 失败数量: ${result.failed}\n`;
            }
            if (result.message) {
                message += `• 详情: ${result.message}`;
            }

            showMessage('自动翻译任务触发成功', 'success');
            alert(message);
        } else {
            showMessage(`触发自动翻译失败: ${data.message}`, 'error');
        }

        // 刷新状态显示
        await refreshStatus();

    } catch (error) {
        console.error('触发自动翻译失败:', error);
        showMessage(`触发自动翻译失败: ${error.message}`, 'error');
    }
}

/**
 * 重置指标
 */
async function resetMetrics() {
    if (!confirm('确定要重置所有监控指标吗？\n\n这将重置：\n• 翻译次数统计\n• 成功率统计\n• 响应时间统计\n• 翻译车辆数量统计\n• 错误记录\n\n此操作不可撤销！')) {
        return;
    }

    try {
        showMessage('正在重置监控指标...', 'info');

        const data = await apiRequest('/reset-metrics', {
            method: 'POST'
        });

        showMessage(data.message, 'success');

        // 刷新显示
        await refreshStatus();

    } catch (error) {
        console.error('重置指标失败:', error);
        showMessage('重置指标失败: ' + error.message, 'error');
    }
}

// 清理过期数据功能已移除，因为监控系统会自动管理数据

/**
 * 加载配置
 */
async function loadConfig() {
    try {
        const data = await apiRequest('/config');
        const config = data.data.config;
        
        // 安全地填充表单 (转换单位)
        const slowTranslationThresholdElement = document.getElementById('slowTranslationThreshold');
        if (slowTranslationThresholdElement) {
            slowTranslationThresholdElement.value = Math.round(config.performance.slowTranslationThreshold / 1000); // 毫秒转秒
        }

        const enableMetricsElement = document.getElementById('enableMetrics');
        if (enableMetricsElement) {
            enableMetricsElement.value = config.performance.enableMetrics.toString();
        }

        const logLevelElement = document.getElementById('logLevel');
        if (logLevelElement) {
            logLevelElement.value = config.logging.level;
        }
        
    } catch (error) {
        console.error('加载配置失败:', error);
    }
}

/**
 * 更新配置
 */
async function updateConfig() {
    try {
        // 安全地获取表单数据
        const slowTranslationThresholdElement = document.getElementById('slowTranslationThreshold');
        const enableMetricsElement = document.getElementById('enableMetrics');
        const logLevelElement = document.getElementById('logLevel');

        if (!slowTranslationThresholdElement || !enableMetricsElement || !logLevelElement) {
            showMessage('配置表单元素未找到', 'error');
            return;
        }

        const formData = {
            performance: {
                slowTranslationThreshold: parseInt(slowTranslationThresholdElement.value) * 1000, // 秒转毫秒
                enableMetrics: enableMetricsElement.value === 'true'
            },
            logging: {
                level: logLevelElement.value
            }
        };
        
        showMessage('正在更新配置...', 'info');
        
        const data = await apiRequest('/config', {
            method: 'PUT',
            body: JSON.stringify(formData)
        });
        
        showMessage(data.message, 'success');
        
    } catch (error) {
        console.error('更新配置失败:', error);
    }
}

/**
 * 更新错误信息显示
 * @param {Object} errors - 错误数据
 */
function updateErrorDisplay(errors) {
    const errorDetails = document.getElementById('errorDetails');
    const errorSummary = document.getElementById('errorSummary');
    const errorList = document.getElementById('errorList');

    if (!errors || !errors.recentErrors || errors.recentErrors.length === 0) {
        errorDetails.style.display = 'none';
        return;
    }

    // 显示错误区域
    errorDetails.style.display = 'block';

    // 更新错误摘要
    if (errorSummary) {
        errorSummary.innerHTML = `
            <div class="summary-item">
                <span class="summary-label">错误统计:</span> 总计 ${errors.totalErrors} 个错误
            </div>
            <div class="summary-item">
                <span class="summary-label">主要错误类型:</span> ${errors.recentErrorTypes.join(', ') || '无'}
            </div>
            <div class="summary-item">
                <span class="summary-label">问题语言:</span> ${errors.topErrorLanguages.join(', ') || '无'}
            </div>
        `;
    }

    // 清空错误列表内容，但保留滚动提示
    errorList.innerHTML = `
        <div class="scroll-hint" style="text-align: center; color: #718096; font-size: 0.9rem; padding: 10px; border-bottom: 1px solid #e2e8f0; margin-bottom: 10px;">
            📜 错误列表可上下滚动查看 (共 ${errors.recentErrors.length} 个错误)
        </div>
    `;

    // 添加错误详情
    errors.recentErrors.forEach(error => {
        const errorItem = document.createElement('div');
        errorItem.className = 'error-item';

        const timestamp = new Date(error.timestamp).toLocaleString('zh-CN');

        errorItem.innerHTML = `
            <div class="error-title">
                [${error.type}] ${error.language}/${error.engine} - ${timestamp}
            </div>
            <div class="error-details-text">
                <strong>错误信息:</strong> ${error.error}<br>
                <strong>车辆信息:</strong> ${error.carBrand} ${error.carModel} (ID: ${error.productId})<br>
                <strong>翻译字段:</strong> ${error.fieldName}<br>
                <strong>原始文本:</strong> ${error.originalText}
            </div>
        `;

        errorList.appendChild(errorItem);
    });

    // 如果错误数量较多，自动滚动到顶部
    if (errors.recentErrors.length > 5) {
        errorList.scrollTop = 0;
    }
}



// ==================== 翻译引擎配置管理 ====================

/**
 * 加载翻译引擎配置
 */
async function loadEngineConfig() {
    try {
        console.log('加载翻译引擎配置...');

        const response = await apiRequest('/engine-config');
        const configData = response.data;

        console.log('引擎配置数据:', configData);

        // 更新当前引擎显示
        updateCurrentEngineDisplay(configData);

        // 更新引擎选择下拉框
        updateEngineSelect(configData);

        // 更新各引擎配置表单
        updateEngineConfigForms(configData);

        showMessage('引擎配置加载成功', 'success');

    } catch (error) {
        console.error('加载引擎配置失败:', error);
        showMessage(`加载引擎配置失败: ${error.message}`, 'error');
    }
}

/**
 * 更新当前引擎显示
 */
function updateCurrentEngineDisplay(configData) {
    const currentEngineDisplay = document.getElementById('currentEngineDisplay');
    if (currentEngineDisplay && configData.engines[configData.currentEngine]) {
        const engineInfo = configData.engines[configData.currentEngine];
        currentEngineDisplay.innerHTML = `
            <strong>${engineInfo.name}</strong>
            <span style="color: #4a5568;">(${configData.currentEngine})</span>
            ${engineInfo.credentials.hasCredentials ?
                '<span style="color: #48bb78;">✓ 已配置</span>' :
                '<span style="color: #f56565;">✗ 未配置</span>'
            }
        `;
    }
}

/**
 * 更新引擎选择下拉框
 */
function updateEngineSelect(configData) {
    const engineSelect = document.getElementById('engineSelect');
    if (engineSelect) {
        engineSelect.innerHTML = '';

        Object.keys(configData.engines).forEach(engineKey => {
            const engine = configData.engines[engineKey];
            const option = document.createElement('option');
            option.value = engineKey;
            option.textContent = `${engine.name} ${engine.credentials.hasCredentials ? '✓' : '✗'}`;

            if (engineKey === configData.currentEngine) {
                option.selected = true;
            }

            engineSelect.appendChild(option);
        });
    }
}

/**
 * 更新引擎配置表单
 */
function updateEngineConfigForms(configData) {
    Object.keys(configData.engines).forEach(engineKey => {
        const engine = configData.engines[engineKey];

        // 更新API URL
        const apiUrlInput = document.getElementById(`${engineKey}ApiUrl`);
        if (apiUrlInput) {
            apiUrlInput.value = engine.apiUrl || '';
        }

        // 更新超时时间
        const timeoutInput = document.getElementById(`${engineKey}Timeout`);
        if (timeoutInput) {
            timeoutInput.value = engine.timeout || 30000;
        }

        // 注意：不更新密钥字段，保持为空以保护安全
    });
}

/**
 * 显示引擎配置面板
 */
function showEngineConfig(engineName) {
    // 隐藏所有配置面板
    document.querySelectorAll('.engine-config-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // 移除所有标签的active状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // 显示选中的配置面板
    const targetPanel = document.getElementById(`${engineName}Config`);
    if (targetPanel) {
        targetPanel.classList.add('active');
    }

    // 激活对应的标签
    event.target.classList.add('active');
}

/**
 * 切换翻译引擎
 */
async function switchEngine() {
    try {
        const engineSelect = document.getElementById('engineSelect');
        if (!engineSelect) {
            showMessage('引擎选择器未找到', 'error');
            return;
        }

        const selectedEngine = engineSelect.value;
        if (!selectedEngine) {
            showMessage('请选择要切换的引擎', 'error');
            return;
        }

        showMessage('正在切换翻译引擎...', 'info');

        const response = await apiRequest('/switch-engine', {
            method: 'POST',
            body: JSON.stringify({ engineName: selectedEngine })
        });

        showMessage(response.message, 'success');

        // 重新加载配置
        await loadEngineConfig();

        // 刷新状态
        await refreshPublicStatus();

    } catch (error) {
        console.error('切换引擎失败:', error);
        showMessage(`切换引擎失败: ${error.message}`, 'error');
    }
}

/**
 * 更新引擎配置
 */
async function updateEngineConfig(engineName) {
    try {
        const config = getEngineConfigFromForm(engineName);

        if (!config) {
            showMessage('请填写必要的配置信息', 'error');
            return;
        }

        showMessage(`正在更新${engineName}引擎配置...`, 'info');

        const response = await apiRequest('/engine-config', {
            method: 'PUT',
            body: JSON.stringify({
                engineName: engineName,
                config: config
            })
        });

        showMessage(`${response.message} (已持久化保存到 .env 文件)`, 'success');

        // 重新加载配置
        await loadEngineConfig();
        await checkConfigStatus();

    } catch (error) {
        console.error('更新引擎配置失败:', error);
        showMessage(`更新引擎配置失败: ${error.message}`, 'error');
    }
}

/**
 * 从表单获取引擎配置
 */
function getEngineConfigFromForm(engineName) {
    const config = {};

    switch (engineName) {
        case 'baidu':
            const baiduAppIdElement = document.getElementById('baiduAppId');
            const baiduSecretKeyElement = document.getElementById('baiduSecretKey');
            const baiduApiUrlElement = document.getElementById('baiduApiUrl');
            const baiduTimeoutElement = document.getElementById('baiduTimeout');

            if (baiduAppIdElement && baiduAppIdElement.value.trim()) {
                config.appId = baiduAppIdElement.value.trim();
            }
            if (baiduSecretKeyElement && baiduSecretKeyElement.value.trim()) {
                config.secretKey = baiduSecretKeyElement.value.trim();
            }
            if (baiduApiUrlElement && baiduApiUrlElement.value.trim()) {
                config.apiUrl = baiduApiUrlElement.value.trim();
            }
            if (baiduTimeoutElement && baiduTimeoutElement.value) {
                config.timeout = parseInt(baiduTimeoutElement.value);
            }
            break;

        case 'deepseek':
            const deepseekApiKeyElement = document.getElementById('deepseekApiKey');
            const deepseekApiUrlElement = document.getElementById('deepseekApiUrl');
            const deepseekTimeoutElement = document.getElementById('deepseekTimeout');

            if (deepseekApiKeyElement && deepseekApiKeyElement.value.trim()) {
                config.apiKey = deepseekApiKeyElement.value.trim();
            }
            if (deepseekApiUrlElement && deepseekApiUrlElement.value.trim()) {
                config.apiUrl = deepseekApiUrlElement.value.trim();
            }
            if (deepseekTimeoutElement && deepseekTimeoutElement.value) {
                config.timeout = parseInt(deepseekTimeoutElement.value);
            }
            break;

        default:
            console.warn(`不支持的引擎类型: ${engineName}`);
            return null;
    }

    return Object.keys(config).length > 0 ? config : null;
}

/**
 * 测试引擎连接
 */
async function testEngine(engineName) {
    try {
        showMessage(`正在测试${engineName}引擎连接...`, 'info');

        const response = await apiRequest('/test-engine', {
            method: 'POST',
            body: JSON.stringify({
                engineName: engineName,
                testText: '测试翻译'
            })
        });

        // 显示测试结果
        displayTestResult(response.data);

        if (response.data.success) {
            showMessage(`${engineName}引擎测试成功`, 'success');
        } else {
            showMessage(`${engineName}引擎测试失败: ${response.data.error}`, 'error');
        }

    } catch (error) {
        console.error('测试引擎失败:', error);
        showMessage(`测试引擎失败: ${error.message}`, 'error');
    }
}

/**
 * 显示测试结果
 */
function displayTestResult(testData) {
    const testResults = document.getElementById('testResults');
    const testResultContent = document.getElementById('testResultContent');

    if (!testResults || !testResultContent) return;

    const resultHtml = `
        <div class="test-result-item ${testData.success ? 'test-result-success' : 'test-result-error'}">
            <div class="test-result-header">
                <span class="test-result-engine">${testData.engineName}</span>
                <span class="test-result-status ${testData.success ? 'success' : 'error'}">
                    ${testData.success ? '成功' : '失败'}
                </span>
            </div>
            <div class="test-result-details">
                <div class="test-result-detail">
                    <span class="test-result-label">测试文本:</span>
                    <span class="test-result-value">${testData.testText}</span>
                </div>
                <div class="test-result-detail">
                    <span class="test-result-label">响应时间:</span>
                    <span class="test-result-value">${testData.duration}</span>
                </div>
                <div class="test-result-detail">
                    <span class="test-result-label">时间戳:</span>
                    <span class="test-result-value">${new Date(testData.timestamp).toLocaleString()}</span>
                </div>
                ${testData.success ? `
                    <div class="test-result-detail">
                        <span class="test-result-label">翻译结果:</span>
                        <span class="test-result-value">${testData.translatedText}</span>
                    </div>
                ` : `
                    <div class="test-result-detail">
                        <span class="test-result-label">错误信息:</span>
                        <span class="test-result-value" style="color: #e53e3e;">${testData.error}</span>
                    </div>
                `}
            </div>
        </div>
    `;

    testResultContent.innerHTML = resultHtml;
    testResults.style.display = 'block';

    // 滚动到测试结果
    testResults.scrollIntoView({ behavior: 'smooth' });
}

// ==================== 配置管理功能 ====================

/**
 * 检查配置状态
 */
async function checkConfigStatus() {
    try {
        const response = await apiRequest('/config-status');
        const statusData = response.data;

        const configStatusText = document.getElementById('configStatusText');
        if (configStatusText) {
            if (statusData.validation.valid) {
                configStatusText.innerHTML = '<span style="color: #48bb78;">✓ 配置文件正常</span>';
            } else {
                configStatusText.innerHTML = `<span style="color: #f56565;">✗ 配置文件有问题: ${statusData.validation.errors.join(', ')}</span>`;
            }
        }

    } catch (error) {
        console.error('检查配置状态失败:', error);
        const configStatusText = document.getElementById('configStatusText');
        if (configStatusText) {
            configStatusText.innerHTML = '<span style="color: #f56565;">✗ 无法检查配置状态</span>';
        }
    }
}

/**
 * 创建配置备份
 */
async function createConfigBackup() {
    try {
        showMessage('正在创建配置备份...', 'info');

        const response = await apiRequest('/backup-config', {
            method: 'POST'
        });

        showMessage('配置备份创建成功', 'success');

    } catch (error) {
        console.error('创建配置备份失败:', error);
        showMessage(`创建配置备份失败: ${error.message}`, 'error');
    }
}

/**
 * 恢复配置备份
 */
async function restoreConfigBackup() {
    if (!confirm('确定要恢复配置备份吗？这将覆盖当前的所有配置设置。')) {
        return;
    }

    try {
        showMessage('正在恢复配置备份...', 'info');

        const response = await apiRequest('/restore-config', {
            method: 'POST'
        });

        showMessage('配置备份恢复成功，系统已重新初始化', 'success');

        // 重新加载页面数据
        await loadEngineConfig();
        await checkConfigStatus();
        await refreshPublicStatus();

    } catch (error) {
        console.error('恢复配置备份失败:', error);
        showMessage(`恢复配置备份失败: ${error.message}`, 'error');
    }
}

/**
 * 重新加载配置
 */
async function reloadConfig() {
    try {
        showMessage('正在重新加载配置...', 'info');

        const response = await apiRequest('/reload-config', {
            method: 'POST'
        });

        showMessage('配置重新加载成功', 'success');

        // 重新加载页面数据
        await loadEngineConfig();
        await checkConfigStatus();
        await refreshPublicStatus();

    } catch (error) {
        console.error('重新加载配置失败:', error);
        showMessage(`重新加载配置失败: ${error.message}`, 'error');
    }
}

// ==================== 引擎健康状态功能 ====================

/**
 * 加载引擎健康状态
 */
async function loadEngineHealth() {
    try {
        const response = await apiRequest('/engine-health');
        const healthData = response.data;

        // 更新系统健康状态显示
        updateSystemHealthDisplay(healthData.system);

        // 更新引擎健康状态详情
        updateEngineHealthDetails(healthData.engines);

    } catch (error) {
        console.error('加载引擎健康状态失败:', error);
        const systemHealthDisplay = document.getElementById('systemHealthDisplay');
        if (systemHealthDisplay) {
            systemHealthDisplay.innerHTML = '<span class="system-health critical">❌ 无法获取状态</span>';
        }
    }
}

/**
 * 刷新引擎健康状态
 */
async function refreshEngineHealth() {
    try {
        showMessage('正在刷新引擎健康状态...', 'info');

        const response = await apiRequest('/refresh-engine-health', {
            method: 'POST'
        });

        // 重新加载健康状态
        await loadEngineHealth();

        showMessage('引擎健康状态已刷新', 'success');

    } catch (error) {
        console.error('刷新引擎健康状态失败:', error);
        showMessage(`刷新引擎健康状态失败: ${error.message}`, 'error');
    }
}

/**
 * 更新系统健康状态显示
 */
function updateSystemHealthDisplay(systemData) {
    const systemHealthDisplay = document.getElementById('systemHealthDisplay');
    if (!systemHealthDisplay) return;

    let statusIcon, statusText, statusClass;

    switch (systemData.overallHealth) {
        case 'healthy':
            statusIcon = '✅';
            statusText = '所有引擎正常';
            statusClass = 'healthy';
            break;
        case 'partial':
            statusIcon = '⚠️';
            statusText = `部分引擎异常 (${systemData.healthyEngines}/${systemData.totalEngines} 正常)`;
            statusClass = 'partial';
            break;
        case 'critical':
            statusIcon = '❌';
            statusText = '所有引擎异常';
            statusClass = 'critical';
            break;
        default:
            statusIcon = '❓';
            statusText = '状态未知';
            statusClass = 'critical';
    }

    systemHealthDisplay.innerHTML = `<span class="system-health ${statusClass}">${statusIcon} ${statusText}</span>`;
}

/**
 * 更新引擎健康状态详情
 */
function updateEngineHealthDetails(enginesData) {
    const healthGrid = document.getElementById('healthGrid');
    if (!healthGrid) return;

    healthGrid.innerHTML = '';

    Object.keys(enginesData).forEach(engineName => {
        const engine = enginesData[engineName];
        const healthItem = document.createElement('div');
        healthItem.className = `health-item ${engine.healthy ? 'healthy' : 'unhealthy'}`;

        const statusIcon = engine.healthy ? '✅' : '❌';
        const statusText = engine.healthy ? '正常' : '异常';
        const statusClass = engine.healthy ? 'healthy' : 'unhealthy';

        healthItem.innerHTML = `
            <div class="health-label">${engine.name} (${engineName})</div>
            <div class="health-status ${statusClass}">
                ${statusIcon} ${statusText}
                ${engine.responseTime ? `(${engine.responseTime}ms)` : ''}
            </div>
            <div class="health-details">
                <div>最后检查: ${new Date(engine.lastTest).toLocaleString()}</div>
                ${engine.error ? `<div style="color: #dc3545;">错误: ${engine.error}</div>` : ''}
            </div>
        `;

        healthGrid.appendChild(healthItem);
    });
}

// 删除定时刷新功能，只保留手动刷新
