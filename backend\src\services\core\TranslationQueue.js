/**
 * 翻译队列系统
 * 简化的队列管理，支持优先级和并发控制
 */

const EventEmitter = require('events');

class TranslationQueue extends EventEmitter {
  constructor(config, logger, monitor = null) {
    super();

    this.config = config;
    this.logger = logger;
    this.monitor = monitor;
    
    // 队列存储
    this.queues = {
      high: [],
      normal: [],
      low: []
    };
    
    // 处理中的任务
    this.processing = new Map();
    
    // 队列状态
    this.isRunning = false;
    this.processingInterval = null;
    this.cleanupInterval = null;
    
    // 统计信息
    this.stats = {
      totalQueued: 0,
      totalProcessed: 0,
      totalSucceeded: 0,
      totalFailed: 0,
      currentProcessing: 0,
      currentQueueSize: 0
    };
  }

  /**
   * 初始化队列
   */
  async initialize() {
    this.logger.info('初始化翻译队列...');
    this.updateQueueSize();
    this.logger.info('翻译队列初始化完成');
  }

  /**
   * 启动队列处理器
   */
  async start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.startProcessor();
    this.startCleanup();
    
    this.logger.milestone('翻译队列处理器已启动');
  }

  /**
   * 停止队列处理器
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // 等待当前处理中的任务完成
    await this.waitForProcessingTasks();
    
    this.logger.milestone('翻译队列处理器已停止');
  }

  /**
   * 添加任务到队列
   * @param {Object} task - 翻译任务
   * @param {string} priority - 优先级
   * @returns {string} - 任务ID
   */
  addTask(task, priority = 'normal') {
    // 验证优先级
    if (!this.config.priorities.includes(priority)) {
      priority = this.config.defaultPriority;
    }

    // 检查队列大小限制
    if (this.getTotalQueueSize() >= this.config.maxQueueSize) {
      throw new Error('队列已满，无法添加新任务');
    }

    // 生成任务ID
    const taskId = this.generateTaskId();
    
    // 创建队列任务
    const queueTask = {
      id: taskId,
      ...task,
      priority,
      addedAt: new Date(),
      retryCount: 0,
      status: 'queued'
    };

    // 验证任务
    this.validateTask(queueTask);

    // 添加到对应优先级队列
    this.queues[priority].push(queueTask);
    this.stats.totalQueued++;
    this.updateQueueSize();

    this.logger.debug(`任务已添加到队列: ${taskId}`, { priority, entityType: task.entityType });
    this.emit('taskAdded', queueTask);

    return taskId;
  }

  /**
   * 获取下一个待处理任务
   */
  getNextTask() {
    // 按优先级顺序获取任务
    for (const priority of this.config.priorities) {
      const queue = this.queues[priority];
      if (queue.length > 0) {
        const task = queue.shift();
        this.updateQueueSize();
        return task;
      }
    }
    return null;
  }

  /**
   * 处理单个任务
   * @param {Object} task - 翻译任务
   */
  async processTask(task) {
    const { id, entityType, entityId, languages, operation } = task;

    try {
      // 更新任务状态
      task.status = 'processing';
      task.startedAt = new Date();
      this.processing.set(id, task);
      this.stats.currentProcessing++;

      this.logger.debug(`开始处理任务: ${id}`, { entityType, entityId, operation });
      this.emit('taskStarted', task);

      // 执行翻译操作
      let result;
      switch (operation) {
        case 'translateProduct':
          result = await this.executeProductTranslation(entityId, languages);
          break;
        case 'translateConfigCar':
          result = await this.executeConfigCarTranslation(entityId, languages);
          break;
        case 'retranslate':
          result = await this.executeRetranslation(entityType, entityId, languages);
          break;
        default:
          throw new Error(`不支持的操作: ${operation}`);
      }

      // 任务成功完成
      task.status = 'completed';
      task.completedAt = new Date();
      task.result = result;

      this.stats.totalSucceeded++;
      this.logger.debug(`任务完成: ${id}`);
      this.emit('taskCompleted', task);

      return { success: true, result };

    } catch (error) {
      this.logger.error(`任务失败: ${id}`, error);

      // 检查是否是翻译系统停止错误
      if (error.message === 'TRANSLATION_SYSTEM_STOPPED') {
        this.logger.error('翻译系统已停止，任务重新进入队列等待重启');

        // 将任务重新设置为待处理状态
        task.status = 'pending';
        task.error = null;
        task.retryCount = 0;

        // 停止队列处理
        this.isProcessing = false;

        return { success: false, error: 'Translation system stopped', requeue: true };
      }

      // 记录翻译错误到监控系统
      if (this.monitor) {
        this.monitor.recordTranslationError(error.message, {
          taskId: id,
          entityType: task.entityType,
          entityId: task.entityId,
          languages: task.languages
        });
      }

      // 更新任务状态
      task.status = 'failed';
      task.error = error.message;
      task.failedAt = new Date();

      // 检查是否需要重试
      if (task.retryCount < this.config.maxRetries) {
        await this.scheduleRetry(task);
      } else {
        this.stats.totalFailed++;
        this.emit('taskFailed', task);
      }

      return { success: false, error: error.message };

    } finally {
      // 清理处理中的任务
      this.processing.delete(id);
      this.stats.currentProcessing--;
      this.stats.totalProcessed++;
    }
  }

  /**
   * 调度重试任务
   * @param {Object} task - 失败的任务
   */
  async scheduleRetry(task) {
    task.retryCount++;
    task.status = 'retrying';
    
    const delay = this.calculateRetryDelay(task.retryCount);
    
    this.logger.info(`任务将在 ${delay}ms 后重试: ${task.id} (第${task.retryCount}次重试)`);

    setTimeout(() => {
      task.status = 'queued';
      this.queues[task.priority].push(task);
      this.updateQueueSize();
      
      this.emit('taskRetried', task);
    }, delay);
  }

  /**
   * 计算重试延迟
   * @param {number} retryCount - 重试次数
   */
  calculateRetryDelay(retryCount) {
    const baseDelay = this.config.retryDelay;
    const multiplier = this.config.retryBackoffMultiplier;
    return Math.min(baseDelay * Math.pow(multiplier, retryCount - 1), 60000); // 最大60秒
  }

  /**
   * 启动任务处理器
   */
  startProcessor() {
    this.processingInterval = setInterval(async () => {
      try {
        // 检查是否可以处理更多任务
        if (this.stats.currentProcessing >= this.config.maxConcurrentTasks) {
          return;
        }

        // 获取下一个任务
        const task = this.getNextTask();
        if (!task) {
          return;
        }

        // 异步处理任务
        this.processTask(task).catch(error => {
          this.logger.error('处理任务时出错', error);
        });

      } catch (error) {
        this.logger.error('处理器运行时出错', error);
      }
    }, this.config.processingInterval);
  }

  /**
   * 启动清理器
   */
  startCleanup() {
    this.cleanupInterval = setInterval(() => {
      this.cleanupCompletedTasks();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompletedTasks() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
    
    for (const [taskId, task] of this.processing.entries()) {
      if (task.completedAt && task.completedAt.getTime() < cutoffTime) {
        this.processing.delete(taskId);
      }
    }
  }

  /**
   * 等待处理中的任务完成
   */
  async waitForProcessingTasks(timeout = 30000) {
    const startTime = Date.now();
    
    while (this.processing.size > 0 && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (this.processing.size > 0) {
      this.logger.warn(`仍有 ${this.processing.size} 个任务在处理中，强制停止`);
    }
  }

  /**
   * 验证任务
   * @param {Object} task - 任务对象
   */
  validateTask(task) {
    const required = ['entityType', 'entityId', 'languages', 'operation'];
    
    for (const field of required) {
      if (!task[field]) {
        throw new Error(`任务缺少必要字段: ${field}`);
      }
    }

    if (!Array.isArray(task.languages) || task.languages.length === 0) {
      throw new Error('任务必须包含至少一种目标语言');
    }
  }

  /**
   * 生成任务ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新队列大小统计
   */
  updateQueueSize() {
    this.stats.currentQueueSize = this.getTotalQueueSize();
  }

  /**
   * 获取总队列大小
   */
  getTotalQueueSize() {
    return Object.values(this.queues).reduce((total, queue) => total + queue.length, 0);
  }

  /**
   * 获取队列统计信息
   */
  getStats() {
    return {
      ...this.stats,
      queueSizes: {
        high: this.queues.high.length,
        normal: this.queues.normal.length,
        low: this.queues.low.length
      }
    };
  }

  /**
   * 获取健康状态
   */
  getHealthStatus() {
    return {
      isRunning: this.isRunning,
      totalQueueSize: this.getTotalQueueSize(),
      processingTasks: this.stats.currentProcessing,
      maxConcurrentTasks: this.config.maxConcurrentTasks
    };
  }

  /**
   * 执行产品翻译
   * @param {string} entityId - 产品ID
   * @param {Array} languages - 语言列表
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeProductTranslation(entityId, languages) {
    const TranslationState = require('./TranslationState');
    const TranslationEngines = require('./TranslationEngines');
    const { Product, ProductEn, ProductRu, ProductEs, ProductFr } = require('../../models');

    try {
      // 获取产品信息
      const product = await Product.findOne({ where: { productId: entityId } });
      if (!product) {
        throw new Error(`产品不存在: ${entityId}`);
      }

      const state = new TranslationState(this.logger);
      const engines = new TranslationEngines(this.logger);

      const results = {};

      // 并行翻译所有语言
      const translationPromises = languages.map(async (language) => {
        try {
          await state.updateLanguageStatus('product', entityId, language, 'in_progress');

          // 获取需要翻译的字段
          const fieldsToTranslate = this.getProductTranslatableFields(product);
          const translatedFields = {};

          // 翻译每个字段
          for (const [field, value] of Object.entries(fieldsToTranslate)) {
            if (value && value.trim()) {
              const result = await engines.translate(value, 'zh', language, { productId: entityId });
              if (result.success) {
                translatedFields[field] = result.translatedText;
              } else {
                throw new Error(`字段 ${field} 翻译失败: ${result.error}`);
              }
            }
          }

          // 保存翻译结果
          await this.saveProductTranslation(entityId, language, translatedFields);

          await state.updateLanguageStatus('product', entityId, language, 'completed');
          results[language] = { success: true, fields: Object.keys(translatedFields) };

        } catch (error) {
          await state.updateLanguageStatus('product', entityId, language, 'failed', error.message);
          results[language] = { success: false, error: error.message };
          throw error;
        }
      });

      await Promise.all(translationPromises);

      this.logger.info(`产品翻译完成: ${entityId}`, { languages, results });
      return { success: true, results };

    } catch (error) {
      this.logger.error(`产品翻译失败: ${entityId}`, error);

      // 记录翻译错误到监控系统
      if (this.monitor) {
        this.monitor.recordTranslationError(error.message, {
          entityType: 'product',
          entityId: entityId,
          languages: languages
        });
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * 执行配置车翻译
   * @param {number} entityId - 配置车ID
   * @param {Array} languages - 语言列表
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeConfigCarTranslation(entityId, languages) {
    const TranslationState = require('./TranslationState');
    const TranslationEngines = require('./TranslationEngines');
    const { ConfigCar, ConfigCarEn, ConfigCarRu, ConfigCarEs, ConfigCarFr } = require('../../models');

    try {
      // 获取配置车信息
      const configCar = await ConfigCar.findOne({ where: { id: entityId } });
      if (!configCar) {
        throw new Error(`配置车不存在: ${entityId}`);
      }

      const state = new TranslationState(this.logger);
      const engines = new TranslationEngines(this.logger);

      const results = {};

      // 并行翻译所有语言
      const translationPromises = languages.map(async (language) => {
        try {
          await state.updateLanguageStatus('configCar', entityId, language, 'in_progress');

          // 获取需要翻译的字段
          const fieldsToTranslate = this.getConfigCarTranslatableFields(configCar);
          const translatedFields = {};

          // 翻译每个字段
          let fieldIndex = 0;
          for (const [field, value] of Object.entries(fieldsToTranslate)) {
            if (value && value.trim()) {
              const result = await engines.translate(value, 'zh', language, {
                productId: `config-${entityId}`,
                isFirstField: fieldIndex === 0
              });
              if (result.success) {
                translatedFields[field] = result.translatedText;
              } else {
                throw new Error(`字段 ${field} 翻译失败: ${result.error}`);
              }
            }
            fieldIndex++;
          }

          // 保存翻译结果
          await this.saveConfigCarTranslation(entityId, language, translatedFields);

          await state.updateLanguageStatus('configCar', entityId, language, 'completed');
          results[language] = { success: true, fields: Object.keys(translatedFields) };

          // 只在第一个语言翻译时显示成功日志
          if (languages.indexOf(language) === 0) {
            console.log(`配置 ${entityId} 翻译成功`);
          }

        } catch (error) {
          await state.updateLanguageStatus('configCar', entityId, language, 'failed', error.message);
          results[language] = { success: false, error: error.message };
          throw error;
        }
      });

      await Promise.all(translationPromises);

      this.logger.info(`配置车翻译完成: ${entityId}`, { languages, results });
      return { success: true, results };

    } catch (error) {
      this.logger.error(`配置车翻译失败: ${entityId}`, error);

      // 记录翻译错误到监控系统
      if (this.monitor) {
        this.monitor.recordTranslationError(error.message, {
          entityType: 'configCar',
          entityId: entityId,
          languages: languages
        });
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * 执行重新翻译
   * @param {string} entityType - 实体类型
   * @param {string|number} entityId - 实体ID
   * @param {Array} languages - 语言列表
   * @returns {Promise<Object>} - 翻译结果
   */
  async executeRetranslation(entityType, entityId, languages) {
    const TranslationState = require('./TranslationState');

    try {
      // 重置翻译状态
      const state = new TranslationState(this.logger);
      await state.resetStatus(entityType, entityId, languages);

      // 根据实体类型执行相应的翻译
      if (entityType === 'product') {
        return await this.executeProductTranslation(entityId, languages);
      } else if (entityType === 'configCar') {
        return await this.executeConfigCarTranslation(entityId, languages);
      } else {
        throw new Error(`不支持的实体类型: ${entityType}`);
      }

    } catch (error) {
      this.logger.error(`重新翻译失败: ${entityType}:${entityId}`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取产品可翻译字段
   * @param {Object} product - 产品对象
   * @returns {Object} - 可翻译字段
   */
  getProductTranslatableFields(product) {
    const fields = {};

    // 基本信息字段
    if (product.title) fields.title = product.title;
    if (product.description) fields.description = product.description;
    if (product.features) fields.features = product.features;
    if (product.specifications) fields.specifications = product.specifications;

    // 其他可翻译字段
    if (product.brand) fields.brand = product.brand;
    if (product.model) fields.model = product.model;
    if (product.category) fields.category = product.category;

    return fields;
  }

  /**
   * 获取配置车可翻译字段
   * @param {Object} configCar - 配置车对象
   * @returns {Object} - 可翻译字段
   */
  getConfigCarTranslatableFields(configCar) {
    const fields = {};

    // 基本信息字段
    if (configCar.brand) fields.brand = configCar.brand;
    if (configCar.model) fields.model = configCar.model;
    if (configCar.series) fields.series = configCar.series;
    if (configCar.trim) fields.trim = configCar.trim;
    if (configCar.description) fields.description = configCar.description;

    // 技术规格字段
    if (configCar.engineType) fields.engineType = configCar.engineType;
    if (configCar.transmission) fields.transmission = configCar.transmission;
    if (configCar.fuelType) fields.fuelType = configCar.fuelType;
    if (configCar.driveType) fields.driveType = configCar.driveType;

    return fields;
  }

  /**
   * 保存产品翻译结果
   * @param {string} productId - 产品ID
   * @param {string} language - 语言代码
   * @param {Object} translatedFields - 翻译后的字段
   */
  async saveProductTranslation(productId, language, translatedFields) {
    const { Product, ProductEn, ProductRu, ProductEs, ProductFr } = require('../../models');

    const ModelMap = {
      en: ProductEn,
      ru: ProductRu,
      es: ProductEs,
      fr: ProductFr
    };

    const Model = ModelMap[language];
    if (!Model) {
      throw new Error(`不支持的语言: ${language}`);
    }

    // 获取原始产品信息，特别是modelId
    const originalProduct = await Product.findOne({
      where: { productId },
      attributes: ['modelId', 'brand', 'title']
    });

    if (!originalProduct) {
      throw new Error(`产品不存在: ${productId}`);
    }

    // 使用 upsert 操作，包含必需的字段
    await Model.upsert({
      productId,
      modelId: originalProduct.modelId,
      ...translatedFields,
      updatedAt: new Date()
    });
  }

  /**
   * 保存配置车翻译结果
   * @param {number} configCarId - 配置车ID
   * @param {string} language - 语言代码
   * @param {Object} translatedFields - 翻译后的字段
   */
  async saveConfigCarTranslation(configCarId, language, translatedFields) {
    const { ConfigCarEn, ConfigCarRu, ConfigCarEs, ConfigCarFr } = require('../../models');

    const ModelMap = {
      en: ConfigCarEn,
      ru: ConfigCarRu,
      es: ConfigCarEs,
      fr: ConfigCarFr
    };

    const Model = ModelMap[language];
    if (!Model) {
      throw new Error(`不支持的语言: ${language}`);
    }

    // 使用 upsert 操作
    await Model.upsert({
      id: configCarId,
      ...translatedFields,
      updatedAt: new Date()
    });
  }
}

module.exports = TranslationQueue;
